<?php
$page_title = "Инвестирование";

// Проверка авторизации
if (!isLoggedIn()) {
    redirect('index.php?page=login&redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

$user = getCurrentUser();
$user_balance = $user['balance'];

// Получение параметров фильтрации
$filter_category = isset($_GET['category']) ? $_GET['category'] : '';
$filter_risk = isset($_GET['risk']) ? $_GET['risk'] : '';
$filter_type = isset($_GET['type']) ? $_GET['type'] : '';
$sort_by = isset($_GET['sort']) ? $_GET['sort'] : 'sort_order';
$sort_order = isset($_GET['order']) ? $_GET['order'] : 'ASC';

// Получение доступных пакетов с фильтрацией
$packages = getInvestmentPackagesFiltered($filter_category, $filter_risk, $filter_type, $sort_by, $sort_order);
$categories = getPackageCategories();

// Получение параметров из URL для автоматического открытия модального окна
$auto_open_package_id = isset($_GET['package_id']) ? intval($_GET['package_id']) : 0;
$auto_open_package_type = isset($_GET['package_type']) ? $_GET['package_type'] : '';

// Обработка инвестирования
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['invest'])) {
    $result = processInvestment($_POST, $user['id']);
    if ($result['success']) {
        redirect('index.php?page=dashboard', $result['message'], 'success');
    } else {
        $error_message = $result['message'];
    }
}
?>

<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="page-header mb-4">
                <h2 class="page-title">
                    <i class="fas fa-chart-line text-primary"></i> Инвестирование
                </h2>
                <p class="page-subtitle">Выберите подходящий инвестиционный пакет</p>
            </div>
        </div>
    </div>
    
    <!-- Баланс пользователя -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="balance-card animate-fadeInUp">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="card-title mb-1">
                                <i class="fas fa-wallet"></i> Доступный баланс
                            </h5>
                            <h3 class="mb-0 user-balance animate-glow"><?php echo formatMoney($user_balance); ?></h3>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <a href="index.php?page=deposit" class="action-btn">
                                <i class="fas fa-plus"></i> Пополнить баланс
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Фильтры и сортировка -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter"></i> Фильтры и сортировка
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="index.php" id="filterForm">
                        <input type="hidden" name="page" value="invest">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="category" class="form-label">Категория</label>
                                <select class="form-select" name="category" id="category">
                                    <option value="">Все категории</option>
                                    <option value="eco_basic" <?php echo $filter_category === 'eco_basic' ? 'selected' : ''; ?>>Эко-проекты базовые</option>
                                    <option value="eco_premium" <?php echo $filter_category === 'eco_premium' ? 'selected' : ''; ?>>Эко-проекты премиум</option>
                                    <option value="eco_vip" <?php echo $filter_category === 'eco_vip' ? 'selected' : ''; ?>>Эко-проекты VIP</option>
                                    <option value="solar_energy" <?php echo $filter_category === 'solar_energy' ? 'selected' : ''; ?>>Солнечная энергия</option>
                                    <option value="wind_energy" <?php echo $filter_category === 'wind_energy' ? 'selected' : ''; ?>>Ветровая энергия</option>
                                    <option value="mining_premium" <?php echo $filter_category === 'mining_premium' ? 'selected' : ''; ?>>Зеленый майнинг</option>
                                    <option value="green_tech" <?php echo $filter_category === 'green_tech' ? 'selected' : ''; ?>>Зеленые технологии</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="risk" class="form-label">Риск</label>
                                <select class="form-select" name="risk" id="risk">
                                    <option value="">Любой</option>
                                    <option value="low" <?php echo $filter_risk === 'low' ? 'selected' : ''; ?>>Низкий</option>
                                    <option value="medium" <?php echo $filter_risk === 'medium' ? 'selected' : ''; ?>>Средний</option>
                                    <option value="high" <?php echo $filter_risk === 'high' ? 'selected' : ''; ?>>Высокий</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="type" class="form-label">Тип</label>
                                <select class="form-select" name="type" id="type">
                                    <option value="">Любой</option>
                                    <option value="flexible" <?php echo $filter_type === 'flexible' ? 'selected' : ''; ?>>Гибкий</option>
                                    <option value="fixed" <?php echo $filter_type === 'fixed' ? 'selected' : ''; ?>>Фиксированный</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="sort" class="form-label">Сортировка</label>
                                <select class="form-select" name="sort" id="sort">
                                    <option value="sort_order" <?php echo $sort_by === 'sort_order' ? 'selected' : ''; ?>>По умолчанию</option>
                                    <option value="daily_rate" <?php echo $sort_by === 'daily_rate' ? 'selected' : ''; ?>>По доходности</option>
                                    <option value="min_amount" <?php echo $sort_by === 'min_amount' ? 'selected' : ''; ?>>По минимальной сумме</option>
                                    <option value="popularity_score" <?php echo $sort_by === 'popularity_score' ? 'selected' : ''; ?>>По популярности</option>
                                    <option value="total_invested" <?php echo $sort_by === 'total_invested' ? 'selected' : ''; ?>>По объему инвестиций</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="order" class="form-label">Порядок</label>
                                <select class="form-select" name="order" id="order">
                                    <option value="ASC" <?php echo $sort_order === 'ASC' ? 'selected' : ''; ?>>По возрастанию</option>
                                    <option value="DESC" <?php echo $sort_order === 'DESC' ? 'selected' : ''; ?>>По убыванию</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Применить фильтры
                                </button>
                                <a href="index.php?page=invest" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Сбросить
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>

    <!-- Инвестиционные пакеты -->
    <div class="row">
        <?php if (empty($packages)): ?>
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle fa-2x mb-3"></i>
                    <h5>Пакеты не найдены</h5>
                    <p>По выбранным фильтрам пакеты не найдены. Попробуйте изменить критерии поиска.</p>
                    <a href="index.php?page=invest" class="btn btn-primary">Сбросить фильтры</a>
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($packages as $package): ?>
                <div class="col-lg-6 mb-4">
                    <div class="card package-card h-100 <?php echo $package['is_featured'] ? 'featured-package' : ''; ?> <?php echo $package['color_scheme']; ?>-theme">
                        <!-- Бейджи -->
                        <div class="package-badges">
                            <?php if ($package['is_featured']): ?>
                                <div class="package-badge featured">
                                    <i class="fas fa-star"></i> Рекомендуем
                                </div>
                            <?php endif; ?>
                            <?php if ($package['is_limited']): ?>
                                <div class="package-badge limited">
                                    <i class="fas fa-clock"></i> Ограничено
                                </div>
                            <?php endif; ?>
                            <div class="package-badge risk-<?php echo $package['risk_level']; ?>">
                                <?php echo ucfirst($package['risk_level']); ?> риск
                            </div>
                        </div>

                        <div class="card-header text-center">
                            <div class="package-icon">
                                <i class="<?php echo $package['icon']; ?>"></i>
                            </div>
                            <h4 class="package-title">
                                <?php echo htmlspecialchars($package['name']); ?>
                            </h4>
                            <div class="package-rate">
                                <?php echo formatPercent($package['daily_rate']); ?> в день
                            </div>
                            <?php if ($package['bonus_rate'] > 0): ?>
                                <div class="package-bonus">
                                    + <?php echo formatPercent($package['bonus_rate']); ?> бонус
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="card-body">
                            <!-- Основная информация -->
                            <div class="package-info mb-3">
                                <div class="info-item">
                                    <i class="fas fa-dollar-sign text-success"></i>
                                    <span>Минимум: <?php echo formatMoney($package['min_amount']); ?></span>
                                </div>
                                <?php if ($package['max_amount']): ?>
                                    <div class="info-item">
                                        <i class="fas fa-dollar-sign text-warning"></i>
                                        <span>Максимум: <?php echo formatMoney($package['max_amount']); ?></span>
                                    </div>
                                <?php endif; ?>
                                <?php if ($package['duration_days']): ?>
                                    <div class="info-item">
                                        <i class="fas fa-calendar text-info"></i>
                                        <span>Срок: <?php echo $package['duration_days']; ?> дней</span>
                                    </div>
                                <?php endif; ?>
                                <?php if ($package['total_investors'] > 0): ?>
                                    <div class="info-item">
                                        <i class="fas fa-users text-primary"></i>
                                        <span>Инвесторов: <?php echo number_format($package['total_investors']); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Теги -->
                            <?php if (!empty($package['tags'])): ?>
                                <div class="package-tags mb-3">
                                    <?php
                                    $tags = json_decode($package['tags'], true);
                                    if ($tags):
                                        foreach ($tags as $tag):
                                    ?>
                                        <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                                    <?php
                                        endforeach;
                                    endif;
                                    ?>
                                </div>
                            <?php endif; ?>

                            <!-- Преимущества -->
                            <div class="package-benefits mb-3">
                                <?php
                                $benefits = json_decode($package['benefits'], true);
                                if ($benefits):
                                    foreach (array_slice($benefits, 0, 4) as $benefit):
                                ?>
                                    <div class="benefit">
                                        <i class="fas fa-check text-success"></i>
                                        <?php echo htmlspecialchars($benefit); ?>
                                    </div>
                                <?php
                                    endforeach;
                                endif;
                                ?>
                            </div>

                            <!-- Экологический эффект -->
                            <?php if (!empty($package['environmental_impact'])): ?>
                                <div class="environmental-impact mb-3">
                                    <h6><i class="fas fa-leaf text-success"></i> Экологический эффект:</h6>
                                    <p class="small"><?php echo htmlspecialchars($package['environmental_impact']); ?></p>
                                    <?php if ($package['carbon_offset'] > 0): ?>
                                        <div class="carbon-offset">
                                            <i class="fas fa-seedling"></i>
                                            Компенсация CO₂: <?php echo number_format($package['carbon_offset'], 1); ?> тонн/год
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        
                        <div class="package-example">
                            <h6>Пример доходности:</h6>
                            <div class="example-calc" data-rate="<?php echo $package['daily_rate']; ?>">
                                <div class="calc-row">
                                    <span>Инвестиция: $1,000</span>
                                    <span>$1,000</span>
                                </div>
                                <div class="calc-row">
                                    <span>Ежедневно:</span>
                                    <span class="daily-profit">$<?php echo number_format(1000 * ($package['daily_rate'] / 100), 2); ?></span>
                                </div>
                                <div class="calc-row">
                                    <span>В месяц:</span>
                                    <span class="monthly-profit">$<?php echo number_format(1000 * ($package['daily_rate'] / 100) * 30, 2); ?></span>
                                </div>
                                <div class="calc-row total">
                                    <span>В год:</span>
                                    <span class="yearly-profit">$<?php echo number_format(1000 * ($package['daily_rate'] / 100) * 365, 2); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                            <div class="card-footer text-center">
                                <?php if ($package['is_limited'] && $package['used_slots'] >= $package['limited_slots']): ?>
                                    <button type="button" class="btn btn-secondary btn-lg w-100" disabled>
                                        <i class="fas fa-lock"></i> Места закончились
                                    </button>
                                <?php else: ?>
                                    <button type="button"
                                            class="btn btn-primary btn-lg w-100 invest-btn"
                                            data-package-id="<?php echo $package['id']; ?>"
                                            data-package-name="<?php echo htmlspecialchars($package['name']); ?>"
                                            data-package-type="<?php echo $package['type']; ?>"
                                            data-min-amount="<?php echo $package['min_amount']; ?>"
                                            data-max-amount="<?php echo $package['max_amount']; ?>"
                                            data-daily-rate="<?php echo $package['daily_rate']; ?>">
                                        <i class="fas fa-chart-line"></i> Инвестировать
                                    </button>
                                <?php endif; ?>

                                <div class="package-stats mt-2">
                                    <?php if ($package['total_invested'] > 0): ?>
                                        <small class="text-muted">
                                            Всего инвестировано: <?php echo formatMoney($package['total_invested']); ?>
                                        </small>
                                    <?php endif; ?>
                                    <?php if ($package['is_limited']): ?>
                                        <br><small class="text-warning">
                                            Осталось мест: <?php echo ($package['limited_slots'] - $package['used_slots']); ?> из <?php echo $package['limited_slots']; ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <!-- Статистика инвестиций -->
    <div class="row mt-5">
        <div class="col-12">
            <h4 class="mb-3">
                <i class="fas fa-chart-bar text-primary"></i> Ваша статистика
            </h4>
        </div>
        
        <?php
        $user_stats = getUserInvestmentStats($user['id']);
        ?>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-value"><?php echo formatMoney($user_stats['total_invested']); ?></div>
                <div class="stats-label">Всего инвестировано</div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="stats-value"><?php echo formatMoney($user_stats['total_profit']); ?></div>
                <div class="stats-label">Общая прибыль</div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-play-circle"></i>
                </div>
                <div class="stats-value"><?php echo $user_stats['active_investments']; ?></div>
                <div class="stats-label">Активных инвестиций</div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="stats-value"><?php echo formatPercent($user_stats['avg_daily_rate']); ?></div>
                <div class="stats-label">Средняя доходность</div>
            </div>
        </div>
    </div>
</div>

<!-- УДАЛЕНО: Старое модальное окно инвестирования -->

<style>
.package-card {
    position: relative;
    transition: all 0.3s ease;
}

.package-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.package-badge {
    position: absolute;
    top: -10px;
    right: 20px;
    background: var(--warning-color);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 1;
}

.package-rate {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.info-item, .feature {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.info-item i, .feature i {
    margin-right: 0.5rem;
    width: 16px;
}

.example-calc .calc-row {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.example-calc .calc-row.total {
    border-bottom: none;
    font-weight: 600;
    color: var(--success-color);
}

.amount-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.profit-calculator {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
}

.calc-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.calc-item {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
}

.calc-item .value {
    font-weight: 600;
    color: var(--success-color);
}

.balance-info {
    text-align: center;
    padding: 1rem;
    background: var(--light-color);
    border-radius: 0.5rem;
}

.balance-amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.info-grid {
    display: grid;
    gap: 0.5rem;
}

.info-grid .info-item {
    display: flex;
    justify-content: space-between;
}

.info-grid .label {
    color: #6c757d;
}

.info-grid .value {
    font-weight: 600;
}

@media (max-width: 768px) {
    .amount-buttons {
        justify-content: center;
    }
    
    .calc-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<!-- УДАЛЕНО: Старые JavaScript функции инвестирования -->

<?php
// УДАЛЕНО: Старая функция getInvestmentPackages

// УДАЛЕНО: Старая функция getUserInvestmentStats

// УДАЛЕНО: Старая функция processInvestment

// УДАЛЕНО: Старая функция processReferralBonus
?>

<!-- Модальное окно инвестирования -->
<div class="modal fade" id="investModal" tabindex="-1" aria-labelledby="investModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="background: linear-gradient(135deg, var(--dark-green), var(--dark-navy)) !important; border: 1px solid var(--gold-primary) !important;">
            <div class="modal-header" style="border-bottom: 1px solid var(--gold-primary) !important;">
                <h5 class="modal-title" id="investModalLabel" style="color: var(--gold-primary) !important;">
                    <i class="fas fa-chart-line"></i> Создание инвестиции
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="filter: invert(1);"></button>
            </div>
            <div class="modal-body">
                <form id="investForm">
                    <input type="hidden" id="packageId" name="package_id">
                    <input type="hidden" name="invest" value="1">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="package-summary" id="packageSummary">
                                <!-- Информация о пакете будет загружена динамически -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="investAmount" class="form-label" style="color: var(--text-white) !important;">
                                    <i class="fas fa-dollar-sign"></i> Сумма инвестиции
                                </label>
                                <input type="number"
                                       class="form-control"
                                       id="investAmount"
                                       name="amount"
                                       step="0.01"
                                       required
                                       style="background: rgba(26, 61, 46, 0.8) !important; border: 1px solid var(--gold-primary) !important; color: var(--text-white) !important;">
                                <div class="form-text" id="amountLimits" style="color: var(--text-medium-gray) !important;"></div>
                            </div>

                            <div class="investment-preview" id="investmentPreview">
                                <!-- Предварительный расчет -->
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="border-top: 1px solid var(--gold-primary) !important;">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                <button type="button" class="btn btn-primary" id="confirmInvestBtn">
                    <i class="fas fa-check"></i> Подтвердить инвестицию
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Обработчики кнопок инвестирования
    document.querySelectorAll('.invest-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const packageId = this.dataset.packageId;
            const packageName = this.dataset.packageName;
            const packageType = this.dataset.packageType;
            const minAmount = parseFloat(this.dataset.minAmount);
            const maxAmount = parseFloat(this.dataset.maxAmount) || null;
            const dailyRate = parseFloat(this.dataset.dailyRate);

            openInvestModal(packageId, packageName, packageType, minAmount, maxAmount, dailyRate);
        });
    });

    // Обработчик изменения суммы
    const investAmountInput = document.getElementById('investAmount');
    if (investAmountInput) {
        investAmountInput.addEventListener('input', function() {
            updateInvestmentPreview();
        });
    }

    // Обработчик подтверждения инвестиции
    const confirmBtn = document.getElementById('confirmInvestBtn');
    if (confirmBtn) {
        confirmBtn.addEventListener('click', function() {
            submitInvestment();
        });
    }
});

function openInvestModal(packageId, packageName, packageType, minAmount, maxAmount, dailyRate) {
    // Заполняем данные пакета
    document.getElementById('packageId').value = packageId;
    document.getElementById('investAmount').min = minAmount;
    if (maxAmount) {
        document.getElementById('investAmount').max = maxAmount;
    }

    // Обновляем информацию о пакете
    const packageSummary = document.getElementById('packageSummary');
    packageSummary.innerHTML = `
        <div class="package-info-modal" style="background: rgba(26, 61, 46, 0.3); padding: 1rem; border-radius: 12px; border: 1px solid var(--gold-primary);">
            <h6 style="color: var(--gold-primary) !important; margin-bottom: 1rem;">${packageName}</h6>
            <div class="info-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem; color: var(--text-light-gray);">
                <span>Доходность:</span>
                <span style="color: var(--gold-bright) !important; font-weight: 600;">${dailyRate}% в день</span>
            </div>
            <div class="info-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem; color: var(--text-light-gray);">
                <span>Тип:</span>
                <span>${packageType === 'fixed' ? 'Фиксированный' : 'Гибкий'}</span>
            </div>
            <div class="info-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem; color: var(--text-light-gray);">
                <span>Минимум:</span>
                <span>$${minAmount.toLocaleString()}</span>
            </div>
            ${maxAmount ? `<div class="info-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem; color: var(--text-light-gray);"><span>Максимум:</span><span>$${maxAmount.toLocaleString()}</span></div>` : ''}
        </div>
    `;

    // Обновляем лимиты
    const amountLimits = document.getElementById('amountLimits');
    amountLimits.textContent = `Минимум: $${minAmount.toLocaleString()}${maxAmount ? `, максимум: $${maxAmount.toLocaleString()}` : ''}`;

    // Устанавливаем минимальную сумму
    document.getElementById('investAmount').value = minAmount;
    updateInvestmentPreview();

    // Показываем модальное окно
    const modal = new bootstrap.Modal(document.getElementById('investModal'));
    modal.show();
}

function updateInvestmentPreview() {
    const amount = parseFloat(document.getElementById('investAmount').value) || 0;
    const packageData = getCurrentPackageData();

    if (amount > 0 && packageData) {
        const dailyProfit = amount * (packageData.dailyRate / 100);
        const monthlyProfit = dailyProfit * 30;
        const yearlyProfit = dailyProfit * 365;

        const preview = document.getElementById('investmentPreview');
        preview.innerHTML = `
            <div class="preview-card" style="background: rgba(26, 61, 46, 0.3); padding: 1rem; border-radius: 12px; border: 1px solid var(--gold-primary);">
                <h6 style="color: var(--gold-primary) !important; margin-bottom: 1rem;">Прогноз доходности</h6>
                <div class="preview-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem; color: var(--text-light-gray);">
                    <span>Ежедневно:</span>
                    <span style="color: var(--gold-bright) !important; font-weight: 600;">$${dailyProfit.toFixed(2)}</span>
                </div>
                <div class="preview-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem; color: var(--text-light-gray);">
                    <span>В месяц:</span>
                    <span style="color: var(--gold-bright) !important; font-weight: 600;">$${monthlyProfit.toFixed(2)}</span>
                </div>
                <div class="preview-row" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem; color: var(--text-light-gray);">
                    <span>В год:</span>
                    <span style="color: var(--gold-bright) !important; font-weight: 600;">$${yearlyProfit.toLocaleString()}</span>
                </div>
            </div>
        `;
    }
}

function getCurrentPackageData() {
    const packageId = document.getElementById('packageId').value;
    const btn = document.querySelector(`[data-package-id="${packageId}"]`);
    if (btn) {
        return {
            dailyRate: parseFloat(btn.dataset.dailyRate),
            minAmount: parseFloat(btn.dataset.minAmount),
            maxAmount: parseFloat(btn.dataset.maxAmount) || null
        };
    }
    return null;
}

function submitInvestment() {
    const form = document.getElementById('investForm');
    const formData = new FormData(form);
    const confirmBtn = document.getElementById('confirmInvestBtn');

    // Блокируем кнопку
    confirmBtn.disabled = true;
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Обработка...';

    // Отправляем данные
    fetch('index.php?page=invest', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        // Перезагружаем страницу для отображения результата
        window.location.reload();
    })
    .catch(error => {
        console.error('Ошибка:', error);
        alert('Произошла ошибка при создании инвестиции');

        // Разблокируем кнопку
        confirmBtn.disabled = false;
        confirmBtn.innerHTML = '<i class="fas fa-check"></i> Подтвердить инвестицию';
    });
}
</script>
