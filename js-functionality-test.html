<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Тест JavaScript функциональности</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/luxury-eco.css">
    <link rel="stylesheet" href="assets/css/adaptive-contrast.css">
    <style>
        .test-console {
            background: #000;
            color: #0f0;
            font-family: monospace;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            border-radius: 5px;
            font-size: 12px;
        }
        .error { color: #f00; }
        .warn { color: #ff0; }
        .success { color: #0f0; }
        .info { color: #00f; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>🔧 Тестирование JavaScript функциональности</h2>
        
        <!-- Симуляция авторизованного пользователя -->
        <div id="user-data" style="display: none;" 
             data-user-id="123" 
             data-user-name="Test User" 
             data-user-role="user">
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>🎯 Тестовые элементы</h5>
                    </div>
                    <div class="card-body">
                        <!-- Кнопки инвестирования -->
                        <h6>Кнопки инвестирования:</h6>
                        <button class="btn btn-primary invest-btn mb-2" data-package-type="flexible">
                            🌱 Гибкий пакет
                        </button><br>
                        
                        <button class="btn btn-success invest-btn mb-2" data-package-id="1" data-package-type="fixed">
                            🏢 Фиксированный пакет
                        </button><br>
                        
                        <!-- Кнопки вывода -->
                        <h6>Кнопки вывода:</h6>
                        <button class="btn btn-warning withdraw-btn mb-2" data-method="card">
                            💳 Вывод на карту
                        </button><br>
                        
                        <!-- Кнопки копирования -->
                        <h6>Кнопки копирования:</h6>
                        <button class="btn btn-info copy-btn mb-2" data-copy="<EMAIL>">
                            📋 Копировать email
                        </button><br>
                        
                        <!-- Кнопки быстрых сумм -->
                        <h6>Быстрые суммы:</h6>
                        <button class="btn btn-outline-primary amount-btn mb-2" data-amount="1000">
                            $1000
                        </button>
                        <button class="btn btn-outline-primary amount-btn mb-2" data-amount="5000">
                            $5000
                        </button><br>
                        
                        <!-- Тестовые функции -->
                        <h6>Тестовые функции:</h6>
                        <button onclick="testAllFunctions()" class="btn btn-primary">
                            🧪 Тест всех функций
                        </button>
                        <button onclick="testInvestmentFlow()" class="btn btn-success">
                            💰 Тест инвестиций
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>📊 Консоль тестирования</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-console" class="test-console"></div>
                        <button onclick="clearConsole()" class="btn btn-sm btn-secondary mt-2">Очистить</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Подключаем все JS файлы как на реальном сайте -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/modern.js"></script>
    <script src="assets/js/realtime.js"></script>
    <script src="assets/js/adaptive-contrast.js"></script>

    <script>
        const testConsole = document.getElementById('test-console');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'warn' ? 'warn' : type === 'success' ? 'success' : 'info';
            testConsole.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            testConsole.scrollTop = testConsole.scrollHeight;
            console.log(message);
        }
        
        function clearConsole() {
            testConsole.innerHTML = '';
        }
        
        function testAllFunctions() {
            log('🧪 Начинаем тестирование всех функций...', 'info');
            
            // Тест доступности функций
            const functionsToTest = [
                'handleInvestClick',
                'showInvestModal',
                'checkAuthStatus',
                'handleWithdrawClick',
                'handleCopyClick',
                'handleAmountClick',
                'formatMoney',
                'formatPercent'
            ];
            
            functionsToTest.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    log(`✅ ${funcName} - доступна`, 'success');
                } else {
                    log(`❌ ${funcName} - НЕ найдена`, 'error');
                }
            });
            
            // Тест переменных
            log(`📊 isLoggedIn: ${typeof isLoggedIn !== 'undefined' ? isLoggedIn : 'НЕ ОПРЕДЕЛЕНА'}`, 'info');
            log(`👤 currentUser: ${typeof currentUser !== 'undefined' ? JSON.stringify(currentUser) : 'НЕ ОПРЕДЕЛЕН'}`, 'info');
            
            // Тест элементов DOM
            const userDataElement = document.getElementById('user-data');
            if (userDataElement) {
                log(`✅ Элемент #user-data найден`, 'success');
                log(`📋 Данные пользователя: ${JSON.stringify(userDataElement.dataset)}`, 'info');
            } else {
                log(`❌ Элемент #user-data НЕ найден`, 'error');
            }
            
            // Тест кнопок
            const investButtons = document.querySelectorAll('.invest-btn');
            log(`🎯 Найдено кнопок .invest-btn: ${investButtons.length}`, 'info');
            
            const withdrawButtons = document.querySelectorAll('.withdraw-btn');
            log(`💳 Найдено кнопок .withdraw-btn: ${withdrawButtons.length}`, 'info');
            
            const copyButtons = document.querySelectorAll('.copy-btn');
            log(`📋 Найдено кнопок .copy-btn: ${copyButtons.length}`, 'info');
            
            log('✅ Тестирование функций завершено', 'success');
        }
        
        function testInvestmentFlow() {
            log('💰 Тестируем поток инвестирования...', 'info');
            
            if (typeof window.handleInvestClick === 'function') {
                try {
                    // Симуляция клика по кнопке инвестирования
                    const fakeEvent = {
                        preventDefault: () => {},
                        target: {
                            dataset: {
                                packageId: '1',
                                packageType: 'fixed'
                            }
                        }
                    };
                    
                    log('🔄 Вызываем handleInvestClick...', 'info');
                    window.handleInvestClick(fakeEvent);
                    log('✅ handleInvestClick выполнена без ошибок', 'success');
                    
                } catch (error) {
                    log(`❌ Ошибка в handleInvestClick: ${error.message}`, 'error');
                }
            } else {
                log('❌ handleInvestClick не найдена', 'error');
            }
            
            if (typeof window.showInvestModal === 'function') {
                try {
                    log('🔄 Вызываем showInvestModal...', 'info');
                    window.showInvestModal('1', 'fixed');
                    log('✅ showInvestModal выполнена без ошибок', 'success');
                    
                } catch (error) {
                    log(`❌ Ошибка в showInvestModal: ${error.message}`, 'error');
                }
            } else {
                log('❌ showInvestModal не найдена', 'error');
            }
        }
        
        // Перехват ошибок JavaScript
        window.addEventListener('error', function(e) {
            log(`🚨 JavaScript Error: ${e.message} at ${e.filename}:${e.lineno}`, 'error');
        });
        
        // Автозапуск при загрузке
        window.addEventListener('load', function() {
            log('🚀 Страница тестирования загружена', 'success');
            log('📋 Доступные тесты: "Тест всех функций", "Тест инвестиций"', 'info');
            
            // Автоматический тест при загрузке
            setTimeout(() => {
                log('🔄 Запускаем автоматический тест...', 'info');
                testAllFunctions();
            }, 1000);
        });
    </script>
</body>
</html>
