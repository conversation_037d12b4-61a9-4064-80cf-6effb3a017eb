/* ===== ИСПРАВЛЕНИЕ ЧИТАЕМОСТИ ТЕКСТА В СВЕТЛЫХ СЕКЦИЯХ ===== */

/* Переменные для темного текста на светлом фоне */
:root {
    --text-dark-primary: #1f2937;      /* Основной темный текст */
    --text-dark-secondary: #374151;    /* Вторичный темный текст */
    --text-dark-muted: #6b7280;        /* Приглушенный темный текст */
    --text-dark-light: #9ca3af;        /* Светлый темный текст */
    
    /* Фоны для светлых карточек */
    --card-bg-white: #ffffff;
    --card-bg-light: #f8fafc;
    --card-bg-very-light: #f1f5f9;
}

/* ===== ИСПРАВЛЕНИЕ КАРТОЧЕК ИНВЕСТИЦИЙ ===== */

/* Карточки инвестиций */
.investment-card {
    background: var(--card-bg-white) !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 1rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.3s ease !important;
}

.investment-card:hover {
    transform: translateY(-8px) !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
    border-color: #10b981 !important;
}

/* Заголовки в карточках инвестиций */
.investment-card .investment-title,
.investment-card h5,
.investment-card h4,
.investment-card h3 {
    color: var(--text-dark-primary) !important;
    font-weight: 600 !important;
}

/* Описания в карточках инвестиций */
.investment-card .investment-category,
.investment-card p,
.investment-card .stat-label,
.investment-card .stat-value {
    color: var(--text-dark-secondary) !important;
}

/* Цены и значения */
.investment-card .investment-price,
.investment-card .stat-value {
    color: var(--text-dark-primary) !important;
    font-weight: 600 !important;
}

/* ===== ИСПРАВЛЕНИЕ КАРТОЧЕК ПАКЕТОВ ===== */

/* Карточки инвестиционных пакетов */
.luxury-investment-card {
    background: var(--card-bg-white) !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 1rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    backdrop-filter: none !important;
}

.luxury-investment-card:hover {
    transform: translateY(-8px) !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
    border-color: #10b981 !important;
}

/* Заголовки в пакетах */
.luxury-investment-card .luxury-investment-title,
.luxury-investment-card h4,
.luxury-investment-card h5,
.luxury-investment-card h6 {
    color: var(--text-dark-primary) !important;
    font-weight: 600 !important;
}

/* Описания в пакетах */
.luxury-investment-card .luxury-investment-period,
.luxury-investment-card p,
.luxury-investment-card span,
.luxury-investment-card .feature span {
    color: var(--text-dark-secondary) !important;
}

/* Проценты доходности */
.luxury-investment-card .luxury-investment-rate {
    color: #059669 !important;
    font-weight: 800 !important;
}

/* Примеры расчетов */
.luxury-investment-card .example-calc div {
    color: var(--text-dark-secondary) !important;
    border-bottom-color: #e5e7eb !important;
}

.luxury-investment-card .example-calc div:last-child {
    color: #059669 !important;
    font-weight: 600 !important;
}

/* ===== ИСПРАВЛЕНИЕ КАРТОЧЕК ФУНКЦИЙ ===== */

/* Карточки функций в светлых секциях */
.features-section .feature-card,
.how-it-works-section .step-card {
    background: var(--card-bg-white) !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 1rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.features-section .feature-card:hover,
.how-it-works-section .step-card:hover {
    transform: translateY(-8px) !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
    border-color: #10b981 !important;
}

/* Заголовки в карточках функций */
.features-section .feature-title,
.how-it-works-section .step-title,
.features-section h5,
.how-it-works-section h5 {
    color: var(--text-dark-primary) !important;
    font-weight: 600 !important;
}

/* Описания в карточках функций */
.features-section .feature-description,
.how-it-works-section .step-description,
.features-section p,
.how-it-works-section p {
    color: var(--text-dark-secondary) !important;
    line-height: 1.6 !important;
}

/* ===== ИСПРАВЛЕНИЕ ЗАГОЛОВКОВ СЕКЦИЙ ===== */

/* Заголовки в светлых секциях */
.features-section .section-title,
.how-it-works-section .section-title {
    color: var(--text-dark-primary) !important;
    font-weight: 700 !important;
}

/* Подзаголовки в светлых секциях */
.features-section .section-subtitle,
.how-it-works-section .section-subtitle {
    color: var(--text-dark-secondary) !important;
}

/* ===== ИКОНКИ В СВЕТЛЫХ СЕКЦИЯХ ===== */

/* Иконки функций */
.features-section .feature-icon,
.how-it-works-section .step-icon {
    color: #059669 !important;
}

/* Номера шагов */
.how-it-works-section .step-number {
    background: linear-gradient(135deg, #059669, #10b981) !important;
    color: #ffffff !important;
}

/* ===== АДАПТИВНОСТЬ ===== */

@media (max-width: 768px) {
    .investment-card,
    .luxury-investment-card,
    .feature-card,
    .step-card {
        padding: 1.5rem !important;
    }
}

@media (max-width: 576px) {
    .investment-card,
    .luxury-investment-card,
    .feature-card,
    .step-card {
        padding: 1.25rem !important;
    }
}
