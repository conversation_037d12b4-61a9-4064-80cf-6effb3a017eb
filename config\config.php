<?php
// Основные настройки проекта GreenChain EcoFund
define('SITE_NAME', 'GreenChain EcoFund');
define('SITE_URL', 'http://localhost');
define('SITE_VERSION', '1.0.0');

// Настройки базы данных
define('DB_HOST', 'localhost');
define('DB_NAME', 'green');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Настройки безопасности
define('HASH_ALGO', 'sha256');
define('SESSION_LIFETIME', 3600 * 24 * 7); // 7 дней
define('PASSWORD_MIN_LENGTH', 8);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 минут

// Email настройки
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'GreenChain EcoFund');

// Настройки инвестиций
define('MIN_INVESTMENT', 10);
define('MAX_INVESTMENT', 100000);
define('DAILY_PROFIT_FLEXIBLE', 0.5); // 0.5% в день для гибких пакетов
define('DAILY_PROFIT_FIXED', 1.2); // 1.2% в день для фиксированных пакетов
define('REFERRAL_BONUS_LEVEL_1', 5); // 5% с первого уровня
define('REFERRAL_BONUS_LEVEL_2', 3); // 3% со второго уровня
define('REFERRAL_BONUS_LEVEL_3', 1); // 1% с третьего уровня

// Настройки файлов
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']);

// Настройки API
define('API_KEY', 'your-api-key-here');
define('CRYPTO_API_URL', 'https://api.coindesk.com/v1/bpi/currentprice.json');

// Часовой пояс
date_default_timezone_set('Europe/Moscow');

// Подключение к базе данных
try {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];
    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
} catch (PDOException $e) {
    error_log("Database connection error: " . $e->getMessage());
    die("Ошибка подключения к базе данных. Попробуйте позже.");
}

// Настройки сессии перенесены в session_config.php

// Обработка ошибок
error_reporting(E_ALL);
ini_set('display_errors', 0); // Отключить в продакшене
ini_set('log_errors', 1);
ini_set('error_log', 'logs/error.log');

// Создание директорий если не существуют
$directories = ['logs', 'uploads', 'uploads/avatars', 'uploads/documents'];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}
?>
