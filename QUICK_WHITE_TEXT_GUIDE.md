# Быстрое руководство: Белый текст в элементах - GreenChain EcoFund

## Основные классы

### 🎯 Самые используемые

```html
<!-- Белый текст для колонок -->
<div class="col-lg-6 white-text">Содержимое</div>

<!-- Белый текст для карточек -->
<div class="stats-card white-text">Статистика</div>

<!-- Принудительный белый текст -->
<div class="white-text-force">Всегда белый</div>

<!-- Умный белый текст (адаптируется к фону) -->
<div class="white-text-smart">Адаптивный белый</div>
```

### 📊 Для таблиц

```html
<!-- Вся таблица белая -->
<table class="table table-white-text">...</table>

<!-- Конкретные колонки белые -->
<th class="white-column">Заголовок</th>
<td class="col-white">Данные</td>
```

### 🧭 Для навигации

```html
<!-- Навигация с белым текстом -->
<nav class="navbar white-text">
    <a class="nav-link white-text" href="#">Ссылка</a>
</nav>
```

### 📝 Для форм

```html
<!-- Метки и элементы форм -->
<label class="form-label white-text">Метка</label>
<button class="btn white-text">Кнопка</button>
```

### 🏷️ Для заголовков

```html
<!-- Заголовки -->
<h1 class="heading-white">Заголовок</h1>
<h3 class="white-text">Подзаголовок</h3>
```

## Быстрые примеры

### Дашборд на темном фоне
```html
<section class="bg-luxury-hero">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 white-text">
                <div class="stats-card">
                    <div class="stats-value">$250,000</div>
                    <div class="stats-label">Инвестиции</div>
                </div>
            </div>
        </div>
    </div>
</section>
```

### Таблица с белыми колонками
```html
<table class="table table-dark">
    <thead>
        <tr>
            <th>Дата</th>
            <th class="white-column">Проект</th>
            <th class="col-white">Статус</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>15.06.2024</td>
            <td class="white-column">Солнечная ферма</td>
            <td class="col-white">Активен</td>
        </tr>
    </tbody>
</table>
```

### Форма с белым текстом
```html
<form style="background: rgba(26, 61, 46, 0.8);">
    <label class="form-label white-text">Выберите проект</label>
    <select class="form-select">
        <option>Солнечная энергия</option>
    </select>
    <button class="btn btn-primary white-text">Инвестировать</button>
</form>
```

## Полный список классов

### Основные
- `.white-text` - базовый белый текст
- `.white-text-column` - белый текст для колонок
- `.col-white-text` - альтернативный класс для колонок
- `.white-text-force` - принудительный белый
- `.white-text-smart` - умный адаптивный белый

### Для таблиц
- `.table-white-text` - вся таблица
- `.white-column` - колонка таблицы
- `.col-white` - альтернативный класс для колонки

### Для карточек
- `.stats-card.white-text` - статистические карточки
- `.investment-card.white-text` - инвестиционные карточки
- `.card.white-text` - обычные карточки
- `.leaderboard-item.white-text` - элементы лидерборда

### Для навигации
- `.navbar.white-text` - навигационная панель
- `.nav.white-text` - навигационное меню
- `.nav-link.white-text` - ссылки навигации

### Для заголовков
- `.heading-white` - специальный класс для заголовков
- `h1.white-text`, `h2.white-text`, etc. - прямое применение

### Для форм
- `.form-label.white-text` - метки полей
- `.btn.white-text` - кнопки
- `.form-control.white-text` - поля ввода

### Для текста
- `p.white-text` - параграфы
- `span.white-text` - спаны
- `.text-white-force` - принудительный белый для текста
- `.paragraph-white` - специально для параграфов

### Bootstrap колонки
Все Bootstrap колонки поддерживают `.white-text`:
- `.col-1.white-text` до `.col-12.white-text`
- `.col-lg-1.white-text` до `.col-lg-12.white-text`
- `.col-md-1.white-text` до `.col-md-12.white-text`

### Специальные элементы
- `.alert.white-text` - алерты
- `.badge.white-text` - бейджи
- `.modal.white-text` - модальные окна
- `.breadcrumb.white-text` - хлебные крошки
- `.pagination.white-text` - пагинация

## Когда использовать

### ✅ Рекомендуется:
- **Темные фоны** (#1a3d2e, #2d5a3d)
- **Hero секции** с темными эко-фонами
- **Навигация** на темных панелях
- **Карточки** на темном фоне
- **Модальные окна** с темными фонами

### ⚠️ С осторожностью:
- **Смешанные фоны** - используйте `.white-text-smart`
- **Мелкий текст** - проверяйте читаемость
- **Контрастность** - соблюдайте WCAG стандарты

### ❌ Не используйте:
- **Светлые фоны** - белый на белом нечитаем
- **Весь контент** - потеряется иерархия

## Технические особенности

### Сохраняемые стили
Классы изменяют только `color`, сохраняя:
- ✅ Шрифт Inter (`font-family`)
- ✅ Размеры (`font-size`)
- ✅ Жирность (`font-weight`)
- ✅ Тени (`text-shadow`)
- ✅ Интервалы (`letter-spacing`)
- ✅ Декорации (`text-decoration`)

### CSS специфичность
```css
.white-text-force * {
    color: #ffffff !important;
}
```

### Адаптивность
`.white-text-smart` автоматически:
- **Темный фон** → белый текст (#ffffff)
- **Светлый фон** → черный текст (#000000)

## Тестирование

Откройте для тестирования:
```
http://127.0.0.1:8081/test-contrast.html
```

Раздел: "Тест: Белый текст в элементах"

## Быстрая интеграция

1. **Подключите CSS:**
   ```html
   <link rel="stylesheet" href="assets/css/adaptive-contrast.css">
   ```

2. **Добавьте класс:**
   ```html
   <div class="col-lg-6 white-text">Содержимое</div>
   ```

3. **Проверьте результат** в браузере

## Поддержка

- ✅ Все современные браузеры
- ✅ Мобильные устройства
- ✅ Совместимость с Bootstrap
- ✅ Интеграция с адаптивной контрастностью

---

**💡 Совет:** Начните с `.white-text-smart` для автоматической адаптации к фону, затем используйте `.white-text-force` для принудительного применения белого цвета.
