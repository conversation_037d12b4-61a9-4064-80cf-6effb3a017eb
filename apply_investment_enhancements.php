<?php
// Скрипт для применения улучшений системы инвестиционных пакетов
require_once 'config/config.php';

echo "<h1>Применение улучшений системы инвестиционных пакетов</h1>";

try {
    // Подключение к базе данных
    $conn = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<h2>✅ Подключение к базе данных успешно</h2>";
    
    // Читаем и выполняем SQL файл
    $sql_file = 'database/enhance_investment_packages.sql';
    
    if (!file_exists($sql_file)) {
        throw new Exception("SQL файл не найден: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    $statements = explode(';', $sql_content);
    
    echo "<h2>Выполнение SQL команд...</h2>";
    
    $executed = 0;
    $errors = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $conn->exec($statement);
            $executed++;
            echo "✅ Команда выполнена успешно<br>";
        } catch (PDOException $e) {
            $errors++;
            echo "❌ Ошибка выполнения команды: " . $e->getMessage() . "<br>";
            echo "SQL: " . substr($statement, 0, 100) . "...<br><br>";
        }
    }
    
    echo "<h2>Результаты:</h2>";
    echo "✅ Успешно выполнено команд: $executed<br>";
    echo "❌ Ошибок: $errors<br>";
    
    // Проверяем, что новые поля добавлены
    echo "<h2>Проверка структуры таблицы investment_packages:</h2>";
    
    $stmt = $conn->query("DESCRIBE investment_packages");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $new_fields = [
        'category', 'risk_level', 'popularity_score', 'total_invested', 
        'total_investors', 'icon', 'color_scheme', 'sort_order', 
        'is_featured', 'is_limited', 'tags', 'benefits', 'environmental_impact'
    ];
    
    foreach ($new_fields as $field) {
        if (in_array($field, $columns)) {
            echo "✅ Поле '$field' добавлено<br>";
        } else {
            echo "❌ Поле '$field' не найдено<br>";
        }
    }
    
    // Проверяем новые таблицы
    echo "<h2>Проверка новых таблиц:</h2>";
    
    $new_tables = ['package_categories', 'package_reviews', 'package_faq', 'package_history'];
    
    foreach ($new_tables as $table) {
        try {
            $stmt = $conn->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "✅ Таблица '$table' создана (записей: $count)<br>";
        } catch (PDOException $e) {
            echo "❌ Таблица '$table' не найдена<br>";
        }
    }
    
    // Проверяем данные в категориях
    echo "<h2>Проверка данных категорий:</h2>";
    
    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM package_categories");
        $count = $stmt->fetchColumn();
        echo "✅ Категории пакетов: $count записей<br>";
        
        if ($count > 0) {
            $stmt = $conn->query("SELECT name FROM package_categories ORDER BY sort_order");
            $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "Категории: " . implode(', ', $categories) . "<br>";
        }
    } catch (PDOException $e) {
        echo "❌ Ошибка проверки категорий: " . $e->getMessage() . "<br>";
    }
    
    // Проверяем обновленные пакеты
    echo "<h2>Проверка обновленных пакетов:</h2>";
    
    try {
        $stmt = $conn->query("
            SELECT name, category, risk_level, is_featured, tags 
            FROM investment_packages 
            WHERE is_active = 1
        ");
        $packages = $stmt->fetchAll();
        
        foreach ($packages as $package) {
            echo "📦 {$package['name']}: ";
            echo "категория={$package['category']}, ";
            echo "риск={$package['risk_level']}, ";
            echo "рекомендуемый=" . ($package['is_featured'] ? 'да' : 'нет');
            echo "<br>";
        }
    } catch (PDOException $e) {
        echo "❌ Ошибка проверки пакетов: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>🎉 Улучшения системы инвестиционных пакетов применены!</h2>";
    echo "<p><a href='index.php?page=invest'>Перейти к инвестиционным пакетам</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Критическая ошибка:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}

h1, h2 {
    color: #2c3e50;
}

h1 {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

h2 {
    border-left: 4px solid #27ae60;
    padding-left: 15px;
    margin-top: 30px;
}

.success {
    color: #27ae60;
    font-weight: bold;
}

.error {
    color: #e74c3c;
    font-weight: bold;
}

p {
    line-height: 1.6;
}

a {
    color: #3498db;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
</style>
