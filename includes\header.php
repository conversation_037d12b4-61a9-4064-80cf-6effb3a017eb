<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?><?php echo SITE_NAME; ?></title>
    <meta name="description" content="GreenChain EcoFund - Инвестиционная платформа для экологических проектов и экомайнинга">
    <meta name="keywords" content="экоинвестиции, зеленые технологии, экомайнинг, инвестиции, экология">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/eco-achievements-preserved.css">
    <link rel="stylesheet" href="assets/css/light-sections-contrast.css">
    <link rel="stylesheet" href="assets/css/unified-sections.css">
    <link rel="stylesheet" href="assets/css/investment-cards-styling.css">
    <link rel="stylesheet" href="assets/css/text-alignment-fixes.css">
    <link rel="stylesheet" href="assets/css/modern-navigation.css">
    <link rel="stylesheet" href="assets/css/section-separation.css">
    <link rel="stylesheet" href="assets/css/layout-alignment-improvements.css">
    <link rel="stylesheet" href="assets/css/enhanced-investment-packages.css">
    <link rel="stylesheet" href="assets/css/desktop-centering.css">
    <link rel="stylesheet" href="assets/css/complete-site-centering.css">
    <link rel="stylesheet" href="assets/css/page-specific-centering.css">

    <!-- Leaflet для карты -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">
    
    <!-- Chart.js для графиков -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- SVG Icons -->
    <?php include 'assets/images/svg-icons.html'; ?>

    <!-- Preloader -->
    <div id="preloader">
        <div class="spinner">
            <div class="spinner-ring"></div>
        </div>
    </div>

    <!-- Header -->
    <header class="modern-header" id="modernHeader">
        <nav class="modern-navbar">
            <div class="container">
                <div class="d-flex align-items-center justify-content-between w-100">
                    <!-- Логотип -->
                    <a class="modern-brand" href="index.php">
                        <div class="brand-logo-modern">
                            🌱
                        </div>
                        <span class="brand-text-modern text-brand-gold">GreenChain EcoFund</span>
                    </a>

                    <!-- Навигация для авторизованных пользователей -->
                    <?php if (isLoggedIn()): ?>
                        <div class="d-flex align-items-center gap-3">
                            <!-- Информация о пользователе -->
                            <div class="user-info">
                                <div class="user-avatar">
                                    <?php echo strtoupper(substr($_SESSION['user_name'], 0, 1)); ?>
                                </div>
                                <div class="user-details">
                                    <div class="user-name"><?php echo sanitizeInput($_SESSION['user_name']); ?></div>
                                    <div class="user-balance"><?php echo formatMoney(getUserBalance($_SESSION['user_id'])); ?></div>
                                </div>
                            </div>

                            <!-- Навигационные кнопки (десктоп) -->
                            <div class="auth-nav">
                                <a href="index.php?page=dashboard" class="nav-button">
                                    <svg class="nav-icon"><use href="#icon-dashboard"></use></svg>
                                    Кабинет
                                </a>
                                <a href="index.php?page=referrals" class="nav-button referrals glow">
                                    <svg class="nav-icon"><use href="#icon-referrals"></use></svg>
                                    Рефералы
                                </a>
                                <a href="index.php?page=profile" class="nav-button settings">
                                    <svg class="nav-icon"><use href="#icon-settings"></use></svg>
                                    Настройки
                                </a>
                                <a href="index.php?page=logout" class="nav-button logout">
                                    <svg class="nav-icon"><use href="#icon-logout"></use></svg>
                                    Выход
                                </a>
                            </div>

                            <!-- Мобильное меню -->
                            <button class="mobile-nav-toggle" id="mobileNavToggle">
                                <svg class="nav-icon"><use href="#icon-menu"></use></svg>
                            </button>
                        </div>
                    <?php else: ?>
                        <!-- Навигация для неавторизованных пользователей -->
                        <div class="d-flex align-items-center gap-2">
                            <a href="index.php?page=login" class="nav-button">
                                <i class="fas fa-sign-in-alt"></i>
                                Войти
                            </a>
                            <a href="index.php?page=register" class="nav-button referrals">
                                <i class="fas fa-user-plus"></i>
                                Регистрация
                            </a>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Мобильная навигация -->
                <?php if (isLoggedIn()): ?>
                    <div class="mobile-nav" id="mobileNav">
                        <div class="mobile-nav-buttons">
                            <a href="index.php?page=dashboard" class="nav-button">
                                <svg class="nav-icon"><use href="#icon-dashboard"></use></svg>
                                Личный кабинет
                            </a>
                            <a href="index.php?page=referrals" class="nav-button referrals">
                                <svg class="nav-icon"><use href="#icon-referrals"></use></svg>
                                Рефералы
                            </a>
                            <a href="index.php?page=profile" class="nav-button settings">
                                <svg class="nav-icon"><use href="#icon-settings"></use></svg>
                                Настройки профиля
                            </a>
                            <a href="index.php?page=logout" class="nav-button logout">
                                <svg class="nav-icon"><use href="#icon-logout"></use></svg>
                                Выйти
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </nav>
    </header>


    <!-- Flash Messages -->
    <?php 
    $flash = getFlashMessage();
    if ($flash): 
    ?>
        <div class="alert alert-<?php echo $flash['type']; ?> alert-dismissible fade show" role="alert">
            <?php echo $flash['message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Скрытый элемент с данными пользователя для JavaScript -->
    <?php if (isLoggedIn()): ?>
        <div id="user-data" style="display: none;"
             data-user-id="<?php echo $_SESSION['user_id']; ?>"
             data-user-name="<?php echo htmlspecialchars($_SESSION['user_name']); ?>"
             data-user-role="<?php echo $_SESSION['user_role']; ?>">
        </div>
    <?php endif; ?>

    <!-- JavaScript для мобильной навигации -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mobileToggle = document.getElementById('mobileNavToggle');
            const mobileNav = document.getElementById('mobileNav');

            if (mobileToggle && mobileNav) {
                mobileToggle.addEventListener('click', function() {
                    mobileNav.classList.toggle('show');
                });

                // Закрытие при клике вне меню
                document.addEventListener('click', function(e) {
                    if (!mobileToggle.contains(e.target) && !mobileNav.contains(e.target)) {
                        mobileNav.classList.remove('show');
                    }
                });
            }
        });
    </script>

    <!-- Main Content -->
    <main class="main-content">
