<?php
// Добавление базовых данных для образования
require_once 'config/config.php';

echo "<h1>Добавление данных для образования</h1>";

try {
    // Добавляем категории образования
    $categories = [
        [1, 'Основы инвестирования', 'Базовые знания об инвестициях', 'fas fa-chart-line', 1],
        [2, 'Экологические проекты', 'Информация об эко-проектах', 'fas fa-leaf', 2],
        [3, 'Управление рисками', 'Как управлять инвестиционными рисками', 'fas fa-shield-alt', 3],
        [4, 'Криптовалюты', 'Цифровые валюты и блокчейн', 'fab fa-bitcoin', 4]
    ];
    
    foreach ($categories as $category) {
        try {
            $stmt = $conn->prepare("INSERT IGNORE INTO education_categories (id, name, description, icon, sort_order) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute($category);
            echo "✅ Категория добавлена: {$category[1]}<br>";
        } catch (Exception $e) {
            echo "❌ Ошибка добавления категории {$category[1]}: " . $e->getMessage() . "<br>";
        }
    }
    
    // Добавляем примеры проектов на карте
    $projects = [
        ['Солнечная ферма "Зеленая энергия"', 'Крупнейшая солнечная электростанция в регионе', 'Краснодарский край, Россия', 45.0355, 38.9753, 'solar', 2500000.00, 12.5],
        ['Ветропарк "Северный ветер"', 'Современный ветропарк с высокой эффективностью', 'Мурманская область, Россия', 68.9585, 33.0827, 'wind', 1800000.00, 10.8],
        ['Гидроэлектростанция "Чистая вода"', 'Экологически чистая ГЭС малой мощности', 'Алтайский край, Россия', 52.0394, 85.0913, 'hydro', 3200000.00, 15.2]
    ];
    
    foreach ($projects as $project) {
        try {
            $stmt = $conn->prepare("INSERT IGNORE INTO map_projects (name, description, location, latitude, longitude, project_type, investment_amount, roi_percentage) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute($project);
            echo "✅ Проект добавлен: {$project[0]}<br>";
        } catch (Exception $e) {
            echo "❌ Ошибка добавления проекта {$project[0]}: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<h2>✅ Данные успешно добавлены!</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ Критическая ошибка: " . $e->getMessage() . "</h2>";
}

echo "<p><a href='index.php'>← Вернуться на главную</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #2c3e50; }
h2 { color: #34495e; border-bottom: 2px solid #ecf0f1; padding-bottom: 5px; }
</style>
