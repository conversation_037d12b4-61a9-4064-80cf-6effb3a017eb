<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест исправлений дизайна - <PERSON><PERSON><PERSON><PERSON></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/eco-achievements-preserved.css">
    <link rel="stylesheet" href="assets/css/light-sections-contrast.css">
    <link rel="stylesheet" href="assets/css/unified-sections.css">
    <link rel="stylesheet" href="assets/css/investment-cards-styling.css">
    <link rel="stylesheet" href="assets/css/text-alignment-fixes.css">
    <link rel="stylesheet" href="assets/css/modern-navigation.css">
    <link rel="stylesheet" href="assets/css/section-separation.css">
    
    <style>
        /* Дополнительные стили для тестирования */
        .test-section {
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 2rem;
            margin: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .test-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- SVG Icons -->
    <svg style="display: none;">
        <defs>
            <symbol id="icon-dashboard" viewBox="0 0 24 24">
                <path fill="currentColor" d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
            </symbol>
            <symbol id="icon-referrals" viewBox="0 0 24 24">
                <path fill="currentColor" d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 7H16c-.8 0-1.54.37-2.01.99L12 10.5 8.01 7.99A2.5 2.5 0 0 0 6 7H3.46c-.81 0-1.54.59-1.42 1.37L4.5 16H7v6h2v-6h2l.48-2.63L14 16h2v6h4z"/>
            </symbol>
            <symbol id="icon-settings" viewBox="0 0 24 24">
                <path fill="currentColor" d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97L2.46 14.6c-.19.15-.24.42-.12.64l2 3.46c.12.22.39.31.61.22l2.49-1c.52.39 1.06.73 1.69.98l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.25 1.17-.59 1.69-.98l2.49 1c.22.09.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/>
            </symbol>
            <symbol id="icon-logout" viewBox="0 0 24 24">
                <path fill="currentColor" d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
            </symbol>
            <symbol id="icon-menu" viewBox="0 0 24 24">
                <path fill="currentColor" d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
            </symbol>
            <symbol id="icon-eco-leaf" viewBox="0 0 24 24">
                <path fill="currentColor" d="M17,8C8,10 5.9,16.17 3.82,21.34L5.71,22L6.66,19.7C7.14,19.87 7.64,20 8,20C19,20 22,3 22,3C21,5 14,5.25 9,6.25C4,7.25 2,11.5 2,13.5C2,15.5 3.75,17.25 3.75,17.25C7,8 17,8 17,8Z"/>
            </symbol>
        </defs>
    </svg>

    <!-- Header -->
    <header class="modern-header" id="modernHeader">
        <nav class="modern-navbar">
            <div class="container">
                <div class="d-flex align-items-center justify-content-between w-100">
                    <!-- Логотип -->
                    <a class="modern-brand" href="#home">
                        <div class="brand-logo-modern">🌱</div>
                        <span class="brand-text-modern">GreenChain EcoFund</span>
                    </a>

                    <!-- Навигация -->
                    <div class="d-flex align-items-center gap-3">
                        <!-- Информация о пользователе -->
                        <div class="user-info">
                            <div class="user-avatar">T</div>
                            <div class="user-details">
                                <div class="user-name">Test User</div>
                                <div class="user-balance">$1,250.00</div>
                            </div>
                        </div>

                        <!-- Навигационные кнопки -->
                        <div class="auth-nav">
                            <a href="#dashboard" class="nav-button">
                                <svg class="nav-icon"><use href="#icon-dashboard"></use></svg>
                                Кабинет
                            </a>
                            <a href="#referrals" class="nav-button referrals glow">
                                <svg class="nav-icon"><use href="#icon-referrals"></use></svg>
                                Рефералы
                            </a>
                            <a href="#settings" class="nav-button settings">
                                <svg class="nav-icon"><use href="#icon-settings"></use></svg>
                                Настройки
                            </a>
                            <a href="#logout" class="nav-button logout">
                                <svg class="nav-icon"><use href="#icon-logout"></use></svg>
                                Выход
                            </a>
                        </div>

                        <!-- Мобильное меню -->
                        <button class="mobile-nav-toggle" id="mobileNavToggle">
                            <svg class="nav-icon"><use href="#icon-menu"></use></svg>
                        </button>
                    </div>
                </div>

                <!-- Мобильная навигация -->
                <div class="mobile-nav" id="mobileNav">
                    <div class="mobile-nav-buttons">
                        <a href="#dashboard" class="nav-button">
                            <svg class="nav-icon"><use href="#icon-dashboard"></use></svg>
                            Личный кабинет
                        </a>
                        <a href="#referrals" class="nav-button referrals">
                            <svg class="nav-icon"><use href="#icon-referrals"></use></svg>
                            Рефералы
                        </a>
                        <a href="#settings" class="nav-button settings">
                            <svg class="nav-icon"><use href="#icon-settings"></use></svg>
                            Настройки профиля
                        </a>
                        <a href="#logout" class="nav-button logout">
                            <svg class="nav-icon"><use href="#icon-logout"></use></svg>
                            Выйти
                        </a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="modern-hero section-important">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="hero-content-modern">
                            <h1 class="hero-title-modern">
                                Тестирование исправлений дизайна
                                <span class="text-brand-gold">GreenChain EcoFund</span>
                            </h1>
                            <p class="hero-subtitle-modern">
                                Проверяем геометрию секций, выравнивание текста, стилизацию навигации и четкое разделение разделов.
                            </p>
                            
                            <div class="hero-stats-modern">
                                <div class="stat-card-modern">
                                    <div class="stat-number-modern">✓</div>
                                    <div class="stat-label-modern">Геометрия</div>
                                </div>
                                <div class="stat-card-modern">
                                    <div class="stat-number-modern">✓</div>
                                    <div class="stat-label-modern">Выравнивание</div>
                                </div>
                                <div class="stat-card-modern">
                                    <div class="stat-number-modern">✓</div>
                                    <div class="stat-label-modern">Навигация</div>
                                </div>
                            </div>

                            <div class="hero-buttons-modern">
                                <a href="#test" class="btn-hero-primary">
                                    <svg class="nav-icon me-2"><use href="#icon-eco-leaf"></use></svg>
                                    Протестировать
                                </a>
                                <a href="#results" class="btn-hero-secondary">
                                    <svg class="nav-icon me-2"><use href="#icon-dashboard"></use></svg>
                                    Результаты
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="test-card">
                            <h3>Тест геометрии</h3>
                            <p>Эта карточка должна иметь border-radius максимум 8px и четкие прямоугольные формы.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Test Section 1 -->
        <section class="section-green-bg">
            <div class="container">
                <h2 class="section-title-modern">Тест выравнивания текста</h2>
                <p class="section-subtitle-modern">
                    Этот заголовок должен быть выровнен по центру, а текст в карточках - по левому краю.
                </p>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="test-card">
                            <h3>Заголовок карточки</h3>
                            <p>Этот текст должен быть выровнен по левому краю с правильными межстрочными интервалами.</p>
                            <ul>
                                <li>Пункт списка 1</li>
                                <li>Пункт списка 2</li>
                                <li>Пункт списка 3</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="test-card">
                            <h3>Второй заголовок</h3>
                            <p>Проверяем единообразное выравнивание и правильные отступы между элементами.</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="test-card">
                            <h3>Третий заголовок</h3>
                            <p>Все карточки должны иметь одинаковую высоту и правильную геометрию.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Test Section 2 -->
        <section class="section-dark-bg">
            <div class="container">
                <h2 class="section-title-modern">Тест разделения секций</h2>
                <p class="section-subtitle-modern">
                    Эта секция должна быть четко визуально отделена от предыдущей контрастным фоном и границами.
                </p>
                
                <div class="section-container-highlighted">
                    <h3>Выделенный контейнер</h3>
                    <p>Этот контейнер демонстрирует дополнительное выделение важного контента.</p>
                </div>
            </div>
        </section>

        <!-- Test Section 3 -->
        <section class="section-accent-bg section-secondary">
            <div class="container">
                <h2 class="section-title-modern">Тест адаптивности</h2>
                <p class="section-subtitle-modern">
                    Проверяем, как дизайн адаптируется к разным размерам экрана.
                </p>
                
                <div class="row">
                    <div class="col-12 col-md-6 col-lg-3">
                        <div class="test-card">
                            <h4>Мобильный</h4>
                            <p>Тест на мобильных устройствах</p>
                        </div>
                    </div>
                    <div class="col-12 col-md-6 col-lg-3">
                        <div class="test-card">
                            <h4>Планшет</h4>
                            <p>Тест на планшетах</p>
                        </div>
                    </div>
                    <div class="col-12 col-md-6 col-lg-3">
                        <div class="test-card">
                            <h4>Десктоп</h4>
                            <p>Тест на десктопе</p>
                        </div>
                    </div>
                    <div class="col-12 col-md-6 col-lg-3">
                        <div class="test-card">
                            <h4>Широкий экран</h4>
                            <p>Тест на широких экранах</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mobileToggle = document.getElementById('mobileNavToggle');
            const mobileNav = document.getElementById('mobileNav');
            
            if (mobileToggle && mobileNav) {
                mobileToggle.addEventListener('click', function() {
                    mobileNav.classList.toggle('show');
                });
                
                document.addEventListener('click', function(e) {
                    if (!mobileToggle.contains(e.target) && !mobileNav.contains(e.target)) {
                        mobileNav.classList.remove('show');
                    }
                });
            }
        });
    </script>
</body>
</html>
