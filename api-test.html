<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔌 Тест API endpoints</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-console {
            background: #000;
            color: #0f0;
            font-family: monospace;
            padding: 15px;
            height: 500px;
            overflow-y: auto;
            border-radius: 5px;
            font-size: 12px;
        }
        .error { color: #f00; }
        .warn { color: #ff0; }
        .success { color: #0f0; }
        .info { color: #00f; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>🔌 Тестирование API endpoints</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>🎯 API тесты</h5>
                    </div>
                    <div class="card-body">
                        <button onclick="testAllAPIs()" class="btn btn-primary mb-3">🚀 Тест всех API</button>
                        <button onclick="testSpecificAPI()" class="btn btn-success mb-3">🎯 Тест конкретных API</button>
                        <button onclick="clearConsole()" class="btn btn-secondary mb-3">🧹 Очистить</button>
                        
                        <h6>Индивидуальные тесты:</h6>
                        <button onclick="testAPI('api/get-package.php?id=1')" class="btn btn-sm btn-outline-primary mb-1">📦 get-package</button><br>
                        <button onclick="testAPI('api/stats.php')" class="btn btn-sm btn-outline-primary mb-1">📊 stats</button><br>
                        <button onclick="testAPI('api/realtime.php?action=global_stats')" class="btn btn-sm btn-outline-success mb-1">🔄 realtime-stats</button><br>
                        <button onclick="testAPI('api/profit-chart.php')" class="btn btn-sm btn-outline-info mb-1">📈 profit-chart</button><br>
                        <button onclick="testAPI('api/project-details.php?id=1')" class="btn btn-sm btn-outline-warning mb-1">🏗️ project-details</button><br>
                        <button onclick="testAPI('api/investment-details.php?id=1')" class="btn btn-sm btn-outline-secondary mb-1">💰 investment-details</button><br>
                        <button onclick="testAPI('api/support.php')" class="btn btn-sm btn-outline-danger mb-1">🎧 support</button><br>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>📊 Результаты тестов</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-console" class="test-console"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const testConsole = document.getElementById('test-console');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'warn' ? 'warn' : type === 'success' ? 'success' : 'info';
            testConsole.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            testConsole.scrollTop = testConsole.scrollHeight;
            console.log(message);
        }
        
        function clearConsole() {
            testConsole.innerHTML = '';
        }
        
        async function testAPI(endpoint) {
            log(`🔍 Тестируем: ${endpoint}`, 'info');
            
            try {
                const startTime = Date.now();
                const response = await fetch(endpoint);
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                log(`📡 HTTP ${response.status} (${responseTime}ms)`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const contentType = response.headers.get('content-type');
                    log(`📋 Content-Type: ${contentType}`, 'info');
                    
                    if (contentType && contentType.includes('application/json')) {
                        try {
                            const data = await response.json();
                            log(`✅ JSON валиден, ключи: ${Object.keys(data).join(', ')}`, 'success');
                            
                            // Проверяем структуру ответа
                            if (data.success !== undefined) {
                                log(`🎯 Поле success: ${data.success}`, data.success ? 'success' : 'warn');
                            }
                            if (data.error) {
                                log(`⚠️ Ошибка в ответе: ${data.error}`, 'warn');
                            }
                            if (data.data) {
                                log(`📦 Данные получены: ${typeof data.data}`, 'success');
                            }
                            
                        } catch (jsonError) {
                            log(`❌ Ошибка парсинга JSON: ${jsonError.message}`, 'error');
                            const text = await response.text();
                            log(`📄 Содержимое: ${text.substring(0, 200)}...`, 'warn');
                        }
                    } else {
                        const text = await response.text();
                        log(`📄 Текстовый ответ (${text.length} символов)`, 'info');
                        if (text.length < 500) {
                            log(`📋 Содержимое: ${text}`, 'info');
                        }
                    }
                } else {
                    log(`❌ Ошибка HTTP: ${response.status} ${response.statusText}`, 'error');
                }
                
            } catch (error) {
                log(`🚨 Ошибка запроса: ${error.message}`, 'error');
            }
            
            log('─'.repeat(50), 'info');
        }
        
        async function testAllAPIs() {
            log('🚀 Начинаем комплексное тестирование API...', 'info');
            
            const apis = [
                'api/get-package.php?id=1',
                'api/get-package.php?type=flexible',
                'api/stats.php',
                'api/realtime.php?action=global_stats',
                'api/realtime.php?action=crypto_rates',
                'api/profit-chart.php',
                'api/project-details.php?id=1',
                'api/investment-details.php?id=1',
                'api/close-investment.php',
                'api/support.php'
            ];
            
            for (const api of apis) {
                await testAPI(api);
                await new Promise(resolve => setTimeout(resolve, 300)); // Пауза между запросами
            }
            
            log('🎉 Тестирование API завершено!', 'success');
        }
        
        async function testSpecificAPI() {
            log('🎯 Тестируем критически важные API...', 'info');

            // Тест get-package с разными параметрами
            await testAPI('api/get-package.php?id=1');
            await testAPI('api/get-package.php?id=999'); // Несуществующий пакет
            await testAPI('api/get-package.php?type=flexible');
            await testAPI('api/get-package.php'); // Без параметров

            // Тест статистики
            await testAPI('api/stats.php');

            // Тест realtime API
            await testAPI('api/realtime.php?action=global_stats');
            await testAPI('api/realtime.php?action=crypto_rates');
            await testAPI('api/realtime.php?action=invalid_action'); // Неверное действие

            // Тест проектов
            await testAPI('api/project-details.php?id=1');
            await testAPI('api/project-details.php?id=999'); // Несуществующий проект

            // Тест инвестиций (требует авторизации)
            await testAPI('api/profit-chart.php');
            await testAPI('api/investment-details.php?id=1');

            log('✅ Специфичное тестирование завершено!', 'success');
        }
        
        // Автозапуск при загрузке
        window.addEventListener('load', function() {
            log('🔌 Система тестирования API готова', 'success');
            log('👆 Выберите тип тестирования', 'info');
        });
    </script>
</body>
</html>
