<?php
$page_title = "Поддержка";

// Проверка авторизации для создания тикетов
$user = null;
if (isLoggedIn()) {
    $user = getCurrentUser();
    $user_tickets = getUserTickets($user['id']);
}

// Обработка создания тикета
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_ticket'])) {
    if (!isLoggedIn()) {
        redirect('index.php?page=login&redirect=' . urlencode($_SERVER['REQUEST_URI']));
    }
    
    $result = createSupportTicket($user['id']);
    if ($result['success']) {
        redirect('index.php?page=support', $result['message'], 'success');
    } else {
        $error_message = $result['message'];
    }
}

// Получение FAQ
$faq_items = getFAQItems();
?>

<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="page-header mb-4">
                <h2 class="page-title">
                    <i class="fas fa-headset text-primary"></i> Центр поддержки
                </h2>
                <p class="page-subtitle">Мы готовы помочь вам в любое время</p>
            </div>
        </div>
    </div>
    
    <!-- Быстрые контакты -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="contact-card">
                <div class="contact-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="contact-info">
                    <h6>Email поддержка</h6>
                    <p><EMAIL></p>
                    <small class="text-muted">Ответ в течение 24 часов</small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="contact-card">
                <div class="contact-icon">
                    <i class="fab fa-telegram"></i>
                </div>
                <div class="contact-info">
                    <h6>Telegram</h6>
                    <p>@greenchain_support</p>
                    <small class="text-muted">Быстрые ответы</small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="contact-card">
                <div class="contact-icon">
                    <i class="fas fa-phone"></i>
                </div>
                <div class="contact-info">
                    <h6>Телефон</h6>
                    <p>+****************</p>
                    <small class="text-muted">Пн-Пт 9:00-18:00 UTC</small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="contact-card">
                <div class="contact-icon">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="contact-info">
                    <h6>Онлайн чат</h6>
                    <p>Прямо на сайте</p>
                    <small class="text-muted">24/7 поддержка</small>
                </div>
            </div>
        </div>
    </div>
    
    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>
    
    <div class="row">
        <!-- Создание тикета -->
        <div class="col-lg-8">
            <?php if (isLoggedIn()): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-ticket-alt"></i> Создать тикет поддержки
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="needs-validation" novalidate>
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="subject" class="form-label">Тема обращения *</label>
                                    <input type="text" class="form-control" id="subject" name="subject" 
                                           required maxlength="200">
                                    <div class="invalid-feedback">
                                        Введите тему обращения
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="category" class="form-label">Категория *</label>
                                    <select class="form-select" id="category" name="category" required>
                                        <option value="">Выберите категорию</option>
                                        <option value="technical">Технические вопросы</option>
                                        <option value="financial">Финансовые вопросы</option>
                                        <option value="account">Проблемы с аккаунтом</option>
                                        <option value="investment">Вопросы по инвестициям</option>
                                        <option value="referral">Реферальная программа</option>
                                        <option value="other">Другое</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Выберите категорию
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="priority" class="form-label">Приоритет</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="low">Низкий</option>
                                    <option value="medium" selected>Средний</option>
                                    <option value="high">Высокий</option>
                                    <option value="urgent">Срочный</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="message" class="form-label">Сообщение *</label>
                                <textarea class="form-control" id="message" name="message" rows="6" 
                                          required maxlength="2000" placeholder="Опишите вашу проблему или вопрос подробно..."></textarea>
                                <div class="form-text">Максимум 2000 символов</div>
                                <div class="invalid-feedback">
                                    Введите сообщение
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" name="create_ticket" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> Отправить тикет
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Мои тикеты -->
                <?php if (!empty($user_tickets)): ?>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-list"></i> Мои тикеты
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Тема</th>
                                            <th>Категория</th>
                                            <th>Статус</th>
                                            <th>Создан</th>
                                            <th>Действия</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($user_tickets as $ticket): ?>
                                            <tr>
                                                <td>#<?php echo $ticket['id']; ?></td>
                                                <td><?php echo htmlspecialchars($ticket['subject']); ?></td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?php echo getCategoryName($ticket['category']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo getStatusColor($ticket['status']); ?>">
                                                        <?php echo getStatusName($ticket['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('d.m.Y H:i', strtotime($ticket['created_at'])); ?></td>
                                                <td>
                                                    <a href="index.php?page=ticket&id=<?php echo $ticket['id']; ?>" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i> Просмотр
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="card mb-4">
                    <div class="card-body text-center">
                        <i class="fas fa-user-lock fa-3x text-muted mb-3"></i>
                        <h5>Войдите в аккаунт</h5>
                        <p class="text-muted">Для создания тикета поддержки необходимо войти в систему</p>
                        <a href="index.php?page=login&redirect=<?php echo urlencode($_SERVER['REQUEST_URI']); ?>" 
                           class="btn btn-primary">
                            <i class="fas fa-sign-in-alt"></i> Войти
                        </a>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- FAQ -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-question-circle"></i> Часто задаваемые вопросы
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($faq_items)): ?>
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-question fa-2x mb-2"></i>
                            <p>FAQ скоро появится</p>
                        </div>
                    <?php else: ?>
                        <div class="accordion" id="faqAccordion">
                            <?php foreach ($faq_items as $index => $faq): ?>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="faq<?php echo $index; ?>">
                                        <button class="accordion-button <?php echo $index > 0 ? 'collapsed' : ''; ?>" 
                                                type="button" data-bs-toggle="collapse" 
                                                data-bs-target="#collapse<?php echo $index; ?>">
                                            <?php echo htmlspecialchars($faq['question']); ?>
                                        </button>
                                    </h2>
                                    <div id="collapse<?php echo $index; ?>" 
                                         class="accordion-collapse collapse <?php echo $index === 0 ? 'show' : ''; ?>" 
                                         data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            <?php echo nl2br(htmlspecialchars($faq['answer'])); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Боковая панель -->
        <div class="col-lg-4">
            <!-- Статус поддержки -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> Статус поддержки
                    </h5>
                </div>
                <div class="card-body">
                    <div class="support-status">
                        <div class="status-item">
                            <div class="status-indicator online"></div>
                            <div class="status-info">
                                <h6>Онлайн поддержка</h6>
                                <p class="text-muted">Доступна 24/7</p>
                            </div>
                        </div>
                        
                        <div class="status-item">
                            <div class="status-indicator online"></div>
                            <div class="status-info">
                                <h6>Email поддержка</h6>
                                <p class="text-muted">Ответ в течение 24 часов</p>
                            </div>
                        </div>
                        
                        <div class="status-item">
                            <div class="status-indicator away"></div>
                            <div class="status-info">
                                <h6>Телефонная поддержка</h6>
                                <p class="text-muted">Пн-Пт 9:00-18:00 UTC</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Полезные ссылки -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-link"></i> Полезные ссылки
                    </h5>
                </div>
                <div class="card-body">
                    <div class="useful-links">
                        <a href="index.php?page=education" class="useful-link">
                            <i class="fas fa-graduation-cap"></i>
                            <span>Обучающие материалы</span>
                        </a>
                        <a href="index.php?page=calculator" class="useful-link">
                            <i class="fas fa-calculator"></i>
                            <span>Калькулятор доходности</span>
                        </a>
                        <a href="index.php?page=terms" class="useful-link">
                            <i class="fas fa-file-contract"></i>
                            <span>Пользовательское соглашение</span>
                        </a>
                        <a href="index.php?page=privacy" class="useful-link">
                            <i class="fas fa-shield-alt"></i>
                            <span>Политика конфиденциальности</span>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Время работы -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock"></i> Время работы поддержки
                    </h5>
                </div>
                <div class="card-body">
                    <div class="working-hours">
                        <div class="hour-item">
                            <span class="day">Понедельник - Пятница</span>
                            <span class="time">9:00 - 18:00 UTC</span>
                        </div>
                        <div class="hour-item">
                            <span class="day">Суббота</span>
                            <span class="time">10:00 - 16:00 UTC</span>
                        </div>
                        <div class="hour-item">
                            <span class="day">Воскресенье</span>
                            <span class="time">Выходной</span>
                        </div>
                    </div>
                    
                    <div class="mt-3 p-3 bg-light rounded">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            Онлайн чат и email поддержка работают круглосуточно
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.contact-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    align-items: center;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.contact-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.contact-info h6 {
    margin: 0 0 0.25rem 0;
    font-weight: 600;
}

.contact-info p {
    margin: 0 0 0.25rem 0;
    font-weight: 600;
    color: var(--primary-color);
}

.support-status {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.status-item {
    display: flex;
    align-items: center;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 1rem;
    flex-shrink: 0;
}

.status-indicator.online {
    background: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.3);
}

.status-indicator.away {
    background: #ffc107;
    box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.3);
}

.status-indicator.offline {
    background: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.3);
}

.status-info h6 {
    margin: 0 0 0.25rem 0;
    font-weight: 600;
}

.status-info p {
    margin: 0;
    font-size: 0.9rem;
}

.useful-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.useful-link {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
    text-decoration: none;
    color: var(--dark-color);
    transition: all 0.3s ease;
}

.useful-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

.useful-link i {
    margin-right: 0.75rem;
    width: 20px;
}

.working-hours {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.hour-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.hour-item:last-child {
    border-bottom: none;
}

.day {
    font-weight: 600;
}

.time {
    color: #6c757d;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .contact-card {
        flex-direction: column;
        text-align: center;
    }
    
    .contact-icon {
        margin: 0 0 1rem 0;
    }
    
    .hour-item {
        flex-direction: column;
        text-align: center;
    }
    
    .time {
        margin-top: 0.25rem;
    }
}
</style>

<?php
/**
 * Создание тикета поддержки
 */
function createSupportTicket($user_id) {
    global $conn;
    
    // Проверка CSRF токена
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        return ['success' => false, 'message' => 'Ошибка безопасности. Попробуйте еще раз.'];
    }
    
    $subject = sanitizeInput($_POST['subject']);
    $category = sanitizeInput($_POST['category']);
    $priority = sanitizeInput($_POST['priority']);
    $message = sanitizeInput($_POST['message']);
    
    // Валидация
    if (empty($subject) || strlen($subject) < 5) {
        return ['success' => false, 'message' => 'Тема должна содержать минимум 5 символов'];
    }
    
    if (empty($message) || strlen($message) < 10) {
        return ['success' => false, 'message' => 'Сообщение должно содержать минимум 10 символов'];
    }
    
    try {
        $conn->beginTransaction();
        
        // Создаем тикет
        $stmt = $conn->prepare("
            INSERT INTO support_tickets (user_id, subject, category, priority, status) 
            VALUES (?, ?, ?, ?, 'open')
        ");
        $stmt->execute([$user_id, $subject, $category, $priority]);
        $ticket_id = $conn->lastInsertId();
        
        // Добавляем первое сообщение
        $stmt = $conn->prepare("
            INSERT INTO support_messages (ticket_id, user_id, message, is_staff) 
            VALUES (?, ?, ?, 0)
        ");
        $stmt->execute([$ticket_id, $user_id, $message]);
        
        // Создаем уведомление для администраторов
        $stmt = $conn->prepare("
            INSERT INTO notifications (user_id, title, message, type) 
            SELECT id, 'Новый тикет поддержки', ?, 'info'
            FROM users 
            WHERE role = 'admin'
        ");
        $stmt->execute(["Создан новый тикет #$ticket_id: $subject"]);
        
        // Логирование
        logAction('support_ticket_created', "Ticket ID: $ticket_id");
        
        $conn->commit();
        
        return [
            'success' => true,
            'message' => "Тикет #$ticket_id успешно создан. Мы ответим в ближайшее время."
        ];
        
    } catch (Exception $e) {
        $conn->rollBack();
        error_log("Create support ticket error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при создании тикета'];
    }
}

/**
 * Получение тикетов пользователя
 */
function getUserTickets($user_id) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT * FROM support_tickets 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 20
    ");
    $stmt->execute([$user_id]);
    
    return $stmt->fetchAll();
}

/**
 * Получение FAQ
 */
function getFAQItems() {
    global $conn;
    
    $stmt = $conn->query("
        SELECT * FROM faq 
        WHERE is_active = 1 
        ORDER BY sort_order, id
    ");
    
    return $stmt->fetchAll();
}

/**
 * Получение названия категории
 */
function getCategoryName($category) {
    $categories = [
        'technical' => 'Технические вопросы',
        'financial' => 'Финансовые вопросы',
        'account' => 'Проблемы с аккаунтом',
        'investment' => 'Вопросы по инвестициям',
        'referral' => 'Реферальная программа',
        'other' => 'Другое'
    ];
    
    return $categories[$category] ?? $category;
}
?>
