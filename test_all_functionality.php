<?php
// Тестирование всей функциональности GreenChain EcoFund
require_once 'config/config.php';
require_once 'config/session_config.php';
session_start();
require_once 'includes/functions.php';

echo "<h1>Полное тестирование функциональности GreenChain EcoFund</h1>";

// 1. Тест основных страниц
echo "<h2>1. Тест доступности страниц</h2>";

$pages_to_test = [
    'index.php' => 'Главная страница',
    'index.php?page=login' => 'Страница входа',
    'index.php?page=register' => 'Страница регистрации',
    'index.php?page=invest' => 'Страница инвестиций',
    'index.php?page=map' => 'Карта проектов',
    'index.php?page=education' => 'Образование',
    'index.php?page=calculator' => 'Калькулятор'
];

foreach ($pages_to_test as $url => $name) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "http://localhost/$url");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code == 200) {
        echo "✅ $name: OK<br>";
    } else {
        echo "❌ $name: HTTP $http_code<br>";
    }
}

// 2. Тест базы данных
echo "<h2>2. Тест структуры базы данных</h2>";

$required_tables = [
    'users' => 'Пользователи',
    'investment_packages' => 'Инвестиционные пакеты',
    'user_investments' => 'Инвестиции пользователей',
    'transactions' => 'Транзакции',
    'map_projects' => 'Проекты на карте',
    'education_categories' => 'Категории образования',
    'tasks' => 'Задания',
    'achievements' => 'Достижения',
    'settings' => 'Настройки системы'
];

foreach ($required_tables as $table => $description) {
    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        echo "✅ $description ($table): $count записей<br>";
    } catch (Exception $e) {
        echo "❌ $description ($table): Ошибка - " . $e->getMessage() . "<br>";
    }
}

// 3. Тест функций
echo "<h2>3. Тест основных функций</h2>";

$functions_to_test = [
    'hashPassword' => 'Хеширование пароля',
    'generateToken' => 'Генерация токена',
    'validateEmail' => 'Валидация email',
    'formatMoney' => 'Форматирование денег',
    'getInvestmentPackages' => 'Получение пакетов',
    'getMapProjects' => 'Получение проектов карты',
    'getEducationCategories' => 'Получение категорий образования',
    'getSetting' => 'Получение настроек'
];

foreach ($functions_to_test as $function => $description) {
    if (function_exists($function)) {
        try {
            switch ($function) {
                case 'hashPassword':
                    $result = hashPassword('test123');
                    echo "✅ $description: " . (strlen($result) > 50 ? 'OK' : 'Ошибка') . "<br>";
                    break;
                case 'generateToken':
                    $result = generateToken();
                    echo "✅ $description: " . (strlen($result) == 64 ? 'OK' : 'Ошибка') . "<br>";
                    break;
                case 'validateEmail':
                    $result = validateEmail('<EMAIL>');
                    echo "✅ $description: " . ($result ? 'OK' : 'Ошибка') . "<br>";
                    break;
                case 'formatMoney':
                    $result = formatMoney(1234.56);
                    echo "✅ $description: $result<br>";
                    break;
                case 'getInvestmentPackages':
                    $result = getInvestmentPackages();
                    echo "✅ $description: " . count($result) . " пакетов<br>";
                    break;
                case 'getMapProjects':
                    $result = getMapProjects();
                    echo "✅ $description: " . count($result) . " проектов<br>";
                    break;
                case 'getEducationCategories':
                    $result = getEducationCategories();
                    echo "✅ $description: " . count($result) . " категорий<br>";
                    break;
                case 'getSetting':
                    $result = getSetting('min_withdrawal', '10');
                    echo "✅ $description: $result<br>";
                    break;
            }
        } catch (Exception $e) {
            echo "❌ $description: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "❌ $description: Функция не найдена<br>";
    }
}

// 4. Тест API
echo "<h2>4. Тест API</h2>";

$api_endpoints = [
    'api/project-details.php?id=1' => 'Детали проекта'
];

foreach ($api_endpoints as $endpoint => $description) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "http://localhost/$endpoint");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code == 200) {
        $data = json_decode($response, true);
        if ($data && isset($data['success'])) {
            echo "✅ $description: " . ($data['success'] ? 'OK' : 'Ошибка API') . "<br>";
        } else {
            echo "❌ $description: Неверный формат ответа<br>";
        }
    } else {
        echo "❌ $description: HTTP $http_code<br>";
    }
}

// 5. Тест файловой системы
echo "<h2>5. Тест файловой системы</h2>";

$directories_to_check = [
    'uploads/avatars' => 'Папка аватаров',
    'uploads/documents' => 'Папка документов',
    'assets/css' => 'Папка CSS',
    'assets/js' => 'Папка JavaScript',
    'assets/images' => 'Папка изображений'
];

foreach ($directories_to_check as $dir => $description) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "✅ $description: Доступна для записи<br>";
        } else {
            echo "⚠️ $description: Только для чтения<br>";
        }
    } else {
        echo "❌ $description: Не существует<br>";
    }
}

// 6. Тест настроек
echo "<h2>6. Тест настроек системы</h2>";

$required_settings = [
    'min_withdrawal' => 'Минимальная сумма вывода',
    'withdrawal_fee' => 'Комиссия за вывод',
    'referral_rates' => 'Реферальные проценты'
];

foreach ($required_settings as $key => $description) {
    $value = getSetting($key);
    if ($value !== null) {
        echo "✅ $description: $value<br>";
    } else {
        echo "❌ $description: Не установлено<br>";
    }
}

echo "<h2>✅ Тестирование завершено!</h2>";
echo "<p><strong>Рекомендации:</strong></p>";
echo "<ul>";
echo "<li>Все основные страницы должны возвращать HTTP 200</li>";
echo "<li>Все таблицы базы данных должны существовать</li>";
echo "<li>Все функции должны работать без ошибок</li>";
echo "<li>API должны возвращать корректные JSON ответы</li>";
echo "<li>Папки uploads должны быть доступны для записи</li>";
echo "</ul>";

echo "<p><a href='index.php'>← Вернуться на главную</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #2c3e50; }
h2 { color: #34495e; border-bottom: 2px solid #ecf0f1; padding-bottom: 5px; }
ul { background: #f8f9fa; padding: 1rem; border-radius: 5px; }
</style>
