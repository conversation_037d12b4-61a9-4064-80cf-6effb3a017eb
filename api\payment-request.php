<?php
// API для создания заявки на пополнение баланса
// GreenChain EcoFund - Payment Request API

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/config.php';
require_once '../includes/functions.php';

// Проверка метода запроса
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Метод не разрешен']);
    exit;
}

// Проверка авторизации
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Необходима авторизация']);
    exit;
}

try {
    // Получение данных запроса
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $input = $_POST;
    }

    // Валидация входных данных
    $amount = floatval($input['amount'] ?? 0);
    $currency = $input['currency'] ?? 'USDT';
    $network = $input['network'] ?? 'TRC-20';
    $transaction_hash = trim($input['transaction_hash'] ?? '');
    $wallet_address = trim($input['wallet_address'] ?? '');

    // Проверка обязательных полей
    if ($amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'Некорректная сумма']);
        exit;
    }

    if (empty($transaction_hash)) {
        echo json_encode(['success' => false, 'message' => 'Необходимо указать хеш транзакции']);
        exit;
    }

    // Получение настроек системы
    $min_deposit = floatval(getSetting('payment_min_deposit', 10));
    $max_deposit = floatval(getSetting('payment_max_deposit', 10000));
    $system_enabled = getSetting('payment_system_enabled', 1);

    if (!$system_enabled) {
        echo json_encode(['success' => false, 'message' => 'Платежная система временно недоступна']);
        exit;
    }

    // Проверка лимитов
    if ($amount < $min_deposit) {
        echo json_encode(['success' => false, 'message' => "Минимальная сумма пополнения: $min_deposit USDT"]);
        exit;
    }

    if ($amount > $max_deposit) {
        echo json_encode(['success' => false, 'message' => "Максимальная сумма пополнения: $max_deposit USDT"]);
        exit;
    }

    // Проверка на дублирование транзакции
    $stmt = $conn->prepare("SELECT id FROM payment_requests WHERE transaction_hash = ? AND status != 'cancelled'");
    $stmt->execute([$transaction_hash]);
    
    if ($stmt->rowCount() > 0) {
        echo json_encode(['success' => false, 'message' => 'Транзакция с таким хешем уже существует']);
        exit;
    }

    // Получение ID текущего пользователя
    $user = getCurrentUser();
    $user_id = $user['id'];

    // Создание заявки на пополнение
    $stmt = $conn->prepare("
        INSERT INTO payment_requests 
        (user_id, amount, currency, network, transaction_hash, wallet_address, status, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW())
    ");

    if ($stmt->execute([$user_id, $amount, $currency, $network, $transaction_hash, $wallet_address])) {
        $request_id = $conn->lastInsertId();

        // Логирование операции
        logTransaction($user_id, 'deposit_request', $amount, "Заявка на пополнение #$request_id", $request_id, 'payment_request');

        // Проверка на автоматическое одобрение (для небольших сумм)
        $auto_approve_limit = floatval(getSetting('payment_auto_approve_limit', 100));
        
        $response = [
            'success' => true,
            'message' => 'Заявка на пополнение создана успешно',
            'request_id' => $request_id,
            'amount' => $amount,
            'currency' => $currency,
            'network' => $network,
            'status' => 'pending',
            'auto_approve' => $amount <= $auto_approve_limit
        ];

        // Если сумма меньше лимита автоодобрения, можно добавить логику автоматического одобрения
        if ($amount <= $auto_approve_limit) {
            $response['message'] .= '. Заявка будет обработана в ближайшее время.';
        } else {
            $response['message'] .= '. Заявка отправлена на ручную проверку администратором.';
        }

        echo json_encode($response);

    } else {
        echo json_encode(['success' => false, 'message' => 'Ошибка создания заявки']);
    }

} catch (Exception $e) {
    error_log("Payment Request Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Внутренняя ошибка сервера']);
}

// Функция для логирования транзакций
function logTransaction($user_id, $type, $amount, $description, $reference_id = null, $reference_type = null) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO transactions 
            (user_id, type, amount, description, reference_id, reference_type, status, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW())
        ");
        
        $stmt->execute([$user_id, $type, $amount, $description, $reference_id, $reference_type]);
        return $conn->lastInsertId();
    } catch (Exception $e) {
        error_log("Transaction Log Error: " . $e->getMessage());
        return false;
    }
}

// Функция для получения настроек (если не существует в functions.php)
if (!function_exists('getSetting')) {
    function getSetting($key, $default = null) {
        global $conn;
        
        try {
            $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
            $stmt->execute([$key]);
            $result = $stmt->fetch();
            
            return $result ? $result['setting_value'] : $default;
        } catch (Exception $e) {
            return $default;
        }
    }
}
?>
