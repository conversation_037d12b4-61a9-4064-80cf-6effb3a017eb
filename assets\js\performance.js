// Оптимизация производительности для GreenChain <PERSON>

// Ленивая загрузка изображений
class LazyImageLoader {
    constructor() {
        this.imageObserver = null;
        this.init();
    }
    
    init() {
        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        this.loadImage(img);
                        observer.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });
            
            this.observeImages();
        } else {
            // Fallback для старых браузеров
            this.loadAllImages();
        }
    }
    
    observeImages() {
        const lazyImages = document.querySelectorAll('img[data-src], img[loading="lazy"]');
        lazyImages.forEach(img => {
            this.imageObserver.observe(img);
        });
    }
    
    loadImage(img) {
        if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
        }
        
        img.addEventListener('load', () => {
            img.classList.add('loaded');
        });
        
        img.addEventListener('error', () => {
            img.src = 'assets/images/placeholder.png';
            img.classList.add('error');
        });
    }
    
    loadAllImages() {
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => this.loadImage(img));
    }
    
    // Добавление новых изображений для наблюдения
    addImages(container = document) {
        if (this.imageObserver) {
            const newImages = container.querySelectorAll('img[data-src]:not(.observed)');
            newImages.forEach(img => {
                img.classList.add('observed');
                this.imageObserver.observe(img);
            });
        }
    }
}

// Дебаунс функция
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

// Троттлинг функция
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Оптимизация скролла
class ScrollOptimizer {
    constructor() {
        this.isScrolling = false;
        this.scrollCallbacks = [];
        this.init();
    }
    
    init() {
        // Используем passive listeners для лучшей производительности
        window.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
        
        // Оптимизированный обработчик скролла
        this.optimizedScrollHandler = throttle(() => {
            this.scrollCallbacks.forEach(callback => callback());
        }, 16); // ~60fps
    }
    
    handleScroll() {
        if (!this.isScrolling) {
            requestAnimationFrame(() => {
                this.optimizedScrollHandler();
                this.isScrolling = false;
            });
            this.isScrolling = true;
        }
    }
    
    addScrollCallback(callback) {
        this.scrollCallbacks.push(callback);
    }
    
    removeScrollCallback(callback) {
        const index = this.scrollCallbacks.indexOf(callback);
        if (index > -1) {
            this.scrollCallbacks.splice(index, 1);
        }
    }
}

// Оптимизация ресайза
class ResizeOptimizer {
    constructor() {
        this.resizeCallbacks = [];
        this.init();
    }
    
    init() {
        const optimizedResizeHandler = debounce(() => {
            this.resizeCallbacks.forEach(callback => callback());
        }, 250);
        
        window.addEventListener('resize', optimizedResizeHandler, { passive: true });
    }
    
    addResizeCallback(callback) {
        this.resizeCallbacks.push(callback);
    }
    
    removeResizeCallback(callback) {
        const index = this.resizeCallbacks.indexOf(callback);
        if (index > -1) {
            this.resizeCallbacks.splice(index, 1);
        }
    }
}

// Предзагрузка критических ресурсов
class ResourcePreloader {
    constructor() {
        this.preloadedResources = new Set();
    }
    
    preloadCSS(href) {
        if (this.preloadedResources.has(href)) return;
        
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = href;
        link.onload = () => {
            link.rel = 'stylesheet';
        };
        document.head.appendChild(link);
        
        this.preloadedResources.add(href);
    }
    
    preloadJS(src) {
        if (this.preloadedResources.has(src)) return;
        
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'script';
        link.href = src;
        document.head.appendChild(link);
        
        this.preloadedResources.add(src);
    }
    
    preloadImage(src) {
        if (this.preloadedResources.has(src)) return;
        
        const img = new Image();
        img.src = src;
        
        this.preloadedResources.add(src);
    }
    
    preloadFont(href) {
        if (this.preloadedResources.has(href)) return;
        
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'font';
        link.type = 'font/woff2';
        link.crossOrigin = 'anonymous';
        link.href = href;
        document.head.appendChild(link);
        
        this.preloadedResources.add(href);
    }
}

// Кэширование DOM элементов
class DOMCache {
    constructor() {
        this.cache = new Map();
    }
    
    get(selector) {
        if (!this.cache.has(selector)) {
            const element = document.querySelector(selector);
            if (element) {
                this.cache.set(selector, element);
            }
        }
        return this.cache.get(selector);
    }
    
    getAll(selector) {
        const cacheKey = `all:${selector}`;
        if (!this.cache.has(cacheKey)) {
            const elements = document.querySelectorAll(selector);
            this.cache.set(cacheKey, elements);
        }
        return this.cache.get(cacheKey);
    }
    
    clear() {
        this.cache.clear();
    }
    
    remove(selector) {
        this.cache.delete(selector);
        this.cache.delete(`all:${selector}`);
    }
}

// Оптимизация анимаций
class AnimationOptimizer {
    constructor() {
        this.activeAnimations = new Set();
        this.init();
    }
    
    init() {
        // Проверяем поддержку CSS анимаций
        this.supportsCSS = this.checkCSSSupport();
        
        // Отключаем анимации при низкой производительности
        this.checkPerformance();
    }
    
    checkCSSSupport() {
        const testElement = document.createElement('div');
        return 'animation' in testElement.style || 
               'webkitAnimation' in testElement.style;
    }
    
    checkPerformance() {
        // Проверяем производительность устройства
        if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
            document.body.classList.add('low-performance');
        }
        
        // Проверяем предпочтения пользователя
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.body.classList.add('reduced-motion');
        }
    }
    
    animate(element, keyframes, options = {}) {
        if (!this.supportsCSS || document.body.classList.contains('reduced-motion')) {
            return Promise.resolve();
        }
        
        const animation = element.animate(keyframes, {
            duration: 300,
            easing: 'ease-out',
            fill: 'forwards',
            ...options
        });
        
        this.activeAnimations.add(animation);
        
        animation.addEventListener('finish', () => {
            this.activeAnimations.delete(animation);
        });
        
        return animation.finished;
    }
    
    cancelAllAnimations() {
        this.activeAnimations.forEach(animation => {
            animation.cancel();
        });
        this.activeAnimations.clear();
    }
}

// Менеджер производительности
class PerformanceManager {
    constructor() {
        this.lazyLoader = new LazyImageLoader();
        this.scrollOptimizer = new ScrollOptimizer();
        this.resizeOptimizer = new ResizeOptimizer();
        this.preloader = new ResourcePreloader();
        this.domCache = new DOMCache();
        this.animationOptimizer = new AnimationOptimizer();
        
        this.init();
    }
    
    init() {
        // Предзагружаем критические ресурсы
        this.preloadCriticalResources();
        
        // Оптимизируем загрузку страницы
        this.optimizePageLoad();
        
        // Мониторинг производительности
        this.monitorPerformance();
        
        // Очистка при выгрузке страницы
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }
    
    preloadCriticalResources() {
        // Предзагружаем шрифты
        this.preloader.preloadFont('https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2');
        
        // Предзагружаем критические изображения
        this.preloader.preloadImage('assets/images/logo.png');
        this.preloader.preloadImage('assets/images/hero-bg.jpg');
    }
    
    optimizePageLoad() {
        // Откладываем загрузку некритических ресурсов
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.loadNonCriticalResources();
            });
        } else {
            this.loadNonCriticalResources();
        }
    }
    
    loadNonCriticalResources() {
        // Загружаем некритические CSS
        setTimeout(() => {
            this.preloader.preloadCSS('https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css');
        }, 1000);
        
        // Загружаем некритические JS
        setTimeout(() => {
            this.preloader.preloadJS('assets/js/analytics.js');
        }, 2000);
    }
    
    monitorPerformance() {
        // Мониторинг FPS
        this.monitorFPS();
        
        // Мониторинг памяти
        this.monitorMemory();
        
        // Мониторинг сетевых запросов
        this.monitorNetwork();
    }
    
    monitorFPS() {
        let lastTime = performance.now();
        let frames = 0;
        
        const measureFPS = (currentTime) => {
            frames++;
            if (currentTime >= lastTime + 1000) {
                const fps = Math.round((frames * 1000) / (currentTime - lastTime));
                
                if (fps < 30) {
                    console.warn('Low FPS detected:', fps);
                    document.body.classList.add('low-fps');
                }
                
                frames = 0;
                lastTime = currentTime;
            }
            requestAnimationFrame(measureFPS);
        };
        
        requestAnimationFrame(measureFPS);
    }
    
    monitorMemory() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                const usedMB = Math.round(memory.usedJSHeapSize / 1048576);
                const limitMB = Math.round(memory.jsHeapSizeLimit / 1048576);
                
                if (usedMB / limitMB > 0.8) {
                    console.warn('High memory usage:', usedMB, 'MB of', limitMB, 'MB');
                    this.optimizeMemory();
                }
            }, 30000); // Проверяем каждые 30 секунд
        }
    }
    
    monitorNetwork() {
        if ('connection' in navigator) {
            const connection = navigator.connection;
            
            if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                document.body.classList.add('slow-connection');
                console.log('Slow connection detected, optimizing...');
            }
            
            connection.addEventListener('change', () => {
                if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                    document.body.classList.add('slow-connection');
                } else {
                    document.body.classList.remove('slow-connection');
                }
            });
        }
    }
    
    optimizeMemory() {
        // Очищаем кэш DOM
        this.domCache.clear();
        
        // Отменяем все анимации
        this.animationOptimizer.cancelAllAnimations();
        
        // Принудительная сборка мусора (если доступна)
        if (window.gc) {
            window.gc();
        }
    }
    
    cleanup() {
        this.animationOptimizer.cancelAllAnimations();
        this.domCache.clear();
    }
    
    // Публичные методы для использования в других частях приложения
    addScrollCallback(callback) {
        this.scrollOptimizer.addScrollCallback(callback);
    }
    
    addResizeCallback(callback) {
        this.resizeOptimizer.addResizeCallback(callback);
    }
    
    preloadResource(type, src) {
        switch (type) {
            case 'css':
                this.preloader.preloadCSS(src);
                break;
            case 'js':
                this.preloader.preloadJS(src);
                break;
            case 'image':
                this.preloader.preloadImage(src);
                break;
            case 'font':
                this.preloader.preloadFont(src);
                break;
        }
    }
    
    animate(element, keyframes, options) {
        return this.animationOptimizer.animate(element, keyframes, options);
    }
    
    getElement(selector) {
        return this.domCache.get(selector);
    }
    
    getAllElements(selector) {
        return this.domCache.getAll(selector);
    }
}

// Инициализация менеджера производительности
let performanceManager;

document.addEventListener('DOMContentLoaded', () => {
    performanceManager = new PerformanceManager();
    
    // Делаем доступным глобально
    window.performanceManager = performanceManager;
});

// Экспорт для использования в других модулях
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        PerformanceManager,
        LazyImageLoader,
        debounce,
        throttle
    };
}
