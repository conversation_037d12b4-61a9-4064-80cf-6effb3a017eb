<?php
$page_title = "Повторная отправка подтверждения";

// Если пользователь уже авторизован, перенаправляем в личный кабинет
if (isLoggedIn()) {
    redirect('index.php?page=dashboard');
}

// Обработка формы
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $result = processResendVerification();
    if ($result['success']) {
        $success_message = $result['message'];
    } else {
        $error_message = $result['message'];
    }
}
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-5 col-md-7">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient text-white text-center">
                    <h3 class="mb-0">
                        <i class="fas fa-paper-plane"></i> Повторная отправка
                    </h3>
                    <p class="mb-0 mt-2">Отправим ссылку подтверждения повторно</p>
                </div>
                
                <div class="card-body p-4">
                    <?php if (isset($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($success_message)): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email адрес</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                       required>
                            </div>
                            <div class="form-text">
                                Введите email, указанный при регистрации
                            </div>
                            <div class="invalid-feedback">
                                Введите корректный email адрес
                            </div>
                        </div>
                        
                        <!-- Капча -->
                        <?php
                        $num1 = rand(1, 10);
                        $num2 = rand(1, 10);
                        $captcha_answer = $num1 + $num2;
                        $_SESSION['captcha_answer'] = $captcha_answer;
                        ?>
                        <div class="mb-3">
                            <label for="captcha" class="form-label">
                                Решите пример: <?php echo $num1; ?> + <?php echo $num2; ?> = ?
                            </label>
                            <input type="number" class="form-control" id="captcha" name="captcha" 
                                   required min="0" max="20">
                            <div class="invalid-feedback">
                                Решите математический пример
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane"></i> Отправить ссылку
                            </button>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-2">Уже подтвердили email?</p>
                        <a href="index.php?page=login" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt"></i> Войти в систему
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Информация -->
            <div class="card mt-4 border-0 bg-light">
                <div class="card-body text-center">
                    <h6 class="card-title">
                        <i class="fas fa-info-circle text-info"></i> Не получили письмо?
                    </h6>
                    <ul class="list-unstyled text-muted small mb-0">
                        <li>• Проверьте папку "Спам" или "Нежелательная почта"</li>
                        <li>• Убедитесь, что email адрес указан правильно</li>
                        <li>• Письмо может прийти в течение 5-10 минут</li>
                        <li>• Обратитесь в поддержку, если проблема не решается</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
/**
 * Обработка повторной отправки подтверждения
 */
function processResendVerification() {
    global $conn;
    
    // Проверка CSRF токена
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        return ['success' => false, 'message' => 'Ошибка безопасности. Попробуйте еще раз.'];
    }
    
    $email = sanitizeInput($_POST['email']);
    $captcha = intval($_POST['captcha']);
    
    // Валидация
    if (!validateEmail($email)) {
        return ['success' => false, 'message' => 'Некорректный email адрес'];
    }
    
    // Проверка капчи
    if (!isset($_SESSION['captcha_answer']) || $captcha !== $_SESSION['captcha_answer']) {
        return ['success' => false, 'message' => 'Неверно решен пример'];
    }
    
    // Очищаем капчу
    unset($_SESSION['captcha_answer']);
    
    try {
        // Поиск пользователя
        $stmt = $conn->prepare("
            SELECT id, first_name, email_verified, created_at
            FROM users 
            WHERE email = ? AND status != 'banned'
        ");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if (!$user) {
            // Не раскрываем информацию о существовании email
            return ['success' => true, 'message' => 'Если указанный email зарегистрирован и не подтвержден, на него будет отправлена ссылка подтверждения.'];
        }
        
        if ($user['email_verified']) {
            return ['success' => false, 'message' => 'Email уже подтвержден. Вы можете войти в систему.'];
        }
        
        // Проверка лимита отправки (не более 3 раз в час)
        $stmt = $conn->prepare("
            SELECT COUNT(*) as send_count
            FROM activity_logs 
            WHERE user_id = ? 
            AND action = 'verification_email_sent'
            AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ");
        $stmt->execute([$user['id']]);
        $send_count = $stmt->fetch()['send_count'];
        
        if ($send_count >= 3) {
            return ['success' => false, 'message' => 'Превышен лимит отправки. Попробуйте через час.'];
        }
        
        // Генерация нового токена
        $verification_token = generateToken();
        
        // Обновление токена
        $stmt = $conn->prepare("
            UPDATE users 
            SET email_verification_token = ? 
            WHERE id = ?
        ");
        $stmt->execute([$verification_token, $user['id']]);
        
        // Отправка email
        $result = sendVerificationEmail($email, $user['first_name'], $verification_token);
        
        if ($result) {
            // Логирование
            logAction('verification_email_sent', "Email: {$email}");
            
            return ['success' => true, 'message' => 'Ссылка подтверждения отправлена на ваш email.'];
        } else {
            return ['success' => false, 'message' => 'Ошибка при отправке email. Попробуйте позже.'];
        }
        
    } catch (Exception $e) {
        error_log("Resend verification error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при обработке запроса. Попробуйте позже.'];
    }
}

/**
 * Отправка email подтверждения
 */
function sendVerificationEmail($email, $name, $token) {
    $subject = 'Подтверждение регистрации - GreenChain EcoFund';
    $verification_link = SITE_URL . "/index.php?page=verify&token=" . $token;
    
    $message = "
    <h2>Подтверждение регистрации</h2>
    <p>Здравствуйте, {$name}!</p>
    <p>Для завершения регистрации на платформе GreenChain EcoFund, пожалуйста, подтвердите ваш email адрес:</p>
    <p><a href='{$verification_link}' style='background: #2E8B57; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Подтвердить email</a></p>
    <p>Если кнопка не работает, скопируйте и вставьте эту ссылку в браузер:</p>
    <p>{$verification_link}</p>
    <p>После подтверждения вы получите бонус $5.00 на ваш баланс!</p>
    <p>С уважением,<br>Команда GreenChain EcoFund</p>
    ";
    
    return sendEmail($email, $subject, $message, true);
}
?>
