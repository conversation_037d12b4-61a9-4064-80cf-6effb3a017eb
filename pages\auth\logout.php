<?php
// Выход из системы
// Конфигурация уже подключена в index.php

// Сессия уже запущена в index.php

require_once __DIR__ . '/../../includes/functions.php';

// Логирование выхода
if (isLoggedIn()) {
    try {
        logAction('user_logout', "User ID: {$_SESSION['user_id']}");
    } catch (Exception $e) {
        error_log("Error logging logout: " . $e->getMessage());
    }
}

// Очистка remember token из cookie и БД
if (isset($_COOKIE['remember_token'])) {
    $remember_token = $_COOKIE['remember_token'];

    // Удаляем токен из БД
    try {
        $stmt = $conn->prepare("
            UPDATE users
            SET remember_token = NULL, remember_token_expires = NULL
            WHERE remember_token = ?
        ");
        $stmt->execute([$remember_token]);
    } catch (Exception $e) {
        error_log("Error clearing remember token: " . $e->getMessage());
    }

    // Удаляем cookie
    setcookie('remember_token', '', time() - 3600, '/', '', false, true);
}

// Очистка сессии
session_unset();
session_destroy();

// Запуск новой сессии для flash сообщения
session_start();

// Установка flash сообщения
$_SESSION['flash_message'] = 'Вы успешно вышли из системы';
$_SESSION['flash_type'] = 'success';

// Перенаправление на главную страницу
header('Location: ../../index.php');
exit();
?>
