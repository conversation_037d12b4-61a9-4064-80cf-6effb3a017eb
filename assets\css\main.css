/* ===== GREENCHAIN ECOFUND - НОВЫЙ СОВРЕМЕННЫЙ ДИЗАЙН ===== */

/* ===== ИМПОРТ ШРИФТОВ ===== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* ===== CSS ПЕРЕМЕННЫЕ - СОВРЕМЕННАЯ ЗЕЛЕНАЯ ПАЛИТРА ===== */
:root {
    /* Основные зеленые цвета для градиента */
    --green-darkest: #0f2419;
    --green-dark: #1a3d2e;
    --green-medium-dark: #2d5a3d;
    --green-medium: #16844a;
    --green-light: #22c55e;
    --green-lightest: #bbf7d0;

    /* Текстовые цвета для WCAG AAA соответствия */
    --text-white: #ffffff;
    --text-light: #f8fafc;
    --text-very-light: #f1f5f9;
    --text-light-green: #dcfce7;
    
    /* Основной вертикальный градиент сайта */
    --main-gradient: linear-gradient(180deg, 
        var(--green-darkest) 0%, 
        var(--green-dark) 15%, 
        var(--green-medium-dark) 35%, 
        var(--green-medium) 65%, 
        var(--green-light) 85%, 
        var(--green-lightest) 100%);
    
    /* Градиенты для элементов */
    --gradient-primary: linear-gradient(135deg, var(--green-medium) 0%, var(--green-dark) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--green-light) 0%, var(--green-medium) 100%);
    --gradient-accent: linear-gradient(135deg, var(--green-medium-dark) 0%, var(--green-darkest) 100%);
    
    /* Тени */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-green: 0 0 20px rgba(22, 132, 74, 0.3);
    --shadow-green-glow: 0 0 30px rgba(22, 132, 74, 0.4);
    
    /* Переходы */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Размеры */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    
    /* Шрифты */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
}

/* ===== БАЗОВЫЕ СТИЛИ ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-white);
    background: var(--main-gradient);
    background-attachment: fixed;
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== ТИПОГРАФИКА ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-primary);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: var(--text-white) !important;
}

h1 { 
    font-size: var(--font-size-5xl); 
    font-weight: 800;
}

h2 { 
    font-size: var(--font-size-4xl); 
    font-weight: 700;
}

h3 { 
    font-size: var(--font-size-3xl); 
    font-weight: 600;
}

h4 { 
    font-size: var(--font-size-2xl); 
    font-weight: 600;
}

h5 { 
    font-size: var(--font-size-xl); 
    font-weight: 500;
}

h6 { 
    font-size: var(--font-size-lg); 
    font-weight: 500;
}

p {
    margin-bottom: 1rem;
    color: var(--text-light) !important;
    line-height: 1.6;
}

a {
    color: var(--text-very-light);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--text-white);
    text-decoration: none;
}

/* ===== СПЕЦИАЛЬНЫЕ АНИМАЦИИ ДЛЯ ЛОГОТИПА И ЗАГОЛОВКОВ ===== */

/* Анимированный логотип */
.brand-text,
.navbar-brand .brand-text,
.brand-text-modern {
    background: linear-gradient(45deg, var(--text-white), var(--green-light), var(--text-white));
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: logoShimmer 3s ease-in-out infinite, logoGlow 2s ease-in-out infinite alternate;
    font-weight: 800 !important;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

/* Анимированные заголовки */
.hero-title,
.section-title,
.main-title {
    animation: titleFloat 4s ease-in-out infinite, titleGlow 3s ease-in-out infinite alternate;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.4);
}

/* Ключевые кадры анимаций */
@keyframes logoShimmer {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes logoGlow {
    0% {
        filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
    }
    100% {
        filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.6));
    }
}

@keyframes titleFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-5px);
    }
}

@keyframes titleGlow {
    0% {
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
    }
    100% {
        text-shadow: 0 0 40px rgba(255, 255, 255, 0.6);
    }
}

/* ===== УНИФИЦИРОВАННЫЕ СТИЛИ ДЛЯ SVG ИКОНОК ===== */

/* Базовые размеры для SVG иконок */
:root {
    --icon-size-sm: 24px;      /* Навигационные иконки */
    --icon-size-md: 32px;      /* Стандартные иконки */
    --icon-size-lg: 48px;      /* Основные иконки */
    --icon-size-xl: 64px;      /* Большие иконки */
}

/* Унифицированные стили для всех SVG */
svg,
.nav-icon,
.icon,
.feature-icon-modern svg,
i[class*="fa-"] {
    width: var(--icon-size-md);
    height: var(--icon-size-md);
    transition: all var(--transition-normal);
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.2));
    flex-shrink: 0;
}

/* Навигационные иконки */
.nav-icon,
.navbar svg,
header svg {
    width: var(--icon-size-sm);
    height: var(--icon-size-sm);
}

/* Основные иконки в карточках */
.feature-icon-modern svg,
.feature-icon svg,
.investment-card svg {
    width: var(--icon-size-lg);
    height: var(--icon-size-lg);
}

/* Большие иконки для hero секции */
.hero-image-modern svg,
.hero-content-modern svg {
    width: var(--icon-size-xl);
    height: var(--icon-size-xl);
}

/* Анимации при наведении */
svg:hover,
.nav-icon:hover,
.icon:hover,
i[class*="fa-"]:hover {
    transform: scale(1.1);
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.5));
    animation: iconPulse 1s ease-in-out infinite;
}

/* Плавающие иконки */
.floating-icon {
    animation: iconFloat 3s ease-in-out infinite;
}

.floating-icon:nth-child(2n) {
    animation-delay: 1s;
}

.floating-icon:nth-child(3n) {
    animation-delay: 2s;
}

/* Ключевые кадры для иконок */
@keyframes iconPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1.1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.15);
    }
}

@keyframes iconFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Адаптивные размеры иконок */
@media (max-width: 768px) {
    :root {
        --icon-size-sm: 20px;
        --icon-size-md: 28px;
        --icon-size-lg: 40px;
        --icon-size-xl: 56px;
    }
}

@media (max-width: 576px) {
    :root {
        --icon-size-sm: 18px;
        --icon-size-md: 24px;
        --icon-size-lg: 36px;
        --icon-size-xl: 48px;
    }
}

@keyframes iconFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-8px) rotate(2deg);
    }
    66% {
        transform: translateY(4px) rotate(-1deg);
    }
}

/* ===== СОВРЕМЕННЫЕ КНОПКИ ===== */
.btn {
    border-radius: var(--border-radius-lg) !important;
    font-weight: 600 !important;
    padding: 0.875rem 2rem !important;
    transition: all var(--transition-normal) !important;
    border: none;
    font-family: var(--font-family-primary);
    text-transform: none;
    letter-spacing: 0.025em;
}

.btn-primary {
    background: var(--gradient-primary) !important;
    color: var(--text-white) !important;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-3px) !important;
    box-shadow: var(--shadow-green-glow) !important;
    color: var(--text-white) !important;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 2px solid var(--text-white) !important;
    color: var(--text-white) !important;
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: var(--text-white) !important;
    border-color: var(--text-white) !important;
    transform: translateY(-3px) !important;
    color: var(--green-dark) !important;
    box-shadow: var(--shadow-lg);
}

/* ===== СОВРЕМЕННЫЕ КАРТОЧКИ ===== */
.card,
.feature-card,
.stats-card {
    background: rgba(255, 255, 255, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important; /* Максимум 8px для геометрической правильности */
    box-shadow: var(--shadow-lg) !important;
    transition: all var(--transition-normal) !important;
    backdrop-filter: blur(15px);
    color: var(--green-dark) !important;
    /* Геометрическая правильность */
    position: relative;
    overflow: hidden;
}

.card:hover,
.feature-card:hover,
.stats-card:hover {
    transform: translateY(-8px) !important;
    box-shadow: var(--shadow-xl) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

/* ===== КОНТЕЙНЕРЫ И СЕКЦИИ - СОВМЕСТИМОСТЬ С РАСШИРЕННЫМИ КОНТЕЙНЕРАМИ ===== */
.container {
    max-width: 1400px; /* Увеличено с 1200px до 1400px */
    margin: 0 auto;
    padding: 0 2rem;
    width: 100%;
    position: relative;
}

/* Адаптивные контейнеры для разных размеров экрана */
@media (min-width: 1600px) {
    .container {
        max-width: 1600px;
        padding: 0 3rem;
    }
}

@media (min-width: 1400px) and (max-width: 1599px) {
    .container {
        max-width: 1400px;
        padding: 0 2.5rem;
    }
}

@media (max-width: 1199px) {
    .container {
        max-width: 1200px;
        padding: 0 1.5rem;
    }
}

section {
    padding: 5rem 0;
    position: relative;
    width: 100%;
    margin: 0;
    /* Геометрическая правильность секций */
    border-radius: 0 !important; /* Убираем скругления секций */
    overflow: hidden;
    /* Четкие границы между секциями */
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.main-content {
    min-height: calc(100vh - 80px);
    padding-top: 80px;
    width: 100%;
    margin: 0;
    position: relative;
}

/* ===== УТИЛИТАРНЫЕ КЛАССЫ ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-white { color: var(--text-white) !important; }
.text-light { color: var(--text-light) !important; }
.text-very-light { color: var(--text-very-light) !important; }

.bg-transparent { background: transparent !important; }
.bg-glass {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-green { box-shadow: var(--shadow-green); }
.shadow-green-glow { box-shadow: var(--shadow-green-glow); }

/* ===== АДАПТИВНЫЕ СТИЛИ ===== */
@media (max-width: 1200px) {
    .container {
        max-width: 100%;
        padding: 0 2rem;
    }

    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }
}

@media (max-width: 992px) {
    h1 { font-size: 2.2rem; }
    h2 { font-size: 1.8rem; }
    h3 { font-size: 1.5rem; }

    .btn {
        padding: 0.75rem 1.5rem !important;
        font-size: 0.9rem !important;
    }

    section {
        padding: 3rem 0;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }

    h1 { font-size: 1.8rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.3rem; }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    section {
        padding: 2rem 0;
    }
}

@media (max-width: 576px) {
    h1 { font-size: 1.5rem; }
    h2 { font-size: 1.3rem; }

    .card,
    .feature-card,
    .stats-card {
        margin-bottom: 1rem;
    }
}

/* ===== ДОПОЛНИТЕЛЬНЫЕ АНИМАЦИИ ===== */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fadeIn { animation: fadeIn 0.8s ease-out; }
.animate-fadeInUp { animation: fadeInUp 1s ease-out; }
.animate-slideInLeft { animation: slideInLeft 0.8s ease-out; }

/* ===== СОВРЕМЕННАЯ НАВИГАЦИЯ ===== */

/* Основной контейнер навигации */
.modern-header {
    background: rgba(15, 36, 25, 0.95) !important; /* Темно-зеленый с прозрачностью */
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all var(--transition-normal);
}

.modern-navbar {
    padding: 1rem 0;
}

/* Логотип */
.modern-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    transition: all var(--transition-normal);
}

.brand-logo-modern {
    font-size: 2rem;
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.brand-text-modern {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--text-white) !important;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

/* Навигационные кнопки */
.nav-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px; /* Максимум 8px */
    color: var(--text-white) !important;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
    min-height: 44px; /* Минимальная высота для удобства */
    justify-content: center;
}

.nav-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    color: var(--text-white) !important;
    text-decoration: none;
}

/* Специальные стили для разных типов кнопок */
.nav-button.referrals {
    background: linear-gradient(135deg, var(--green-medium) 0%, var(--green-dark) 100%);
    border-color: var(--green-medium);
    box-shadow: 0 0 15px rgba(22, 132, 74, 0.3);
}

.nav-button.referrals:hover {
    background: linear-gradient(135deg, var(--green-light) 0%, var(--green-medium) 100%);
    box-shadow: 0 0 25px rgba(22, 132, 74, 0.5);
}

.nav-button.settings {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.4);
}

.nav-button.settings:hover {
    background: rgba(59, 130, 246, 0.3);
    border-color: rgba(59, 130, 246, 0.6);
}

.nav-button.logout {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.4);
}

.nav-button.logout:hover {
    background: rgba(239, 68, 68, 0.3);
    border-color: rgba(239, 68, 68, 0.6);
}

/* Информация о пользователе */
.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
}

.user-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.user-name {
    color: var(--text-white);
    font-weight: 600;
    font-size: 0.9rem;
    line-height: 1;
}

.user-balance {
    color: var(--green-light);
    font-weight: 500;
    font-size: 0.8rem;
    line-height: 1;
}

/* Навигационная группа */
.auth-nav {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* Мобильная навигация */
.mobile-nav-toggle {
    display: none;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 0.75rem;
    color: var(--text-white);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.mobile-nav-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.mobile-nav {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(15, 36, 25, 0.98);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem;
}

.mobile-nav-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* Адаптивность навигации */
@media (max-width: 992px) {
    .auth-nav {
        display: none;
    }

    .mobile-nav-toggle {
        display: block;
    }

    .mobile-nav.show {
        display: block;
    }

    .user-info {
        padding: 0.5rem;
        gap: 0.5rem;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }

    .user-details {
        display: none;
    }
}

@media (max-width: 768px) {
    .modern-navbar {
        padding: 0.75rem 0;
    }

    .brand-text-modern {
        font-size: 1.25rem;
    }

    .nav-button {
        padding: 0.625rem 1rem;
        font-size: 0.85rem;
        min-height: 40px;
    }
}

/* ===== КОНЕЦ ФАЙЛА ===== */
