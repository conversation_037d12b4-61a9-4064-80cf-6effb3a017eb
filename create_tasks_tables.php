<?php
// Создание таблиц для заданий и достижений
require_once 'config/config.php';

echo "<h1>Создание таблиц для заданий и достижений</h1>";

try {
    // Таблица заданий
    $conn->exec("
        CREATE TABLE IF NOT EXISTS tasks (
            id INT PRIMARY KEY AUTO_INCREMENT,
            title VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            type VARCHAR(50) NOT NULL,
            target_value INT NOT NULL DEFAULT 1,
            reward_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            reward_type VARCHAR(20) DEFAULT 'balance',
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "✅ Таблица tasks создана<br>";
    
    // Таблица прогресса заданий пользователей
    $conn->exec("
        CREATE TABLE IF NOT EXISTS user_task_progress (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            task_id INT NOT NULL,
            current_value INT DEFAULT 0,
            is_completed BOOLEAN DEFAULT FALSE,
            completed_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_task (user_id, task_id)
        )
    ");
    echo "✅ Таблица user_task_progress создана<br>";
    
    // Таблица достижений
    $conn->exec("
        CREATE TABLE IF NOT EXISTS achievements (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            type VARCHAR(50) NOT NULL,
            icon VARCHAR(50) DEFAULT 'fas fa-star',
            points INT DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "✅ Таблица achievements создана<br>";
    
    // Добавляем базовые задания
    $tasks = [
        ['Ежедневный вход', 'Заходите в личный кабинет каждый день', 'daily_login', 1, 1.00],
        ['Первая инвестиция', 'Создайте свою первую инвестицию', 'first_investment', 1, 5.00],
        ['Инвестор-новичок', 'Инвестируйте $100 или больше', 'total_investment', 100, 10.00],
        ['Пригласите друга', 'Пригласите одного друга по реферальной ссылке', 'referral_invite', 1, 15.00],
        ['Заполните профиль', 'Заполните все поля в своем профиле', 'profile_complete', 1, 3.00],
        ['Поделитесь в соцсетях', 'Поделитесь ссылкой на платформу', 'social_share', 1, 2.00]
    ];
    
    foreach ($tasks as $task) {
        try {
            $stmt = $conn->prepare("INSERT IGNORE INTO tasks (title, description, type, target_value, reward_amount) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute($task);
            echo "✅ Задание добавлено: {$task[0]}<br>";
        } catch (Exception $e) {
            echo "❌ Ошибка добавления задания {$task[0]}: " . $e->getMessage() . "<br>";
        }
    }
    
    // Добавляем базовые достижения
    $achievements = [
        ['Первые шаги', 'Создали первую инвестицию', 'first_investment', 'fas fa-seedling', 10],
        ['Инвестор-бронза', 'Инвестировали $100+', 'investor_bronze', 'fas fa-medal', 25],
        ['Инвестор-серебро', 'Инвестировали $1000+', 'investor_silver', 'fas fa-medal', 50],
        ['Инвестор-золото', 'Инвестировали $10000+', 'investor_gold', 'fas fa-trophy', 100],
        ['Получатель прибыли', 'Получили $50+ прибыли', 'profit_maker', 'fas fa-coins', 30],
        ['Мастер заданий', 'Выполнили 10 заданий', 'task_master', 'fas fa-tasks', 40],
        ['Реферальный старт', 'Пригласили первого друга', 'referral_starter', 'fas fa-handshake', 20],
        ['Реферальный мастер', 'Пригласили 10+ друзей', 'referral_master', 'fas fa-crown', 75]
    ];
    
    foreach ($achievements as $achievement) {
        try {
            $stmt = $conn->prepare("INSERT IGNORE INTO achievements (name, description, type, icon, points) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute($achievement);
            echo "✅ Достижение добавлено: {$achievement[0]}<br>";
        } catch (Exception $e) {
            echo "❌ Ошибка добавления достижения {$achievement[0]}: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<h2>✅ Таблицы для заданий и достижений созданы!</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ Критическая ошибка: " . $e->getMessage() . "</h2>";
}

echo "<p><a href='index.php'>← Вернуться на главную</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #2c3e50; }
h2 { color: #34495e; border-bottom: 2px solid #ecf0f1; padding-bottom: 5px; }
</style>
