-- Расширение системы инвестиционных пакетов GreenChain EcoFund
-- Добавление новых полей и функциональности

-- Добавляем новые поля к таблице investment_packages
ALTER TABLE investment_packages 
ADD COLUMN IF NOT EXISTS category ENUM('eco_basic', 'eco_premium', 'eco_vip', 'mining_basic', 'mining_premium', 'solar_energy', 'wind_energy', 'green_tech') DEFAULT 'eco_basic',
ADD COLUMN IF NOT EXISTS risk_level ENUM('low', 'medium', 'high') DEFAULT 'low',
ADD COLUMN IF NOT EXISTS popularity_score INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_invested DECIMAL(15,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS total_investors INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS icon VARCHAR(100) DEFAULT 'fas fa-leaf',
ADD COLUMN IF NOT EXISTS color_scheme VARCHAR(20) DEFAULT 'green',
ADD COLUMN IF NOT EXISTS sort_order INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS is_limited BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS limited_slots INT DEFAULT NULL,
ADD COLUMN IF NOT EXISTS used_slots INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS bonus_rate DECIMAL(5,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS early_withdrawal_fee DECIMAL(5,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS compound_available BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS auto_reinvest BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS min_reinvest_amount DECIMAL(15,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS tags JSON DEFAULT NULL,
ADD COLUMN IF NOT EXISTS requirements JSON DEFAULT NULL,
ADD COLUMN IF NOT EXISTS benefits JSON DEFAULT NULL,
ADD COLUMN IF NOT EXISTS image_url VARCHAR(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS video_url VARCHAR(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS project_location VARCHAR(100) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS environmental_impact TEXT DEFAULT NULL,
ADD COLUMN IF NOT EXISTS carbon_offset DECIMAL(10,2) DEFAULT 0.00;

-- Создаем таблицу категорий пакетов для лучшей организации
CREATE TABLE IF NOT EXISTS package_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(100) DEFAULT 'fas fa-leaf',
    color VARCHAR(20) DEFAULT 'green',
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Создаем таблицу для отзывов о пакетах
CREATE TABLE IF NOT EXISTS package_reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    package_id INT NOT NULL,
    user_id INT NOT NULL,
    rating TINYINT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    is_approved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (package_id) REFERENCES investment_packages(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_package_review (user_id, package_id),
    INDEX idx_package_id (package_id),
    INDEX idx_rating (rating),
    INDEX idx_approved (is_approved)
);

-- Создаем таблицу для FAQ по пакетам
CREATE TABLE IF NOT EXISTS package_faq (
    id INT PRIMARY KEY AUTO_INCREMENT,
    package_id INT NOT NULL,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (package_id) REFERENCES investment_packages(id) ON DELETE CASCADE,
    INDEX idx_package_id (package_id),
    INDEX idx_sort_order (sort_order)
);

-- Создаем таблицу для истории изменений пакетов
CREATE TABLE IF NOT EXISTS package_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    package_id INT NOT NULL,
    field_name VARCHAR(50) NOT NULL,
    old_value TEXT,
    new_value TEXT,
    changed_by INT NOT NULL,
    change_reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (package_id) REFERENCES investment_packages(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(id),
    INDEX idx_package_id (package_id),
    INDEX idx_changed_by (changed_by),
    INDEX idx_created_at (created_at)
);

-- Вставляем категории пакетов
INSERT IGNORE INTO package_categories (name, description, icon, color, sort_order) VALUES
('Эко-проекты базовые', 'Начальные экологические инвестиции с низким риском', 'fas fa-seedling', 'green', 1),
('Эко-проекты премиум', 'Продвинутые экологические проекты со средней доходностью', 'fas fa-leaf', 'emerald', 2),
('Эко-проекты VIP', 'Эксклюзивные экологические инвестиции с высокой доходностью', 'fas fa-crown', 'gold', 3),
('Майнинг базовый', 'Базовые майнинговые операции', 'fas fa-microchip', 'blue', 4),
('Майнинг премиум', 'Продвинутый майнинг с зеленой энергией', 'fas fa-server', 'purple', 5),
('Солнечная энергия', 'Инвестиции в солнечные электростанции', 'fas fa-sun', 'yellow', 6),
('Ветровая энергия', 'Проекты ветровых электростанций', 'fas fa-wind', 'cyan', 7),
('Зеленые технологии', 'Инновационные экологические технологии', 'fas fa-atom', 'teal', 8);

-- Обновляем существующие пакеты с новыми данными
UPDATE investment_packages SET
    category = 'eco_basic',
    risk_level = 'low',
    icon = 'fas fa-seedling',
    color_scheme = 'green',
    sort_order = 1,
    compound_available = TRUE,
    tags = '["Экология", "Низкий риск", "Гибкость"]',
    benefits = '["Вывод в любое время", "Низкий риск", "Экологичность", "Стабильный доход"]',
    environmental_impact = 'Поддержка проектов по восстановлению лесов и очистке водоемов',
    carbon_offset = 0.5
WHERE name = 'Гибкий пакет';

UPDATE investment_packages SET
    category = 'eco_premium',
    risk_level = 'medium',
    icon = 'fas fa-leaf',
    color_scheme = 'emerald',
    sort_order = 2,
    is_featured = TRUE,
    compound_available = TRUE,
    tags = '["Экология", "Средний риск", "30 дней", "Популярный"]',
    benefits = '["Повышенная доходность", "Ежедневные выплаты", "Экологический проект", "Фиксированный срок"]',
    environmental_impact = 'Финансирование солнечных электростанций и ветровых ферм',
    carbon_offset = 2.0
WHERE name = 'Фиксированный 30 дней';

UPDATE investment_packages SET
    category = 'eco_premium',
    risk_level = 'medium',
    icon = 'fas fa-tree',
    color_scheme = 'forest',
    sort_order = 3,
    compound_available = TRUE,
    tags = '["Экология", "Средний риск", "60 дней", "Высокий доход"]',
    benefits = '["Высокая доходность", "Ежедневные выплаты", "Долгосрочный проект", "Экологический эффект"]',
    environmental_impact = 'Инвестиции в переработку отходов и чистые технологии',
    carbon_offset = 4.0
WHERE name = 'Фиксированный 60 дней';

UPDATE investment_packages SET
    category = 'eco_vip',
    risk_level = 'medium',
    icon = 'fas fa-crown',
    color_scheme = 'gold',
    sort_order = 4,
    is_featured = TRUE,
    compound_available = TRUE,
    tags = '["Экология", "VIP", "90 дней", "Максимальный доход"]',
    benefits = '["Максимальная доходность", "VIP статус", "Приоритетная поддержка", "Эксклюзивные проекты"]',
    environmental_impact = 'Поддержка инновационных проектов по борьбе с изменением климата',
    carbon_offset = 8.0
WHERE name = 'Фиксированный 90 дней';

-- Добавляем новые инновационные пакеты
INSERT IGNORE INTO investment_packages (
    name, type, daily_rate, min_amount, max_amount, duration_days, description,
    category, risk_level, icon, color_scheme, sort_order, is_featured,
    compound_available, tags, benefits, environmental_impact, carbon_offset
) VALUES
('Солнечная энергия Pro', 'fixed', 2.20, 2000.00, 50000.00, 45,
 'Эксклюзивные инвестиции в солнечные электростанции нового поколения',
 'solar_energy', 'medium', 'fas fa-sun', 'yellow', 5, TRUE, TRUE,
 '["Солнечная энергия", "Инновации", "45 дней", "Высокий доход"]',
 '["Чистая энергия", "Высокая доходность", "Экологический эффект", "Инновационные технологии"]',
 'Строительство и эксплуатация солнечных электростанций с использованием передовых технологий',
 12.0),

('Ветровая ферма Elite', 'fixed', 2.50, 5000.00, 100000.00, 75,
 'Премиальные инвестиции в морские ветровые электростанции',
 'wind_energy', 'medium', 'fas fa-wind', 'cyan', 6, TRUE, TRUE,
 '["Ветровая энергия", "Премиум", "75 дней", "Элитный"]',
 '["Морские ветряки", "Премиум доходность", "Долгосрочная стабильность", "Экологическая чистота"]',
 'Развитие морских ветровых электростанций с максимальной эффективностью',
 25.0),

('Зеленый майнинг', 'flexible', 1.80, 1000.00, NULL, NULL,
 'Майнинг криптовалют на 100% возобновляемой энергии',
 'mining_premium', 'medium', 'fas fa-server', 'purple', 7, FALSE, TRUE,
 '["Зеленый майнинг", "Криптовалюты", "Экология", "Гибкий"]',
 '["Экологичный майнинг", "Гибкие условия", "Стабильный доход", "Инновационный подход"]',
 'Майнинг криптовалют с использованием исключительно возобновляемых источников энергии',
 5.0),

('Карбоновые кредиты', 'fixed', 3.00, 10000.00, 500000.00, 120,
 'Инвестиции в проекты по торговле карбоновыми кредитами',
 'green_tech', 'high', 'fas fa-atom', 'teal', 8, TRUE, TRUE,
 '["Карбоновые кредиты", "Высокий доход", "120 дней", "Инновации"]',
 '["Максимальная доходность", "Борьба с изменением климата", "Инновационный рынок", "Долгосрочная перспектива"]',
 'Участие в глобальной торговле карбоновыми кредитами для снижения выбросов CO2',
 50.0);
