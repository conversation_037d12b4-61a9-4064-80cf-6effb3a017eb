/* ===== СПЕЦИФИЧЕСКОЕ ЦЕНТРИРОВАНИЕ ДЛЯ РАЗНЫХ СТРАНИЦ ===== */

/*
 * Этот файл содержит специфические правила центрирования для различных страниц
 * Обеспечивает правильное отображение на всех вкладках сайта
 */

/* ===== ГЛАВНАЯ СТРАНИЦА ===== */

/* Hero секция */
.modern-hero {
    text-align: center !important;
    padding: 4rem 0 !important;
}

.modern-hero .container {
    max-width: 1200px !important;
    margin: 0 auto !important;
}

.modern-hero .row {
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
}

.modern-hero .col-lg-6 {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

.hero-content-modern {
    text-align: center !important;
    max-width: 600px !important;
    margin: 0 auto !important;
}

.hero-image-modern {
    text-align: center !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

/* ===== СТРАНИЦА ИНВЕСТИЦИЙ ===== */

.investment-packages {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

.investment-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 2rem !important;
    justify-content: center !important;
    align-items: center !important;
    justify-items: center !important;
    max-width: 1200px !important;
    margin: 0 auto !important;
}

.luxury-investment-card {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 auto !important;
    max-width: 350px !important;
    width: 100% !important;
}

/* ===== СТРАНИЦА ДАШБОРДА ===== */

.modern-dashboard {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

.dashboard-stats {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 1.5rem !important;
    justify-content: center !important;
    align-items: center !important;
    justify-items: center !important;
    max-width: 1000px !important;
    margin: 0 auto !important;
}

.stat-card {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
}

/* ===== СТРАНИЦА КАРТЫ ===== */

.map-container {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    max-width: 1200px !important;
    margin: 0 auto !important;
}

#map {
    margin: 0 auto !important;
    max-width: 100% !important;
}

/* ===== СТРАНИЦА КАЛЬКУЛЯТОРА ===== */

.calculator-container {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    max-width: 800px !important;
    margin: 0 auto !important;
}

.calculator-form {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    max-width: 500px !important;
    margin: 0 auto !important;
}

.calculator-results {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 2rem auto !important;
}

/* ===== СТРАНИЦА ОБРАЗОВАНИЯ ===== */

.education-content {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    max-width: 1000px !important;
    margin: 0 auto !important;
}

.education-modules {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 2rem !important;
    justify-content: center !important;
    align-items: center !important;
    justify-items: center !important;
    margin: 0 auto !important;
}

.module-card {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    max-width: 350px !important;
}

/* ===== СТРАНИЦА ПРОФИЛЯ ===== */

.profile-container {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    max-width: 800px !important;
    margin: 0 auto !important;
}

.profile-form {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    max-width: 500px !important;
    margin: 0 auto !important;
}

/* ===== СТРАНИЦЫ АВТОРИЗАЦИИ ===== */

.auth-container {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    max-width: 500px !important;
    margin: 0 auto !important;
    min-height: 60vh !important;
}

.auth-form {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    padding: 2rem !important;
}

/* ===== СТРАНИЦА ЛИДЕРБОРДА ===== */

.leaderboard-container {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    max-width: 1000px !important;
    margin: 0 auto !important;
}

.leaderboard-table {
    text-align: center !important;
    margin: 0 auto !important;
    width: 100% !important;
    max-width: 800px !important;
}

/* ===== СТРАНИЦА РЕФЕРАЛОВ ===== */

.referrals-container {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    max-width: 1000px !important;
    margin: 0 auto !important;
}

.referral-stats {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 1.5rem !important;
    justify-content: center !important;
    align-items: center !important;
    justify-items: center !important;
    margin: 2rem auto !important;
}

/* ===== СТРАНИЦА ЗАДАЧ ===== */

.tasks-container {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    max-width: 1000px !important;
    margin: 0 auto !important;
}

.tasks-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 1.5rem !important;
    justify-content: center !important;
    align-items: center !important;
    justify-items: center !important;
    margin: 0 auto !important;
}

.task-card {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    max-width: 350px !important;
}

/* ===== АДАПТИВНОСТЬ ДЛЯ ВСЕХ СТРАНИЦ ===== */

@media (max-width: 768px) {
    .modern-hero .col-lg-6 {
        text-align: center !important;
        margin-bottom: 2rem !important;
    }
    
    .investment-grid,
    .dashboard-stats,
    .education-modules,
    .referral-stats,
    .tasks-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }
    
    .calculator-container,
    .profile-container,
    .auth-container,
    .leaderboard-container,
    .referrals-container,
    .tasks-container {
        padding: 1rem !important;
        margin: 1rem auto !important;
    }
}

@media (max-width: 576px) {
    .hero-content-modern {
        padding: 1rem !important;
    }
    
    .luxury-investment-card,
    .stat-card,
    .module-card,
    .task-card {
        max-width: 100% !important;
        margin: 0.5rem auto !important;
    }
}
