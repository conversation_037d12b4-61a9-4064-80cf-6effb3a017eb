<?php
// Исправление таблицы map_projects
require_once 'config/config.php';

echo "<h1>Исправление таблицы map_projects</h1>";

try {
    // Проверяем структуру таблицы
    $stmt = $conn->query("DESCRIBE map_projects");
    $columns = $stmt->fetchAll();
    
    echo "<h2>Текущие колонки:</h2>";
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']})<br>";
    }
    
    // Добавляем недостающую колонку location если её нет
    $has_location = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'location') {
            $has_location = true;
            break;
        }
    }
    
    if (!$has_location) {
        $conn->exec("ALTER TABLE map_projects ADD COLUMN location VARCHAR(255) NOT NULL AFTER description");
        echo "✅ Колонка location добавлена<br>";
    } else {
        echo "ℹ️ Колонка location уже существует<br>";
    }
    
    // Теперь добавляем проекты
    echo "<h2>Добавление проектов:</h2>";
    
    $projects = [
        ['Солнечная ферма "Зеленая энергия"', 'Крупнейшая солнечная электростанция в регионе', 'Краснодарский край, Россия', 45.0355, 38.9753, 'solar', 2500000.00, 12.5],
        ['Ветропарк "Северный ветер"', 'Современный ветропарк с высокой эффективностью', 'Мурманская область, Россия', 68.9585, 33.0827, 'wind', 1800000.00, 10.8],
        ['Гидроэлектростанция "Чистая вода"', 'Экологически чистая ГЭС малой мощности', 'Алтайский край, Россия', 52.0394, 85.0913, 'hydro', 3200000.00, 15.2]
    ];
    
    foreach ($projects as $project) {
        try {
            // Используем правильные названия колонок
            $stmt = $conn->prepare("INSERT IGNORE INTO map_projects (name, description, location, latitude, longitude, type, investment_amount, roi_percentage, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active')");
            $stmt->execute([$project[0], $project[1], $project[2], $project[3], $project[4], 'eco_project', $project[6], $project[7]]);
            echo "✅ Проект добавлен: {$project[0]}<br>";
        } catch (Exception $e) {
            echo "❌ Ошибка добавления проекта {$project[0]}: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<h2>✅ Таблица исправлена!</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ Критическая ошибка: " . $e->getMessage() . "</h2>";
}

echo "<p><a href='index.php'>← Вернуться на главную</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #2c3e50; }
h2 { color: #34495e; border-bottom: 2px solid #ecf0f1; padding-bottom: 5px; }
</style>
