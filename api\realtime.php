<?php
// API для обновлений в реальном времени
require_once '../config/config.php';
require_once '../config/session_config.php';
session_start();
require_once '../includes/functions.php';

// Установка заголовков для JSON ответа
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Проверка авторизации
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$action = $_GET['action'] ?? '';
$user_id = $_SESSION['user_id'];

try {
    switch ($action) {
        case 'user_data':
            echo json_encode(getUserData($user_id));
            break;
            
        case 'global_stats':
            echo json_encode(getGlobalStats());
            break;
            
        case 'crypto_rates':
            echo json_encode(getCryptoRatesData());
            break;
            
        case 'notifications':
            echo json_encode(getUserNotifications($user_id));
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
    }
} catch (Exception $e) {
    error_log("Realtime API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

/**
 * Получение данных пользователя
 */
function getUserData($user_id) {
    global $conn;
    
    try {
        // Основные данные пользователя
        $stmt = $conn->prepare("
            SELECT balance, total_invested, total_profit, total_withdrawn 
            FROM users 
            WHERE id = ?
        ");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch();
        
        if (!$user) {
            return ['success' => false, 'message' => 'User not found'];
        }
        
        // Дневная прибыль
        $daily_profit = calculateUserDailyProfit($user_id);
        
        // Активные инвестиции
        $active_investments = getActiveInvestments($user_id);
        
        // Статистика рефералов
        $referral_stats = getReferralStats($user_id);
        
        // Прогресс заданий
        $task_progress = getTaskProgress($user_id);
        
        return [
            'success' => true,
            'balance' => floatval($user['balance']),
            'total_invested' => floatval($user['total_invested']),
            'total_profit' => floatval($user['total_profit']),
            'total_withdrawn' => floatval($user['total_withdrawn']),
            'daily_profit' => $daily_profit,
            'active_investments' => $active_investments,
            'referral_stats' => $referral_stats,
            'task_progress' => $task_progress
        ];
        
    } catch (Exception $e) {
        error_log("Error getting user data: " . $e->getMessage());
        return ['success' => false, 'message' => 'Error fetching user data'];
    }
}

/**
 * Расчет дневной прибыли пользователя
 */
function calculateUserDailyProfit($user_id) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT SUM(amount * (daily_rate / 100)) as daily_profit
        FROM user_investments 
        WHERE user_id = ? AND status = 'active'
        AND (end_date IS NULL OR end_date >= CURDATE())
    ");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch();
    
    return floatval($result['daily_profit'] ?? 0);
}

/**
 * Получение активных инвестиций
 */
function getActiveInvestments($user_id) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT 
            ui.id,
            ui.amount,
            ui.daily_rate,
            ui.total_profit,
            ui.start_date,
            ui.end_date,
            ip.name as package_name,
            ip.type as package_type,
            CASE 
                WHEN ui.end_date IS NULL THEN 100
                ELSE ROUND((DATEDIFF(CURDATE(), ui.start_date) / DATEDIFF(ui.end_date, ui.start_date)) * 100, 2)
            END as progress,
            CASE 
                WHEN ui.end_date IS NULL THEN NULL
                ELSE DATEDIFF(ui.end_date, CURDATE()) * 86400
            END as time_left
        FROM user_investments ui
        JOIN investment_packages ip ON ui.package_id = ip.id
        WHERE ui.user_id = ? AND ui.status = 'active'
        ORDER BY ui.created_at DESC
    ");
    $stmt->execute([$user_id]);
    
    $investments = [];
    while ($row = $stmt->fetch()) {
        $row['amount'] = floatval($row['amount']);
        $row['daily_rate'] = floatval($row['daily_rate']);
        $row['total_profit'] = floatval($row['total_profit']);
        $row['progress'] = floatval($row['progress']);
        $row['current_profit'] = $row['amount'] * ($row['daily_rate'] / 100);
        $investments[] = $row;
    }
    
    return $investments;
}

/**
 * Получение статистики рефералов
 */
function getReferralStats($user_id) {
    global $conn;
    
    // Количество рефералов
    $stmt = $conn->prepare("
        SELECT COUNT(*) as total_referrals
        FROM referrals 
        WHERE referrer_id = ? AND is_active = 1
    ");
    $stmt->execute([$user_id]);
    $referral_count = $stmt->fetch()['total_referrals'];
    
    // Общий заработок с рефералов
    $stmt = $conn->prepare("
        SELECT SUM(total_earned) as total_earnings
        FROM referrals 
        WHERE referrer_id = ?
    ");
    $stmt->execute([$user_id]);
    $total_earnings = $stmt->fetch()['total_earnings'] ?? 0;
    
    return [
        'total_referrals' => intval($referral_count),
        'total_earnings' => floatval($total_earnings)
    ];
}

/**
 * Получение прогресса заданий
 */
function getTaskProgress($user_id) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT 
            t.id,
            t.title,
            t.target_value as target,
            t.reward_amount as reward,
            COALESCE(utp.current_value, 0) as current,
            COALESCE(utp.is_completed, 0) as completed,
            ROUND((COALESCE(utp.current_value, 0) / t.target_value) * 100, 2) as progress
        FROM tasks t
        LEFT JOIN user_task_progress utp ON t.id = utp.task_id AND utp.user_id = ?
        WHERE t.is_active = 1
        ORDER BY t.id
    ");
    $stmt->execute([$user_id]);
    
    $tasks = [];
    while ($row = $stmt->fetch()) {
        $row['target'] = intval($row['target']);
        $row['current'] = intval($row['current']);
        $row['completed'] = boolval($row['completed']);
        $row['progress'] = floatval($row['progress']);
        $row['reward'] = floatval($row['reward']);
        $tasks[] = $row;
    }
    
    return $tasks;
}

/**
 * Получение глобальной статистики
 */
function getGlobalStats() {
    global $conn;
    
    try {
        // Общее количество пользователей
        $stmt = $conn->query("SELECT COUNT(*) as total_users FROM users WHERE status = 'active'");
        $total_users = $stmt->fetch()['total_users'];
        
        // Общая сумма инвестиций
        $stmt = $conn->query("SELECT SUM(total_invested) as total_invested FROM users");
        $total_invested = $stmt->fetch()['total_invested'] ?? 0;
        
        // Статистика проектов
        $stmt = $conn->query("
            SELECT 
                type,
                status,
                COUNT(*) as count,
                SUM(investment_amount) as total_investment
            FROM map_projects 
            WHERE is_active = 1
            GROUP BY type, status
        ");
        $project_stats = $stmt->fetchAll();
        
        // Топ инвесторов для лидерборда
        $stmt = $conn->query("
            SELECT 
                id,
                CONCAT(first_name, ' ', SUBSTRING(last_name, 1, 1), '.') as name,
                total_invested
            FROM users 
            WHERE status = 'active' AND total_invested > 0
            ORDER BY total_invested DESC 
            LIMIT 10
        ");
        $leaderboard = $stmt->fetchAll();
        
        // Форматируем данные
        foreach ($leaderboard as &$user) {
            $user['total_invested'] = floatval($user['total_invested']);
        }
        
        return [
            'success' => true,
            'total_users' => intval($total_users),
            'total_invested' => floatval($total_invested),
            'project_stats' => $project_stats,
            'leaderboard' => $leaderboard
        ];
        
    } catch (Exception $e) {
        error_log("Error getting global stats: " . $e->getMessage());
        return ['success' => false, 'message' => 'Error fetching global stats'];
    }
}

/**
 * Получение курсов криптовалют
 */
function getCryptoRatesData() {
    global $conn;
    
    try {
        $stmt = $conn->query("
            SELECT currency_code, rate_usd, rate_change_24h, last_updated
            FROM currency_rates 
            ORDER BY currency_code
        ");
        $rates = [];
        
        while ($row = $stmt->fetch()) {
            $rates[$row['currency_code']] = floatval($row['rate_usd']);
        }
        
        return [
            'success' => true,
            'rates' => $rates,
            'last_updated' => date('Y-m-d H:i:s')
        ];
        
    } catch (Exception $e) {
        error_log("Error getting crypto rates: " . $e->getMessage());
        return ['success' => false, 'message' => 'Error fetching crypto rates'];
    }
}

/**
 * Получение уведомлений пользователя
 */
function getUserNotifications($user_id) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            SELECT id, title, message, type, is_read, action_url, created_at
            FROM notifications 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT 20
        ");
        $stmt->execute([$user_id]);
        
        $notifications = [];
        while ($row = $stmt->fetch()) {
            $row['is_read'] = boolval($row['is_read']);
            $notifications[] = $row;
        }
        
        return [
            'success' => true,
            'notifications' => $notifications
        ];
        
    } catch (Exception $e) {
        error_log("Error getting notifications: " . $e->getMessage());
        return ['success' => false, 'message' => 'Error fetching notifications'];
    }
}
?>
