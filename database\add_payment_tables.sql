-- Скрипт для добавления таблиц платежной системы
-- GreenChain EcoFund - Payment System Tables

-- Проверяем и создаем таблицу заявок на пополнение баланса
CREATE TABLE IF NOT EXISTS payment_requests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'USDT',
    network VARCHAR(20) DEFAULT 'TRC-20',
    transaction_hash VARCHAR(100),
    wallet_address VARCHAR(100),
    status ENUM('pending', 'approved', 'rejected', 'cancelled') DEFAULT 'pending',
    admin_comment TEXT,
    admin_id INT,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON><PERSON>EIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_payment_user (user_id),
    INDEX idx_payment_status (status),
    INDEX idx_payment_date (created_at),
    INDEX idx_payment_hash (transaction_hash)
);

-- Проверяем и создаем таблицу заявок на вывод средств
CREATE TABLE IF NOT EXISTS withdrawal_requests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'USDT',
    network VARCHAR(20) DEFAULT 'TRC-20',
    wallet_address VARCHAR(100) NOT NULL,
    transaction_hash VARCHAR(100),
    fee_amount DECIMAL(15,2) DEFAULT 0.00,
    final_amount DECIMAL(15,2) NOT NULL,
    status ENUM('pending', 'approved', 'rejected', 'completed', 'cancelled') DEFAULT 'pending',
    admin_comment TEXT,
    admin_id INT,
    processed_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_withdrawal_user (user_id),
    INDEX idx_withdrawal_status (status),
    INDEX idx_withdrawal_date (created_at),
    INDEX idx_withdrawal_wallet (wallet_address),
    INDEX idx_withdrawal_hash (transaction_hash)
);

-- Добавляем настройки платежной системы в таблицу settings (если она существует)
INSERT IGNORE INTO settings (setting_key, setting_value, description) VALUES
('payment_usdt_wallet', 'TYourUSDTWalletAddressHere', 'USDT TRC-20 кошелек для пополнений'),
('payment_min_deposit', '10.00', 'Минимальная сумма пополнения в USDT'),
('payment_max_deposit', '10000.00', 'Максимальная сумма пополнения в USDT'),
('payment_min_withdrawal', '20.00', 'Минимальная сумма вывода в USDT'),
('payment_max_withdrawal', '5000.00', 'Максимальная сумма вывода в USDT'),
('payment_withdrawal_fee', '2.00', 'Комиссия за вывод в USDT'),
('payment_auto_approve_limit', '100.00', 'Лимит для автоматического одобрения пополнений'),
('payment_daily_withdrawal_limit', '1000.00', 'Дневной лимит вывода на пользователя'),
('payment_system_enabled', '1', 'Включена ли платежная система (1 - да, 0 - нет)');

-- Создаем таблицу настроек если она не существует
CREATE TABLE IF NOT EXISTS settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key)
);

-- Добавляем настройки после создания таблицы
INSERT IGNORE INTO settings (setting_key, setting_value, description) VALUES
('payment_usdt_wallet', 'TYourUSDTWalletAddressHere', 'USDT TRC-20 кошелек для пополнений'),
('payment_min_deposit', '10.00', 'Минимальная сумма пополнения в USDT'),
('payment_max_deposit', '10000.00', 'Максимальная сумма пополнения в USDT'),
('payment_min_withdrawal', '20.00', 'Минимальная сумма вывода в USDT'),
('payment_max_withdrawal', '5000.00', 'Максимальная сумма вывода в USDT'),
('payment_withdrawal_fee', '2.00', 'Комиссия за вывод в USDT'),
('payment_auto_approve_limit', '100.00', 'Лимит для автоматического одобрения пополнений'),
('payment_daily_withdrawal_limit', '1000.00', 'Дневной лимит вывода на пользователя'),
('payment_system_enabled', '1', 'Включена ли платежная система (1 - да, 0 - нет)');

-- Добавляем тестовые данные для разработки (можно удалить в продакшене)
-- INSERT INTO payment_requests (user_id, amount, currency, network, status, created_at) VALUES
-- (1, 100.00, 'USDT', 'TRC-20', 'pending', NOW()),
-- (1, 50.00, 'USDT', 'TRC-20', 'approved', NOW() - INTERVAL 1 DAY);

-- INSERT INTO withdrawal_requests (user_id, amount, currency, network, wallet_address, final_amount, status, created_at) VALUES
-- (1, 80.00, 'USDT', 'TRC-20', 'TTestWalletAddress123456789', 78.00, 'pending', NOW()),
-- (1, 30.00, 'USDT', 'TRC-20', 'TTestWalletAddress987654321', 28.00, 'completed', NOW() - INTERVAL 2 DAY);

SELECT 'Таблицы платежной системы успешно созданы!' as result;
