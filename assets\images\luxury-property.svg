<svg width="400" height="250" viewBox="0 0 400 250" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E0F6FF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="poolGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#00BFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0080FF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="houseGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#F5F5DC;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DDD;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Sky Background -->
  <rect width="400" height="150" fill="url(#skyGradient)"/>
  
  <!-- Mountains/Hills -->
  <path d="M0 120 Q100 80 200 100 T400 90 L400 150 L0 150 Z" fill="#8FBC8F"/>
  <path d="M0 130 Q150 100 300 110 T400 105 L400 150 L0 150 Z" fill="#9ACD32" opacity="0.7"/>
  
  <!-- Palm Trees -->
  <g transform="translate(50, 100)">
    <rect x="18" y="20" width="4" height="30" fill="#8B4513"/>
    <ellipse cx="20" cy="15" rx="15" ry="8" fill="#228B22"/>
    <ellipse cx="20" cy="12" rx="12" ry="6" fill="#32CD32"/>
  </g>
  
  <g transform="translate(350, 95)">
    <rect x="18" y="25" width="4" height="25" fill="#8B4513"/>
    <ellipse cx="20" cy="20" rx="12" ry="6" fill="#228B22"/>
    <ellipse cx="20" cy="18" rx="10" ry="5" fill="#32CD32"/>
  </g>
  
  <!-- Modern Villa -->
  <g transform="translate(120, 80)">
    <!-- Main Structure -->
    <rect x="0" y="40" width="160" height="60" fill="url(#houseGradient)" stroke="#CCC" stroke-width="1"/>
    
    <!-- Roof -->
    <polygon points="0,40 80,20 160,40" fill="#8B4513"/>
    
    <!-- Windows -->
    <rect x="20" y="55" width="25" height="20" fill="#87CEEB" stroke="#666" stroke-width="1"/>
    <rect x="55" y="55" width="25" height="20" fill="#87CEEB" stroke="#666" stroke-width="1"/>
    <rect x="100" y="55" width="25" height="20" fill="#87CEEB" stroke="#666" stroke-width="1"/>
    
    <!-- Door -->
    <rect x="130" y="65" width="20" height="35" fill="#8B4513" stroke="#666" stroke-width="1"/>
    <circle cx="145" cy="82" r="1" fill="#FFD700"/>
    
    <!-- Modern Extension -->
    <rect x="160" y="50" width="40" height="50" fill="#F0F0F0" stroke="#CCC" stroke-width="1"/>
    <rect x="170" y="60" width="20" height="15" fill="#87CEEB" stroke="#666" stroke-width="1"/>
  </g>
  
  <!-- Swimming Pool -->
  <ellipse cx="200" cy="180" rx="80" ry="30" fill="url(#poolGradient)"/>
  
  <!-- Pool Deck -->
  <rect x="100" y="160" width="200" height="40" fill="#D2B48C" opacity="0.8"/>
  
  <!-- Pool Reflections -->
  <ellipse cx="190" cy="175" rx="15" ry="5" fill="#FFFFFF" opacity="0.3"/>
  <ellipse cx="220" cy="185" rx="10" ry="3" fill="#FFFFFF" opacity="0.2"/>
  
  <!-- Clouds -->
  <ellipse cx="80" cy="40" rx="20" ry="8" fill="#FFFFFF" opacity="0.8"/>
  <ellipse cx="95" cy="35" rx="15" ry="6" fill="#FFFFFF" opacity="0.8"/>
  <ellipse cx="320" cy="30" rx="25" ry="10" fill="#FFFFFF" opacity="0.8"/>
  <ellipse cx="340" cy="25" rx="18" ry="7" fill="#FFFFFF" opacity="0.8"/>
  
  <!-- Sun -->
  <circle cx="350" cy="50" r="15" fill="#FFD700" opacity="0.9"/>
  
  <!-- Ground/Grass -->
  <rect x="0" y="150" width="400" height="100" fill="#90EE90"/>
  
  <!-- Garden Elements -->
  <circle cx="80" cy="200" r="8" fill="#FF69B4"/>
  <circle cx="95" cy="195" r="6" fill="#FF1493"/>
  <circle cx="320" cy="205" r="7" fill="#FF69B4"/>
  <circle cx="335" cy="200" r="5" fill="#FF1493"/>
</svg>
