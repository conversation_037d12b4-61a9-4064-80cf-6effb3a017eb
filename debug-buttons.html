<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Отладка кнопок инвестирования</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-console {
            background: #000;
            color: #0f0;
            font-family: monospace;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            border-radius: 5px;
        }
        .error { color: #f00; }
        .warn { color: #ff0; }
        .success { color: #0f0; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>🔧 Отладка кнопок инвестирования</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Тестовые кнопки</h5>
                    </div>
                    <div class="card-body">
                        <!-- Симуляция авторизованного пользователя -->
                        <div id="user-data" style="display: none;"
                             data-user-id="123"
                             data-user-name="Test User"
                             data-user-role="user">
                        </div>

                        <button class="btn btn-primary invest-btn mb-2" data-package-type="flexible">
                            Кнопка с типом
                        </button><br>
                        
                        <button class="btn btn-success invest-btn mb-2" data-package-id="1" data-package-type="fixed">
                            Кнопка с ID
                        </button><br>
                        
                        <button class="btn btn-warning invest-btn mb-2">
                            Кнопка без данных
                        </button><br>
                        
                        <button id="manual-test" class="btn btn-info">
                            Ручной тест функции
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Консоль отладки</h5>
                    </div>
                    <div class="card-body">
                        <div id="debug-console" class="debug-console"></div>
                        <button onclick="clearConsole()" class="btn btn-sm btn-secondary mt-2">Очистить</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Подключаем только main.js -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // Система отладки
        const debugConsole = document.getElementById('debug-console');
        
        function log(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'warn' ? 'warn' : type === 'success' ? 'success' : '';
            debugConsole.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            debugConsole.scrollTop = debugConsole.scrollHeight;

            // Безопасный вызов console
            if (type === 'error' && console.error) {
                console.error(message);
            } else if (type === 'warn' && console.warn) {
                console.warn(message);
            } else if (console.log) {
                console.log(message);
            }
        }
        
        function clearConsole() {
            debugConsole.innerHTML = '';
        }
        
        // Перехват ошибок
        window.addEventListener('error', function(e) {
            log(`JS Error: ${e.message} at ${e.filename}:${e.lineno}`, 'error');
        });
        
        // Инициализация
        document.addEventListener('DOMContentLoaded', function() {
            log('=== ОТЛАДКА ЗАПУЩЕНА ===');
            
            // Проверяем загрузку функций
            log('Проверяем функции из main.js...');
            
            if (typeof handleInvestClick === 'function') {
                log('✅ handleInvestClick найдена', 'success');
            } else {
                log('❌ handleInvestClick НЕ найдена', 'error');
            }
            
            if (typeof showInvestModal === 'function') {
                log('✅ showInvestModal найдена', 'success');
            } else {
                log('❌ showInvestModal НЕ найдена', 'error');
            }
            
            if (typeof checkAuthStatus === 'function') {
                log('✅ checkAuthStatus найдена', 'success');
            } else {
                log('❌ checkAuthStatus НЕ найдена', 'error');
            }
            
            // Проверяем переменные
            if (typeof isLoggedIn !== 'undefined') {
                log(`✅ isLoggedIn = ${isLoggedIn}`, 'success');
            } else {
                log('❌ isLoggedIn не определена', 'error');
            }
            
            if (typeof currentUser !== 'undefined') {
                log(`✅ currentUser = ${JSON.stringify(currentUser)}`, 'success');
            } else {
                log('❌ currentUser не определена', 'error');
            }
            
            // Проверяем кнопки
            const buttons = document.querySelectorAll('.invest-btn');
            log(`Найдено кнопок .invest-btn: ${buttons.length}`);
            
            // Добавляем прямой обработчик для тестирования
            buttons.forEach((btn, index) => {
                btn.addEventListener('click', function(e) {
                    log(`🎯 ПРЯМОЙ КЛИК по кнопке ${index + 1}`, 'warn');
                    log(`Target: ${e.target.tagName}.${e.target.className}`);
                    log(`Data: ${JSON.stringify(e.target.dataset)}`);
                });
            });
            
            // Ручной тест
            document.getElementById('manual-test').addEventListener('click', function() {
                log('🧪 РУЧНОЙ ТЕСТ', 'warn');
                if (typeof handleInvestClick === 'function') {
                    try {
                        const fakeEvent = {
                            preventDefault: () => {},
                            target: {
                                dataset: {
                                    packageId: '999',
                                    packageType: 'test'
                                }
                            }
                        };
                        log('Вызываем handleInvestClick с тестовыми данными...');
                        handleInvestClick(fakeEvent);
                    } catch (error) {
                        log(`Ошибка в handleInvestClick: ${error.message}`, 'error');
                    }
                } else {
                    log('handleInvestClick не найдена для ручного теста', 'error');
                }
            });
            
            log('Инициализация завершена');
        });
    </script>
</body>
</html>
