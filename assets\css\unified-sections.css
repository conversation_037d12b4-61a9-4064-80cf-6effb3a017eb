/* ===== УНИФИКАЦИЯ И ВЫРАВНИВАНИЕ ВСЕХ СЕКЦИЙ ===== */

/* Переменные для единообразия - РАСШИРЕННЫЕ КОНТЕЙНЕРЫ */
:root {
    --section-padding: 5rem 0;
    --section-padding-mobile: 3rem 0;
    --card-gap: 2rem;
    --card-gap-mobile: 1.5rem;
    --card-padding: 2rem;
    --card-padding-mobile: 1.5rem;
    --border-radius-card: 8px; /* Максимум 8px для геометрической правильности */
    --transition-standard: all 0.3s ease;
    --section-border: 1px solid rgba(255, 255, 255, 0.1);

    /* Расширенные переменные для улучшенного выравнивания */
    --container-max-width: 1400px; /* Увеличено с 1200px */
    --container-max-width-wide: 1600px; /* Для очень широких экранов */
    --container-max-width-standard: 1200px; /* Стандартная ширина */
    --container-padding: 2rem;
    --container-padding-wide: 3rem; /* Для широких экранов */
    --container-padding-mobile: 1rem;
    --section-spacing: 4rem;
    --section-spacing-mobile: 2rem;
}

/* ===== УНИФИКАЦИЯ ВСЕХ СЕКЦИЙ ===== */

/* Базовые стили для всех секций */
section,
.content-section-modern,
.features-section,
.how-it-works-section,
.partners-section,
.cta-section,
.modern-hero {
    padding: var(--section-padding) !important;
    position: relative;
    overflow: hidden;
    /* Геометрическая правильность секций */
    border-radius: 0 !important; /* Убираем скругления */
    border-bottom: var(--section-border);
    /* Четкое разделение секций */
    margin-bottom: 0;
    /* Улучшенное выравнивание и расширение */
    width: 100% !important;
    min-height: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Контейнеры секций - улучшенное выравнивание с расширенной шириной */
section .container,
.content-section-modern .container,
.features-section .container,
.how-it-works-section .container,
.partners-section .container,
.cta-section .container,
.modern-hero .container {
    position: relative;
    z-index: 2;
    max-width: var(--container-max-width) !important;
    width: 100% !important;
    margin: 0 auto !important;
    padding: 0 var(--container-padding) !important;
    /* Центрирование содержимого */
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

/* Адаптивные контейнеры для широких экранов */
@media (min-width: 1600px) {
    section .container,
    .content-section-modern .container,
    .features-section .container,
    .how-it-works-section .container,
    .partners-section .container,
    .cta-section .container,
    .modern-hero .container {
        max-width: var(--container-max-width-wide) !important;
        padding: 0 var(--container-padding-wide) !important;
    }
}

@media (min-width: 1400px) and (max-width: 1599px) {
    section .container,
    .content-section-modern .container,
    .features-section .container,
    .how-it-works-section .container,
    .partners-section .container,
    .cta-section .container,
    .modern-hero .container {
        max-width: 1400px !important;
        padding: 0 2.5rem !important;
    }
}

/* ===== УНИФИКАЦИЯ СЕТОК И КАРТОЧЕК ===== */

/* Единая сетка для всех карточек - улучшенное выравнивание */
.features-grid-modern,
.row {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: var(--card-gap) !important;
    margin: 2rem auto !important;
    width: 100% !important;
    max-width: var(--container-max-width) !important;
    /* Центрирование сетки */
    justify-content: center !important;
    align-items: stretch !important;
    padding: 0 !important;
}

/* Специальные сетки для разных типов контента */
.row.investment-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)) !important;
}

.row.package-grid {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
}

.row.feature-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
}

.row.step-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
}

/* Специальная обработка для hero секции */
.modern-hero .row {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: left !important;
    gap: 2rem !important;
}

.modern-hero .col-lg-6 {
    flex: 1 1 45% !important;
    min-width: 300px !important;
    max-width: 600px !important;
}

/* ===== УНИФИКАЦИЯ КАРТОЧЕК ===== */

/* Базовые стили для всех карточек - улучшенное выравнивание */
.feature-card-modern,
.investment-card,
.luxury-investment-card,
.feature-card,
.step-card {
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    border-radius: var(--border-radius-card) !important; /* 8px максимум */
    padding: var(--card-padding) !important;
    transition: var(--transition-standard) !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
    align-items: stretch !important;
    /* Геометрическая правильность */
    position: relative !important;
    overflow: hidden !important;
    /* Улучшенное выравнивание */
    margin: 0 auto !important;
    width: 100% !important;
    max-width: 100% !important;
    text-align: left !important;
}

/* Эффекты при наведении */
.feature-card-modern:hover,
.investment-card:hover,
.luxury-investment-card:hover,
.feature-card:hover,
.step-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: #10b981;
}

/* ===== УНИФИКАЦИЯ ЗАГОЛОВКОВ ===== */

/* Заголовки секций - улучшенное центрирование */
.section-title,
.section-title-modern,
h2 {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    text-align: center !important;
    margin: 0 auto 1.5rem auto !important;
    line-height: 1.2 !important;
    width: 100% !important;
    max-width: 800px !important;
    display: block !important;
    /* Улучшенное выравнивание */
    padding: 0 1rem !important;
}

/* Подзаголовки секций - улучшенное центрирование */
.section-subtitle,
.section-subtitle-modern {
    font-size: 1.1rem !important;
    text-align: center !important;
    margin: 0 auto 3rem auto !important;
    max-width: 700px !important;
    line-height: 1.6 !important;
    width: 100% !important;
    display: block !important;
    /* Улучшенное выравнивание */
    padding: 0 1rem !important;
}

/* Специальная обработка для hero заголовков */
.modern-hero .hero-title-modern {
    text-align: left !important;
    margin: 0 0 1.5rem 0 !important;
    max-width: none !important;
    padding: 0 !important;
}

.modern-hero .hero-subtitle-modern {
    text-align: left !important;
    margin: 0 0 2rem 0 !important;
    max-width: none !important;
    padding: 0 !important;
}

/* Заголовки карточек */
.feature-title-modern,
.investment-title,
.luxury-investment-title,
.feature-title,
.step-title,
h3, h4, h5 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    line-height: 1.3;
    text-align: left !important; /* Выравнивание по левому краю в карточках */
}

/* Текст в карточках */
.feature-description-modern,
.investment-description,
.feature-description,
.step-description,
p {
    text-align: left !important; /* Выравнивание по левому краю */
    line-height: 1.6;
    margin-bottom: 1rem;
}

/* ===== УНИФИКАЦИЯ ИКОНОК ===== */

/* Контейнеры иконок */
.feature-icon-modern,
.feature-icon,
.step-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    transition: var(--transition-standard);
}

.feature-icon-modern:hover,
.feature-icon:hover,
.step-icon:hover {
    transform: scale(1.1) rotate(360deg);
}

/* ===== УНИФИКАЦИЯ КНОПОК ===== */

/* Базовые стили кнопок */
.btn-luxury-primary,
.btn-hero-primary,
.btn-primary,
.invest-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    text-decoration: none;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: var(--transition-standard);
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: #ffffff;
    min-width: 180px;
    min-height: 50px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-luxury-primary:hover,
.btn-hero-primary:hover,
.btn-primary:hover,
.invest-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    color: #ffffff;
    text-decoration: none;
}

/* ===== УНИФИКАЦИЯ ИЗОБРАЖЕНИЙ ===== */

/* Изображения в карточках */
.investment-image img,
.feature-image img,
img {
    width: 100%;
    height: auto;
    border-radius: 0.5rem;
    object-fit: cover;
}

/* Контейнеры изображений */
.investment-image,
.feature-image {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

/* ===== АДАПТИВНОСТЬ - УЛУЧШЕННАЯ ===== */

@media (max-width: 1200px) {
    :root {
        --container-padding: 1.5rem;
    }

    section .container,
    .content-section-modern .container,
    .features-section .container,
    .how-it-works-section .container,
    .partners-section .container,
    .cta-section .container,
    .modern-hero .container {
        padding: 0 var(--container-padding) !important;
    }
}

@media (max-width: 1024px) {
    .features-grid-modern,
    .row {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
        gap: 1.5rem !important;
        margin: 1.5rem auto !important;
    }

    .row.package-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
    }

    .modern-hero .col-lg-6 {
        flex: 1 1 100% !important;
        max-width: 100% !important;
        text-align: center !important;
    }

    .modern-hero .hero-title-modern,
    .modern-hero .hero-subtitle-modern {
        text-align: center !important;
    }
}

@media (max-width: 768px) {
    :root {
        --section-padding: var(--section-padding-mobile);
        --card-gap: var(--card-gap-mobile);
        --card-padding: var(--card-padding-mobile);
        --container-padding: var(--container-padding-mobile);
    }

    .features-grid-modern,
    .row {
        grid-template-columns: 1fr !important;
        gap: var(--card-gap-mobile) !important;
        margin: 1rem auto !important;
    }

    .section-title,
    .section-title-modern {
        font-size: 2rem !important;
        padding: 0 0.5rem !important;
    }

    .section-subtitle,
    .section-subtitle-modern {
        font-size: 1rem !important;
        padding: 0 0.5rem !important;
    }

    .feature-icon-modern,
    .feature-icon,
    .step-icon {
        width: 60px !important;
        height: 60px !important;
        font-size: 1.5rem !important;
    }
}

@media (max-width: 576px) {
    .feature-card-modern,
    .investment-card,
    .luxury-investment-card,
    .feature-card,
    .step-card {
        padding: 1.25rem;
    }
    
    .section-title,
    .section-title-modern {
        font-size: 1.75rem;
    }
    
    .btn-luxury-primary,
    .btn-hero-primary,
    .btn-primary,
    .invest-btn {
        min-width: 150px;
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
    }
}

/* ===== ЧЕТКОЕ РАЗДЕЛЕНИЕ СЕКЦИЙ ===== */

/* Альтернативные фоны для секций */
section:nth-child(even) {
    background: rgba(255, 255, 255, 0.02);
    border-top: var(--section-border);
}

section:nth-child(odd) {
    background: rgba(0, 0, 0, 0.02);
}

/* Специальные стили для hero секции */
.modern-hero {
    background: transparent !important;
    border-bottom: 2px solid rgba(255, 255, 255, 0.15) !important;
    padding: 6rem 0 4rem !important; /* Увеличенный верхний отступ для навигации */
}

/* Контрастные секции */
.section-green-bg {
    background: rgba(22, 132, 74, 0.05) !important;
    border-top: 1px solid rgba(22, 132, 74, 0.2);
    border-bottom: 1px solid rgba(22, 132, 74, 0.2);
}

.section-dark-bg {
    background: rgba(0, 0, 0, 0.1) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Визуальные разделители между секциями */
section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    z-index: 1;
}

/* Убираем разделитель для первой секции */
section:first-child::before {
    display: none;
}

/* ===== ДОПОЛНИТЕЛЬНЫЕ СТИЛИ ДЛЯ ГЕОМЕТРИИ ===== */

/* Контейнеры с четкими границами */
.container {
    position: relative;
}

/* Сетки с правильным выравниванием */
.features-grid-modern,
.row {
    align-items: stretch; /* Одинаковая высота карточек */
}

/* Карточки с одинаковыми размерами */
.feature-card-modern,
.investment-card,
.luxury-investment-card,
.feature-card,
.step-card {
    min-height: 300px; /* Минимальная высота для единообразия */
}

/* Иконки с правильным выравниванием */
.feature-icon-modern,
.feature-icon,
.step-icon {
    margin: 0 auto 1.5rem !important; /* Центрирование иконок */
    flex-shrink: 0 !important;
}

/* ===== ДОПОЛНИТЕЛЬНЫЕ УЛУЧШЕНИЯ ВЫРАВНИВАНИЯ ===== */

/* Улучшенное выравнивание для всех колонок Bootstrap */
.col-lg-3,
.col-lg-4,
.col-lg-6,
.col-lg-8,
.col-lg-12,
.col-md-3,
.col-md-4,
.col-md-6,
.col-md-8,
.col-md-12,
.col-sm-12,
.col-12 {
    display: flex !important;
    flex-direction: column !important;
    align-items: stretch !important;
    padding: 0 1rem !important;
    margin-bottom: 2rem !important;
}

/* Центрирование содержимого в колонках с текстом */
.col-12.text-center {
    align-items: center !important;
    text-align: center !important;
}

/* Специальная обработка для статистических карточек */
.hero-stats-modern {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 2rem !important;
    flex-wrap: wrap !important;
    margin: 2rem 0 !important;
    width: 100% !important;
}

.stat-card-modern {
    text-align: center !important;
    min-width: 120px !important;
    flex: 1 1 auto !important;
}

/* Улучшенное выравнивание кнопок */
.hero-buttons-modern,
.cta-buttons {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 1rem !important;
    flex-wrap: wrap !important;
    margin: 2rem auto !important;
    width: 100% !important;
}

/* Партнеры - центрирование */
.partners-logos {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    flex-wrap: wrap !important;
    gap: 2rem !important;
    width: 100% !important;
    margin: 0 auto !important;
}

.partner-logo {
    text-align: center !important;
    flex: 0 0 auto !important;
}

/* Улучшенное выравнивание для мобильных устройств */
@media (max-width: 576px) {
    .col-lg-3,
    .col-lg-4,
    .col-lg-6,
    .col-lg-8,
    .col-lg-12,
    .col-md-3,
    .col-md-4,
    .col-md-6,
    .col-md-8,
    .col-md-12,
    .col-sm-12,
    .col-12 {
        padding: 0 0.5rem !important;
        margin-bottom: 1.5rem !important;
    }

    .hero-stats-modern {
        gap: 1rem !important;
        justify-content: center !important;
    }

    .hero-buttons-modern,
    .cta-buttons {
        gap: 0.75rem !important;
        flex-direction: column !important;
        align-items: stretch !important;
    }

    .partners-logos {
        gap: 1rem !important;
    }
}
