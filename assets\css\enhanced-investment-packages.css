/* ===== РАСШИРЕННЫЕ ИНВЕСТИЦИОННЫЕ ПАКЕТЫ - ПРЕМИАЛЬНАЯ ТЕМНАЯ СХЕМА ===== */

/* Основные переменные для новых элементов */
:root {
    --package-green: #16844a;
    --package-emerald: #10b981;
    --package-forest: #065f46;
    --package-gold: #d4af37;
    --package-blue: #2563eb;
    --package-purple: #7c3aed;
    --package-yellow: #f59e0b;
    --package-cyan: #06b6d4;
    --package-teal: #14b8a6;
}

/* Фильтры и сортировка */
.filter-section {
    background: linear-gradient(135deg, var(--dark-green), var(--dark-navy)) !important;
    border: 1px solid var(--gold-primary) !important;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.filter-section .card-header {
    background: transparent !important;
    border-bottom: 1px solid var(--gold-primary) !important;
}

.filter-section .form-label {
    color: var(--text-white) !important;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.filter-section .form-select {
    background: rgba(26, 61, 46, 0.8) !important;
    border: 1px solid var(--gold-primary) !important;
    color: var(--text-white) !important;
    border-radius: 8px;
}

.filter-section .form-select:focus {
    border-color: var(--gold-bright) !important;
    box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25) !important;
}

.filter-section .form-select option {
    background: var(--dark-green) !important;
    color: var(--text-white) !important;
}

/* Карточки пакетов */
.package-card {
    background: linear-gradient(145deg, var(--dark-green-medium), var(--dark-green)) !important;
    border: 1px solid var(--gold-primary) !important;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.package-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(212, 175, 55, 0.2);
    border-color: var(--gold-bright) !important;
}

.package-card.featured-package {
    border: 2px solid var(--gold-bright) !important;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
}

.package-card.featured-package::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--gold-primary), var(--gold-bright), var(--gold-primary));
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

/* Цветовые схемы пакетов */
.package-card.green-theme .card-header {
    background: linear-gradient(135deg, var(--package-green), var(--package-emerald)) !important;
}

.package-card.emerald-theme .card-header {
    background: linear-gradient(135deg, var(--package-emerald), var(--package-forest)) !important;
}

.package-card.forest-theme .card-header {
    background: linear-gradient(135deg, var(--package-forest), var(--dark-green)) !important;
}

.package-card.gold-theme .card-header {
    background: linear-gradient(135deg, var(--gold-primary), var(--gold-bright)) !important;
}

.package-card.blue-theme .card-header {
    background: linear-gradient(135deg, var(--package-blue), var(--dark-navy)) !important;
}

.package-card.purple-theme .card-header {
    background: linear-gradient(135deg, var(--package-purple), var(--dark-navy)) !important;
}

.package-card.yellow-theme .card-header {
    background: linear-gradient(135deg, var(--package-yellow), var(--gold-primary)) !important;
}

.package-card.cyan-theme .card-header {
    background: linear-gradient(135deg, var(--package-cyan), var(--package-blue)) !important;
}

.package-card.teal-theme .card-header {
    background: linear-gradient(135deg, var(--package-teal), var(--package-emerald)) !important;
}

/* Бейджи пакетов */
.package-badges {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.package-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
    color: var(--text-white) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    min-width: 80px;
}

.package-badge.featured {
    background: linear-gradient(135deg, var(--gold-primary), var(--gold-bright)) !important;
    animation: pulse 2s ease-in-out infinite;
}

.package-badge.limited {
    background: linear-gradient(135deg, #ef4444, #dc2626) !important;
}

.package-badge.risk-low {
    background: linear-gradient(135deg, #10b981, #059669) !important;
}

.package-badge.risk-medium {
    background: linear-gradient(135deg, #f59e0b, #d97706) !important;
}

.package-badge.risk-high {
    background: linear-gradient(135deg, #ef4444, #dc2626) !important;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Заголовок карточки */
.package-card .card-header {
    padding: 2rem 1.5rem;
    text-align: center;
    position: relative;
    color: var(--text-white) !important;
}

.package-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--text-white) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.package-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-white) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.package-rate {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-white) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.package-bonus {
    font-size: 0.9rem;
    color: var(--gold-bright) !important;
    font-weight: 600;
    margin-top: 0.25rem;
}

/* Тело карточки */
.package-card .card-body {
    padding: 1.5rem;
    background: linear-gradient(145deg, var(--dark-green-medium), var(--dark-green)) !important;
}

/* Информация о пакете */
.package-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-light-gray) !important;
}

.info-item i {
    width: 16px;
    text-align: center;
}

/* Теги */
.package-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.tag {
    background: rgba(212, 175, 55, 0.2) !important;
    border: 1px solid var(--gold-primary) !important;
    color: var(--gold-bright) !important;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Преимущества */
.package-benefits {
    margin-bottom: 1rem;
}

.benefit {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-light-gray) !important;
}

.benefit i {
    margin-top: 0.1rem;
    flex-shrink: 0;
}

/* Экологический эффект */
.environmental-impact {
    background: rgba(16, 132, 74, 0.2) !important;
    border: 1px solid var(--package-green) !important;
    border-radius: 12px;
    padding: 1rem;
}

.environmental-impact h6 {
    color: var(--package-emerald) !important;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.environmental-impact p {
    color: var(--text-light-gray) !important;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.carbon-offset {
    color: var(--package-emerald) !important;
    font-size: 0.85rem;
    font-weight: 600;
}

/* Футер карточки */
.package-card .card-footer {
    background: linear-gradient(135deg, var(--dark-green), var(--dark-navy)) !important;
    border-top: 1px solid var(--gold-primary) !important;
    padding: 1.5rem;
}

.invest-btn {
    background: linear-gradient(135deg, var(--gold-primary), var(--gold-bright)) !important;
    border: none !important;
    color: var(--dark-green) !important;
    font-weight: 700;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    text-shadow: none;
}

.invest-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
    background: linear-gradient(135deg, var(--gold-bright), var(--gold-primary)) !important;
}

.package-stats {
    text-align: center;
}

.package-stats small {
    color: var(--text-medium-gray) !important;
    font-size: 0.8rem;
}

/* Адаптивность */
@media (max-width: 768px) {
    .package-info {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .package-badges {
        position: static;
        flex-direction: row;
        justify-content: center;
        margin-bottom: 1rem;
    }
    
    .package-card .card-header {
        padding: 1.5rem 1rem;
    }
    
    .package-icon {
        font-size: 2.5rem;
    }
    
    .package-title {
        font-size: 1.25rem;
    }
}
