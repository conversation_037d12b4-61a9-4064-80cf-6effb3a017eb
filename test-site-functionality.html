<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Тест функциональности сайта</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">🧪 Комплексный тест сайта GreenChain EcoFund</h1>
                
                <!-- Статус тестирования -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-chart-line"></i> Статус тестирования</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-status" class="alert alert-info">
                            <i class="fas fa-spinner fa-spin"></i> Инициализация тестов...
                        </div>
                    </div>
                </div>

                <!-- Тесты страниц -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-globe"></i> Тест доступности страниц</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <button class="btn btn-outline-primary mb-2 w-100" onclick="testPage('/')">
                                    <i class="fas fa-home"></i> Главная страница
                                </button>
                                <button class="btn btn-outline-primary mb-2 w-100" onclick="testPage('/index.php?page=login')">
                                    <i class="fas fa-sign-in-alt"></i> Страница входа
                                </button>
                                <button class="btn btn-outline-primary mb-2 w-100" onclick="testPage('/index.php?page=register')">
                                    <i class="fas fa-user-plus"></i> Регистрация
                                </button>
                                <button class="btn btn-outline-primary mb-2 w-100" onclick="testPage('/index.php?page=invest')">
                                    <i class="fas fa-chart-line"></i> Инвестирование
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-outline-primary mb-2 w-100" onclick="testPage('/index.php?page=about')">
                                    <i class="fas fa-info-circle"></i> О проекте
                                </button>
                                <button class="btn btn-outline-primary mb-2 w-100" onclick="testPage('/index.php?page=contact')">
                                    <i class="fas fa-envelope"></i> Контакты
                                </button>
                                <button class="btn btn-outline-primary mb-2 w-100" onclick="testPage('/index.php?page=news')">
                                    <i class="fas fa-newspaper"></i> Новости
                                </button>
                                <button class="btn btn-outline-primary mb-2 w-100" onclick="testPage('/index.php?page=faq')">
                                    <i class="fas fa-question-circle"></i> FAQ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Тест JavaScript -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-code"></i> Тест JavaScript функций</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-warning mb-2" onclick="testJavaScript()">
                            <i class="fas fa-play"></i> Запустить тест JS
                        </button>
                        <div id="js-test-results" class="mt-3"></div>
                    </div>
                </div>

                <!-- Тест форм -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-wpforms"></i> Тест форм</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success mb-2" onclick="testForms()">
                            <i class="fas fa-check"></i> Тест форм авторизации
                        </button>
                        <div id="form-test-results" class="mt-3"></div>
                    </div>
                </div>

                <!-- Лог событий -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Лог событий</h5>
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="clearLog()">
                            <i class="fas fa-trash"></i> Очистить
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="event-log" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;">
                            <!-- Лог будет заполняться здесь -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Функция логирования
        function log(message, type = 'info') {
            const logElement = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : type === 'warning' ? 'text-warning' : 'text-info';
            
            logElement.innerHTML += `<div class="${colorClass}"><strong>[${timestamp}]</strong> ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // Очистка лога
        function clearLog() {
            document.getElementById('event-log').innerHTML = '';
        }

        // Тест доступности страницы
        async function testPage(url) {
            log(`🔍 Тестирование страницы: ${url}`, 'info');
            
            try {
                const response = await fetch(url, { method: 'HEAD' });
                if (response.ok) {
                    log(`✅ Страница ${url} доступна (${response.status})`, 'success');
                } else {
                    log(`❌ Ошибка на странице ${url}: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                log(`❌ Ошибка при обращении к ${url}: ${error.message}`, 'error');
            }
        }

        // Тест JavaScript функций
        function testJavaScript() {
            log('🧪 Начинаем тест JavaScript функций...', 'info');
            
            // Проверка загрузки основных скриптов
            const scripts = document.querySelectorAll('script[src]');
            log(`📜 Найдено ${scripts.length} внешних скриптов`, 'info');
            
            // Проверка глобальных переменных
            if (typeof window.isLoggedIn !== 'undefined') {
                log(`✅ Переменная isLoggedIn определена: ${window.isLoggedIn}`, 'success');
            } else {
                log(`❌ Переменная isLoggedIn не определена`, 'error');
            }
            
            if (typeof window.currentUser !== 'undefined') {
                log(`✅ Переменная currentUser определена: ${JSON.stringify(window.currentUser)}`, 'success');
            } else {
                log(`❌ Переменная currentUser не определена`, 'error');
            }
            
            // Проверка jQuery
            if (typeof $ !== 'undefined') {
                log(`✅ jQuery загружен: версия ${$.fn.jquery}`, 'success');
            } else {
                log(`❌ jQuery не загружен`, 'error');
            }
            
            // Проверка Bootstrap
            if (typeof bootstrap !== 'undefined') {
                log(`✅ Bootstrap JS загружен`, 'success');
            } else {
                log(`❌ Bootstrap JS не загружен`, 'error');
            }
            
            log('🏁 Тест JavaScript завершен', 'info');
        }

        // Тест форм
        async function testForms() {
            log('📝 Начинаем тест форм...', 'info');
            
            try {
                // Тест страницы входа
                const loginResponse = await fetch('/index.php?page=login');
                const loginHtml = await loginResponse.text();
                
                if (loginHtml.includes('form') && loginHtml.includes('login')) {
                    log('✅ Форма входа найдена', 'success');
                } else {
                    log('❌ Форма входа не найдена', 'error');
                }
                
                // Тест страницы регистрации
                const registerResponse = await fetch('/index.php?page=register');
                const registerHtml = await registerResponse.text();
                
                if (registerHtml.includes('form') && registerHtml.includes('register')) {
                    log('✅ Форма регистрации найдена', 'success');
                } else {
                    log('❌ Форма регистрации не найдена', 'error');
                }
                
            } catch (error) {
                log(`❌ Ошибка при тестировании форм: ${error.message}`, 'error');
            }
            
            log('🏁 Тест форм завершен', 'info');
        }

        // Автоматический запуск тестов при загрузке
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Система тестирования инициализирована', 'success');
            document.getElementById('test-status').innerHTML = '<i class="fas fa-check-circle"></i> Готов к тестированию';
            document.getElementById('test-status').className = 'alert alert-success';
            
            // Автоматический тест основных страниц
            setTimeout(() => {
                log('🔄 Запуск автоматического тестирования...', 'info');
                testPage('/');
                setTimeout(() => testPage('/index.php?page=login'), 500);
                setTimeout(() => testPage('/index.php?page=register'), 1000);
                setTimeout(() => testPage('/index.php?page=invest'), 1500);
            }, 1000);
        });
    </script>
</body>
</html>
