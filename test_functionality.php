<?php
// Тестирование функциональности GreenChain EcoFund
require_once 'config/config.php';
require_once 'config/session_config.php';
session_start();
require_once 'includes/functions.php';

echo "<h1>Тестирование функциональности GreenChain EcoFund</h1>";

// 1. Тест подключения к базе данных
echo "<h2>1. Тест подключения к базе данных</h2>";
if ($conn) {
    echo "✅ Подключение к базе данных успешно<br>";
    
    // Проверяем основные таблицы
    $tables = ['users', 'investment_packages', 'user_investments', 'transactions'];
    foreach ($tables as $table) {
        try {
            $stmt = $conn->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "✅ Таблица $table: $count записей<br>";
        } catch (Exception $e) {
            echo "❌ Ошибка в таблице $table: " . $e->getMessage() . "<br>";
        }
    }
} else {
    echo "❌ Ошибка подключения к базе данных<br>";
}

// 2. Тест функций безопасности
echo "<h2>2. Тест функций безопасности</h2>";

// Тест хеширования пароля
$test_password = "TestPassword123";
$hash = hashPassword($test_password);
if (verifyPassword($test_password, $hash)) {
    echo "✅ Хеширование и проверка пароля работает<br>";
} else {
    echo "❌ Ошибка в хешировании пароля<br>";
}

// Тест генерации токена
$token = generateToken();
if (strlen($token) === 64) {
    echo "✅ Генерация токена работает (длина: " . strlen($token) . ")<br>";
} else {
    echo "❌ Ошибка в генерации токена<br>";
}

// Тест CSRF токена
$csrf_token = generateCSRFToken();
if (verifyCSRFToken($csrf_token)) {
    echo "✅ CSRF токены работают<br>";
} else {
    echo "❌ Ошибка в CSRF токенах<br>";
}

// 3. Тест валидации
echo "<h2>3. Тест валидации</h2>";

// Тест валидации email
$valid_emails = ['<EMAIL>', '<EMAIL>'];
$invalid_emails = ['invalid-email', 'test@', '@domain.com'];

foreach ($valid_emails as $email) {
    if (validateEmail($email)) {
        echo "✅ Email $email валиден<br>";
    } else {
        echo "❌ Email $email должен быть валиден<br>";
    }
}

foreach ($invalid_emails as $email) {
    if (!validateEmail($email)) {
        echo "✅ Email $email правильно определен как невалидный<br>";
    } else {
        echo "❌ Email $email должен быть невалидным<br>";
    }
}

// Тест валидации пароля
$valid_passwords = ['Password123', 'MySecure1Pass'];
$invalid_passwords = ['weak', '12345678', 'PASSWORD'];

foreach ($valid_passwords as $password) {
    if (validatePassword($password)) {
        echo "✅ Пароль '$password' валиден<br>";
    } else {
        echo "❌ Пароль '$password' должен быть валиден<br>";
    }
}

foreach ($invalid_passwords as $password) {
    if (!validatePassword($password)) {
        echo "✅ Пароль '$password' правильно определен как невалидный<br>";
    } else {
        echo "❌ Пароль '$password' должен быть невалидным<br>";
    }
}

// 4. Тест функций форматирования
echo "<h2>4. Тест функций форматирования</h2>";

$test_amount = 1234.56;
$formatted = formatMoney($test_amount);
if ($formatted === '$1,234.56') {
    echo "✅ Форматирование денег работает: $formatted<br>";
} else {
    echo "❌ Ошибка форматирования денег: $formatted<br>";
}

$test_percent = 12.345;
$formatted_percent = formatPercent($test_percent);
if ($formatted_percent === '12.35%') {
    echo "✅ Форматирование процентов работает: $formatted_percent<br>";
} else {
    echo "❌ Ошибка форматирования процентов: $formatted_percent<br>";
}

// 5. Тест инвестиционных пакетов
echo "<h2>5. Тест инвестиционных пакетов</h2>";

try {
    $packages = getInvestmentPackages();
    if (is_array($packages) && count($packages) > 0) {
        echo "✅ Получение инвестиционных пакетов работает (" . count($packages) . " пакетов)<br>";
        foreach ($packages as $package) {
            echo "  - {$package['name']}: {$package['daily_rate']}% в день<br>";
        }
    } else {
        echo "❌ Нет доступных инвестиционных пакетов<br>";
    }
} catch (Exception $e) {
    echo "❌ Ошибка получения пакетов: " . $e->getMessage() . "<br>";
}

// 6. Тест flash сообщений
echo "<h2>6. Тест flash сообщений</h2>";

// Устанавливаем flash сообщение
$_SESSION['flash_message'] = 'Тестовое сообщение';
$_SESSION['flash_type'] = 'success';

$flash = getFlashMessage();
if ($flash && $flash['message'] === 'Тестовое сообщение' && $flash['type'] === 'success') {
    echo "✅ Flash сообщения работают<br>";
} else {
    echo "❌ Ошибка в flash сообщениях<br>";
}

// Проверяем, что сообщение удалилось
$flash2 = getFlashMessage();
if ($flash2 === null) {
    echo "✅ Flash сообщения правильно удаляются после чтения<br>";
} else {
    echo "❌ Flash сообщения не удаляются после чтения<br>";
}

// 7. Тест настроек
echo "<h2>7. Тест настроек</h2>";

$required_constants = [
    'DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASS',
    'SITE_URL', 'MIN_INVESTMENT', 'MAX_INVESTMENT',
    'DAILY_PROFIT_FLEXIBLE', 'DAILY_PROFIT_FIXED'
];

foreach ($required_constants as $constant) {
    if (defined($constant)) {
        echo "✅ Константа $constant определена: " . constant($constant) . "<br>";
    } else {
        echo "❌ Константа $constant не определена<br>";
    }
}

echo "<h2>Тестирование завершено!</h2>";
echo "<p><a href='index.php'>← Вернуться на главную</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #2c3e50; }
h2 { color: #34495e; border-bottom: 2px solid #ecf0f1; padding-bottom: 5px; }
</style>
