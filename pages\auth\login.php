<?php
$page_title = "Вход в систему";

// Если пользователь уже авторизован, перенаправляем в личный кабинет
if (isLoggedIn()) {
    redirect('index.php?page=dashboard');
}

// Обработка формы входа
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $result = processLogin();
    if ($result['success']) {
        $redirect_url = $_GET['redirect'] ?? 'index.php?page=dashboard';
        redirect($redirect_url, $result['message'], 'success');
    } else {
        $error_message = $result['message'];
    }
}
?>

<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="auth-card animate-fadeInUp">
                    <div class="auth-header">
                        <h3 class="mb-0">
                            <i class="fas fa-sign-in-alt"></i> Вход в систему
                        </h3>
                        <p class="mb-0 mt-2">Добро пожаловать обратно!</p>
                    </div>
                
                    <div class="auth-body">
                    <?php if (isset($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($_GET['verified'])): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> Email успешно подтвержден! Теперь вы можете войти в систему.
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($_GET['reset'])): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> Пароль успешно изменен! Войдите с новым паролем.
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="form-group">
                            <label for="login" class="form-label">
                                <i class="fas fa-user me-2"></i>Email или имя пользователя
                            </label>
                            <input type="text" class="form-control" id="login" name="login"
                                   value="<?php echo htmlspecialchars($_POST['login'] ?? ''); ?>"
                                   placeholder="Введите email или имя пользователя"
                                   required autocomplete="username">
                            <div class="invalid-feedback">
                                Введите email или имя пользователя
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>Пароль
                            </label>
                            <div class="position-relative">
                                <input type="password" class="form-control" id="password" name="password"
                                       placeholder="Введите пароль"
                                       required autocomplete="current-password">
                                <button class="btn position-absolute top-50 end-0 translate-middle-y me-2"
                                        type="button" id="togglePassword"
                                        style="background: none; border: none; color: var(--text-light-gray);">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback">
                                Введите пароль
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                                <label class="form-check-label" for="remember_me">
                                    Запомнить меня
                                </label>
                            </div>
                            <a href="index.php?page=forgot-password" class="text-decoration-none">
                                Забыли пароль?
                            </a>
                        </div>

                        <!-- Капча (простая математическая) -->
                        <?php
                        $num1 = rand(1, 10);
                        $num2 = rand(1, 10);
                        $captcha_answer = $num1 + $num2;
                        $_SESSION['captcha_answer'] = $captcha_answer;
                        ?>
                        <div class="form-group">
                            <label for="captcha" class="form-label">
                                <i class="fas fa-shield-alt me-2"></i>Решите пример: <?php echo $num1; ?> + <?php echo $num2; ?> = ?
                            </label>
                            <input type="number" class="form-control" id="captcha" name="captcha"
                                   placeholder="Введите ответ"
                                   required min="0" max="20">
                            <div class="invalid-feedback">
                                Решите математический пример
                            </div>
                        </div>
                        
                        <div class="d-grid mb-4">
                            <button type="submit" class="btn btn-primary btn-lg hover-glow">
                                <i class="fas fa-sign-in-alt me-2"></i> Войти в систему
                            </button>
                        </div>
                    </form>

                    <!-- Быстрый вход для демо -->
                    <?php if (defined('DEMO_MODE') && DEMO_MODE): ?>
                        <div class="text-center mb-4">
                            <p class="mb-2"><small class="text-muted">Демо режим:</small></p>
                            <div class="d-flex gap-2 justify-content-center">
                                <button type="button" class="btn btn-sm btn-secondary" onclick="fillDemoUser()">
                                    Демо пользователь
                                </button>
                                <button type="button" class="btn btn-sm btn-warning" onclick="fillDemoAdmin()">
                                    Демо админ
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>
                    </div>

                    <div class="auth-footer">
                        <div class="text-center">
                            <p class="mb-3">Нет аккаунта?</p>
                            <a href="index.php?page=register" class="btn btn-secondary w-100 hover-lift">
                                <i class="fas fa-user-plus me-2"></i> Создать аккаунт
                            </a>
                        </div>

                    <div class="auth-footer">
                        <p class="mb-2">Нет аккаунта?</p>
                        <a href="index.php?page=register" class="btn btn-outline-primary">
                            <i class="fas fa-user-plus"></i> Зарегистрироваться
                        </a>

                        <div class="mt-3">
                            <small>
                                <i class="fas fa-shield-alt text-success"></i>
                                Ваши данные защищены современным шифрованием
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Переключение видимости пароля
    document.getElementById('togglePassword').addEventListener('click', function() {
        const password = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (password.type === 'password') {
            password.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            password.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    
    // Автофокус на поле входа
    document.getElementById('login').focus();
});

<?php if (defined('DEMO_MODE') && DEMO_MODE): ?>
function fillDemoUser() {
    document.getElementById('login').value = '<EMAIL>';
    document.getElementById('password').value = 'demo123456';
    document.getElementById('captcha').value = <?php echo $captcha_answer; ?>;
}

function fillDemoAdmin() {
    document.getElementById('login').value = '<EMAIL>';
    document.getElementById('password').value = 'admin123456';
    document.getElementById('captcha').value = <?php echo $captcha_answer; ?>;
}
<?php endif; ?>
</script>

<?php
/**
 * Обработка входа в систему
 */
function processLogin() {
    global $conn;
    
    // Проверка CSRF токена
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        return ['success' => false, 'message' => 'Ошибка безопасности. Попробуйте еще раз.'];
    }
    
    $login = sanitizeInput($_POST['login']);
    $password = $_POST['password'];
    $captcha = intval($_POST['captcha']);
    $remember_me = isset($_POST['remember_me']);
    
    // Валидация
    if (empty($login) || empty($password)) {
        return ['success' => false, 'message' => 'Заполните все поля'];
    }
    
    // Проверка капчи
    if (!isset($_SESSION['captcha_answer']) || $captcha !== $_SESSION['captcha_answer']) {
        return ['success' => false, 'message' => 'Неверно решен пример'];
    }
    
    // Очищаем капчу
    unset($_SESSION['captcha_answer']);
    
    try {
        // Проверка лимита попыток входа
        if (!checkLoginAttempts($login)) {
            return ['success' => false, 'message' => 'Превышено количество попыток входа. Попробуйте через 15 минут.'];
        }
        
        // Поиск пользователя
        $stmt = $conn->prepare("
            SELECT id, username, email, password_hash, first_name, last_name, role, status, 
                   email_verified, balance, last_login
            FROM users 
            WHERE (email = ? OR username = ?) AND status != 'banned'
        ");
        $stmt->execute([$login, $login]);
        $user = $stmt->fetch();
        
        if (!$user) {
            recordLoginAttempt($login, false);
            return ['success' => false, 'message' => 'Неверный логин или пароль'];
        }
        
        // Проверка пароля
        if (!verifyPassword($password, $user['password_hash'])) {
            recordLoginAttempt($login, false);
            return ['success' => false, 'message' => 'Неверный логин или пароль'];
        }
        
        // Проверка статуса аккаунта
        if ($user['status'] === 'inactive') {
            return ['success' => false, 'message' => 'Аккаунт неактивен. Обратитесь в поддержку.'];
        }
        
        // Проверка подтверждения email
        if (!$user['email_verified']) {
            return ['success' => false, 'message' => 'Подтвердите email адрес. Проверьте почту.'];
        }
        
        // Успешный вход
        recordLoginAttempt($login, true);
        
        // Создание сессии
        session_regenerate_id(true);
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['user_email'] = $user['email'];
        
        // Установка cookie "Запомнить меня"
        if ($remember_me) {
            $token = generateToken();
            $expires = time() + (30 * 24 * 60 * 60); // 30 дней
            
            setcookie('remember_token', $token, $expires, '/', '', false, true);
            
            // Сохраняем токен в БД (можно создать отдельную таблицу remember_tokens)
            $stmt = $conn->prepare("
                UPDATE users 
                SET remember_token = ?, remember_token_expires = FROM_UNIXTIME(?)
                WHERE id = ?
            ");
            $stmt->execute([$token, $expires, $user['id']]);
        }
        
        // Обновление времени последнего входа
        $stmt = $conn->prepare("
            UPDATE users 
            SET last_login = NOW(), last_activity = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$user['id']]);
        
        // Логирование
        logAction('user_login', "User: {$user['username']}, Email: {$user['email']}");
        
        // Проверка на первый вход
        $welcome_message = $user['last_login'] ? 'Добро пожаловать!' : 'Добро пожаловать в GreenChain EcoFund!';
        
        return ['success' => true, 'message' => $welcome_message];
        
    } catch (Exception $e) {
        error_log("Login error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при входе в систему. Попробуйте позже.'];
    }
}
?>
