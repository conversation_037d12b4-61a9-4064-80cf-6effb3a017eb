<?php
// Скрипт для исправления базы данных
require_once 'config/config.php';

echo "<h1>Исправление базы данных GreenChain EcoFund</h1>";

try {
    // Добавляем недостающие колонки в таблицу users
    echo "<h2>Добавление недостающих колонок в таблицу users...</h2>";
    
    $columns_to_add = [
        'remember_token' => 'VARCHAR(255) NULL',
        'remember_token_expires' => 'TIMESTAMP NULL',
        'password_reset_token' => 'VARCHAR(255) NULL', 
        'password_reset_expires' => 'TIMESTAMP NULL',
        'email_verification_token' => 'VARCHAR(255) NULL',
        'email_verified' => 'BOOLEAN DEFAULT FALSE',
        'referral_code' => 'VARCHAR(50) NULL',
        'total_invested' => 'DECIMAL(15,2) DEFAULT 0.00',
        'total_profit' => 'DECIMAL(15,2) DEFAULT 0.00',
        'last_activity' => 'TIMESTAMP NULL'
    ];
    
    foreach ($columns_to_add as $column => $definition) {
        try {
            $stmt = $conn->query("SHOW COLUMNS FROM users LIKE '$column'");
            if ($stmt->rowCount() == 0) {
                $conn->exec("ALTER TABLE users ADD COLUMN $column $definition");
                echo "✅ Добавлена колонка: $column<br>";
            } else {
                echo "ℹ️ Колонка уже существует: $column<br>";
            }
        } catch (Exception $e) {
            echo "❌ Ошибка добавления колонки $column: " . $e->getMessage() . "<br>";
        }
    }
    
    // Создаем недостающие таблицы
    echo "<h2>Создание недостающих таблиц...</h2>";
    
    // Таблица activity_logs
    try {
        $conn->exec("
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NULL,
                action VARCHAR(100) NOT NULL,
                details TEXT NULL,
                ip_address VARCHAR(45) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
            )
        ");
        echo "✅ Таблица activity_logs создана<br>";
    } catch (Exception $e) {
        echo "❌ Ошибка создания activity_logs: " . $e->getMessage() . "<br>";
    }
    
    // Таблица login_attempts
    try {
        $conn->exec("
            CREATE TABLE IF NOT EXISTS login_attempts (
                id INT PRIMARY KEY AUTO_INCREMENT,
                email VARCHAR(255) NOT NULL,
                success BOOLEAN DEFAULT FALSE,
                ip_address VARCHAR(45) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");
        echo "✅ Таблица login_attempts создана<br>";
    } catch (Exception $e) {
        echo "❌ Ошибка создания login_attempts: " . $e->getMessage() . "<br>";
    }
    
    // Таблица settings
    try {
        $conn->exec("
            CREATE TABLE IF NOT EXISTS settings (
                id INT PRIMARY KEY AUTO_INCREMENT,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT NULL,
                description TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        echo "✅ Таблица settings создана<br>";
    } catch (Exception $e) {
        echo "❌ Ошибка создания settings: " . $e->getMessage() . "<br>";
    }
    
    // Таблица education_categories
    try {
        $conn->exec("
            CREATE TABLE IF NOT EXISTS education_categories (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(100) NOT NULL,
                description TEXT NULL,
                icon VARCHAR(50) NULL,
                sort_order INT DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");
        echo "✅ Таблица education_categories создана<br>";
    } catch (Exception $e) {
        echo "❌ Ошибка создания education_categories: " . $e->getMessage() . "<br>";
    }
    
    // Таблица education_articles
    try {
        $conn->exec("
            CREATE TABLE IF NOT EXISTS education_articles (
                id INT PRIMARY KEY AUTO_INCREMENT,
                category_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                excerpt TEXT NULL,
                featured_image VARCHAR(255) NULL,
                is_published BOOLEAN DEFAULT FALSE,
                is_featured BOOLEAN DEFAULT FALSE,
                views_count INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES education_categories(id) ON DELETE CASCADE
            )
        ");
        echo "✅ Таблица education_articles создана<br>";
    } catch (Exception $e) {
        echo "❌ Ошибка создания education_articles: " . $e->getMessage() . "<br>";
    }
    
    // Таблица user_achievements
    try {
        $conn->exec("
            CREATE TABLE IF NOT EXISTS user_achievements (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                achievement_type VARCHAR(50) NOT NULL,
                achievement_name VARCHAR(100) NOT NULL,
                description TEXT NULL,
                icon VARCHAR(50) NULL,
                points INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ");
        echo "✅ Таблица user_achievements создана<br>";
    } catch (Exception $e) {
        echo "❌ Ошибка создания user_achievements: " . $e->getMessage() . "<br>";
    }
    
    // Таблица map_projects
    try {
        $conn->exec("
            CREATE TABLE IF NOT EXISTS map_projects (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                description TEXT NOT NULL,
                location VARCHAR(255) NOT NULL,
                latitude DECIMAL(10, 8) NOT NULL,
                longitude DECIMAL(11, 8) NOT NULL,
                project_type VARCHAR(50) NOT NULL,
                status VARCHAR(50) DEFAULT 'active',
                investment_amount DECIMAL(15,2) DEFAULT 0.00,
                roi_percentage DECIMAL(5,2) DEFAULT 0.00,
                images JSON NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");
        echo "✅ Таблица map_projects создана<br>";
    } catch (Exception $e) {
        echo "❌ Ошибка создания map_projects: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>Добавление базовых данных...</h2>";
    
    // Добавляем базовые настройки
    $settings = [
        ['referral_rates', '[5, 3, 1]', 'Проценты реферальных бонусов по уровням'],
        ['site_maintenance', '0', 'Режим технического обслуживания'],
        ['min_withdrawal', '10', 'Минимальная сумма для вывода'],
        ['withdrawal_fee', '2', 'Комиссия за вывод средств (%)']
    ];
    
    foreach ($settings as $setting) {
        try {
            $stmt = $conn->prepare("INSERT IGNORE INTO settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
            $stmt->execute($setting);
            echo "✅ Настройка добавлена: {$setting[0]}<br>";
        } catch (Exception $e) {
            echo "❌ Ошибка добавления настройки {$setting[0]}: " . $e->getMessage() . "<br>";
        }
    }
    
    // Обновляем реферальные коды для существующих пользователей
    try {
        $conn->exec("UPDATE users SET referral_code = CONCAT('REF', LPAD(id, 6, '0')) WHERE referral_code IS NULL OR referral_code = ''");
        echo "✅ Реферальные коды обновлены<br>";
    } catch (Exception $e) {
        echo "❌ Ошибка обновления реферальных кодов: " . $e->getMessage() . "<br>";
    }
    
    // Обновляем статус email_verified для существующих пользователей
    try {
        $conn->exec("UPDATE users SET email_verified = TRUE WHERE email_verified IS NULL");
        echo "✅ Статус подтверждения email обновлен<br>";
    } catch (Exception $e) {
        echo "❌ Ошибка обновления email_verified: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>✅ База данных успешно исправлена!</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ Критическая ошибка: " . $e->getMessage() . "</h2>";
}

echo "<p><a href='index.php'>← Вернуться на главную</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #2c3e50; }
h2 { color: #34495e; border-bottom: 2px solid #ecf0f1; padding-bottom: 5px; }
</style>
