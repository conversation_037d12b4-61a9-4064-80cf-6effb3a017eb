<?php
// Простая тестовая страница для проверки кнопок инвестирования
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Симуляция авторизации для тестирования
if (isset($_GET['login'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Test User';
    $_SESSION['user_role'] = 'user';
    header('Location: test-real-buttons.php');
    exit;
}

if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: test-real-buttons.php');
    exit;
}

$page_title = "Тест кнопок инвестирования";
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <PERSON><PERSON><PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/luxury-eco-design.css" rel="stylesheet">
    <link href="assets/css/adaptive-contrast.css" rel="stylesheet">
</head>
<body>
    <!-- Скрытый элемент с данными пользователя для JavaScript -->
    <?php if (isLoggedIn()): ?>
        <div id="user-data" style="display: none;" 
             data-user-id="<?php echo $_SESSION['user_id']; ?>" 
             data-user-name="<?php echo htmlspecialchars($_SESSION['user_name']); ?>" 
             data-user-role="<?php echo $_SESSION['user_role']; ?>">
        </div>
    <?php endif; ?>

    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h2 class="text-center mb-4">🧪 Реальный тест кнопок "Инвестировать"</h2>
                
                <!-- Статус авторизации -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Статус авторизации</h5>
                    </div>
                    <div class="card-body">
                        <?php if (isLoggedIn()): ?>
                            <p>✅ <strong>Авторизован как:</strong> <?php echo $_SESSION['user_name']; ?></p>
                            <a href="?logout" class="btn btn-warning">Выйти</a>
                        <?php else: ?>
                            <p>❌ <strong>Не авторизован</strong></p>
                            <a href="?login" class="btn btn-success">Войти (тест)</a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Тест кнопок -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Тестовые кнопки инвестирования</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6>Кнопка без параметров</h6>
                                        <button class="btn btn-primary invest-btn">
                                            <i class="fas fa-chart-line"></i> Инвестировать
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6>Кнопка с типом пакета</h6>
                                        <button class="btn btn-success invest-btn" data-package-type="flexible">
                                            <i class="fas fa-chart-line"></i> Гибкий пакет
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6>Кнопка с ID пакета</h6>
                                        <button class="btn btn-warning invest-btn" data-package-id="1" data-package-type="fixed">
                                            <i class="fas fa-chart-line"></i> Пакет #1
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Консоль логов -->
                <div class="card">
                    <div class="card-header">
                        <h5>Консоль браузера</h5>
                        <small class="text-muted">Откройте консоль браузера (F12) для просмотра логов</small>
                    </div>
                    <div class="card-body">
                        <div id="console-output" style="height: 300px; overflow-y: auto; background: #000; color: #0f0; padding: 10px; font-family: monospace; border-radius: 5px;">
                            <div>Консоль JavaScript будет отображаться здесь...</div>
                        </div>
                    </div>
                </div>

                <!-- Навигация -->
                <div class="card mt-4">
                    <div class="card-body text-center">
                        <h6>Навигация для тестирования</h6>
                        <a href="index.php" class="btn btn-outline-primary">Главная страница</a>
                        <a href="index.php?page=invest" class="btn btn-outline-success">Страница инвестиций</a>
                        <a href="test-invest-buttons.html" class="btn btn-outline-info">Симуляция тест</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
        // Перехват console.log для отображения в интерфейсе
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#f00' : type === 'warn' ? '#ff0' : '#0f0';
            consoleOutput.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        // Перехват ошибок JavaScript
        window.addEventListener('error', function(e) {
            console.error(`JavaScript Error: ${e.message} at ${e.filename}:${e.lineno}`);
        });

        // Инициализация
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== ТЕСТ КНОПОК ИНВЕСТИРОВАНИЯ ЗАПУЩЕН ===');
            console.log('Проверяем загрузку main.js...');

            // Проверяем, загружен ли main.js
            if (typeof handleInvestClick === 'function') {
                console.log('✅ Функция handleInvestClick найдена');
            } else {
                console.error('❌ Функция handleInvestClick НЕ найдена');
            }

            if (typeof showInvestModal === 'function') {
                console.log('✅ Функция showInvestModal найдена');
            } else {
                console.error('❌ Функция showInvestModal НЕ найдена');
            }

            // Проверяем переменные
            console.log('Переменная isLoggedIn:', typeof isLoggedIn !== 'undefined' ? isLoggedIn : 'НЕ ОПРЕДЕЛЕНА');
            console.log('Переменная currentUser:', typeof currentUser !== 'undefined' ? currentUser : 'НЕ ОПРЕДЕЛЕНА');

            // Проверяем элемент user-data
            const userDataElement = document.getElementById('user-data');
            if (userDataElement) {
                console.log('✅ Элемент #user-data найден:', userDataElement.dataset);
            } else {
                console.log('❌ Элемент #user-data НЕ найден');
            }

            // Проверяем кнопки
            const investButtons = document.querySelectorAll('.invest-btn');
            console.log(`Найдено кнопок .invest-btn: ${investButtons.length}`);

            // Добавляем тестовый обработчик
            investButtons.forEach((btn, index) => {
                btn.addEventListener('click', function(e) {
                    console.log(`🔥 ПРЯМОЙ КЛИК по кнопке ${index + 1}:`, {
                        target: e.target,
                        packageId: e.target.dataset.packageId,
                        packageType: e.target.dataset.packageType
                    });
                });
            });

            console.log('Инициализация завершена');

            // КРИТИЧЕСКИЙ ТЕСТ: Добавляем собственный обработчик
            console.log('🧪 Добавляем тестовый обработчик событий...');
            document.addEventListener('click', function(e) {
                if (e.target.matches('.invest-btn')) {
                    console.log('🎯 ТЕСТОВЫЙ ОБРАБОТЧИК: Клик по .invest-btn перехвачен!');
                    console.log('Target:', e.target);
                    console.log('Data attributes:', e.target.dataset);

                    // Проверяем, вызывается ли основной обработчик
                    if (typeof handleInvestClick === 'function') {
                        console.log('🔄 Вызываем handleInvestClick...');
                        try {
                            handleInvestClick(e);
                        } catch (error) {
                            console.error('❌ Ошибка в handleInvestClick:', error);
                        }
                    } else {
                        console.error('❌ handleInvestClick не найден!');
                    }
                }
            });

            console.log('✅ Тестовый обработчик добавлен');
        });
    </script>
</body>
</html>
