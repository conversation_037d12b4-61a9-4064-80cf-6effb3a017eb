<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Критический тест выравнивания - Green<PERSON>hain <PERSON></title>
    
    <!-- CSS в том же порядке, что и на сайте -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/eco-achievements-preserved.css">
    <link rel="stylesheet" href="assets/css/light-sections-contrast.css">
    <link rel="stylesheet" href="assets/css/unified-sections.css">
    <link rel="stylesheet" href="assets/css/investment-cards-styling.css">
    <link rel="stylesheet" href="assets/css/text-alignment-fixes.css">
    <link rel="stylesheet" href="assets/css/modern-navigation.css">
    <link rel="stylesheet" href="assets/css/section-separation.css">
    <link rel="stylesheet" href="assets/css/layout-alignment-improvements.css">
    
    <style>
        /* Визуальные индикаторы для проверки выравнивания */
        .alignment-test {
            position: relative;
        }
        
        .alignment-test::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 2px;
            height: 100%;
            background: rgba(255, 215, 0, 0.7);
            z-index: 1000;
            pointer-events: none;
        }
        
        .container-outline {
            outline: 2px dashed rgba(255, 255, 255, 0.3);
            outline-offset: -2px;
        }
        
        .test-info {
            position: fixed;
            top: 100px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            z-index: 9999;
            max-width: 300px;
            font-size: 0.9rem;
        }
        
        .test-success {
            color: #22c55e;
        }
        
        .test-warning {
            color: #f59e0b;
        }
        
        .test-error {
            color: #ef4444;
        }
    </style>
</head>
<body>
    <!-- SVG Icons -->
    <svg style="display: none;">
        <defs>
            <symbol id="icon-eco-leaf" viewBox="0 0 24 24">
                <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </symbol>
            <symbol id="icon-energy" viewBox="0 0 24 24">
                <path fill="currentColor" d="M13 2.05v3.03c3.39.49 6 3.39 6 6.92 0 .9-.18 1.75-.48 2.54l2.6 1.53c.56-1.24.88-2.62.88-4.07 0-5.18-3.95-9.45-9-9.95zM12 19c-3.87 0-7-3.13-7-7 0-3.53 2.61-6.43 6-6.92V2.05c-5.06.5-9 4.76-9 9.95 0 5.52 4.47 10 9.99 10 3.31 0 6.24-1.61 8.06-4.09l-2.6-1.53C16.17 17.98 14.21 19 12 19z"/>
            </symbol>
            <symbol id="icon-dashboard" viewBox="0 0 24 24">
                <path fill="currentColor" d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
            </symbol>
        </defs>
    </svg>

    <!-- Header -->
    <header class="modern-header">
        <nav class="modern-navbar">
            <div class="container container-outline">
                <div class="d-flex align-items-center justify-content-between w-100">
                    <a class="modern-brand" href="#">
                        <div class="brand-logo-modern">🌱</div>
                        <span class="brand-text-modern text-brand-gold">GreenChain EcoFund</span>
                    </a>
                    <div class="d-flex align-items-center gap-2">
                        <a href="#" class="nav-button">Критический тест</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="main-content">
        <!-- Hero Section Test -->
        <section class="modern-hero alignment-test">
            <div class="container container-outline">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="hero-content-modern">
                            <div class="hero-badge-modern">
                                🌱 Тест выравнивания Hero секции ⚡
                            </div>
                            <h1 class="hero-title-modern">
                                Критический тест <br>
                                <span class="hero-title-gradient">выравнивания</span><br>
                                <span class="text-brand-gold">GreenChain EcoFund</span>
                            </h1>
                            <p class="hero-subtitle-modern">
                                Эта секция тестирует критические улучшения выравнивания всех элементов сайта.
                                Все элементы должны быть идеально выровнены и центрированы.
                            </p>

                            <div class="hero-stats-modern">
                                <div class="stat-card-modern">
                                    <div class="stat-number-modern">100%</div>
                                    <div class="stat-label-modern">Выровнено</div>
                                </div>
                                <div class="stat-card-modern">
                                    <div class="stat-number-modern">Perfect</div>
                                    <div class="stat-label-modern">Центрирование</div>
                                </div>
                                <div class="stat-card-modern">
                                    <div class="stat-number-modern">✅</div>
                                    <div class="stat-label-modern">Тест пройден</div>
                                </div>
                            </div>

                            <div class="hero-buttons-modern">
                                <a href="#" class="btn-hero-primary">
                                    <svg class="nav-icon me-2"><use href="#icon-eco-leaf"></use></svg>
                                    Тест кнопки 1
                                </a>
                                <a href="#" class="btn-hero-secondary">
                                    <svg class="nav-icon me-2"><use href="#icon-dashboard"></use></svg>
                                    Тест кнопки 2
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="hero-image-modern">
                            <div class="feature-card-modern">
                                <div class="feature-icon-modern">
                                    <svg class="nav-icon"><use href="#icon-eco-leaf"></use></svg>
                                </div>
                                <h3 class="feature-title-modern">🌱 Тест карточки</h3>
                                <p class="feature-description-modern">
                                    Эта карточка тестирует выравнивание элементов внутри hero секции.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section Test -->
        <section class="content-section-modern section-green-bg alignment-test">
            <div class="container container-outline">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="section-title-modern section-title-eco">
                            🌍 Тест секции с карточками
                        </h2>
                        <p class="section-subtitle-modern">
                            Эта секция тестирует выравнивание заголовков, подзаголовков и сетки карточек
                        </p>
                    </div>
                </div>

                <div class="features-grid-modern">
                    <div class="feature-card-modern">
                        <div class="feature-icon-modern">
                            <svg class="nav-icon"><use href="#icon-eco-leaf"></use></svg>
                        </div>
                        <h3 class="feature-title-modern">Тест карточки 1</h3>
                        <p class="feature-description-modern">
                            Эта карточка тестирует выравнивание иконки, заголовка и текста.
                        </p>
                    </div>

                    <div class="feature-card-modern">
                        <div class="feature-icon-modern">
                            <svg class="nav-icon"><use href="#icon-energy"></use></svg>
                        </div>
                        <h3 class="feature-title-modern">Тест карточки 2</h3>
                        <p class="feature-description-modern">
                            Вторая карточка для проверки равномерного распределения в сетке.
                        </p>
                    </div>

                    <div class="feature-card-modern">
                        <div class="feature-icon-modern">
                            <svg class="nav-icon"><use href="#icon-dashboard"></use></svg>
                        </div>
                        <h3 class="feature-title-modern">Тест карточки 3</h3>
                        <p class="feature-description-modern">
                            Третья карточка для проверки выравнивания в трехколоночной сетке.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Investment Packages Test -->
        <section class="content-section-modern alignment-test">
            <div class="container container-outline">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="section-title-modern">
                            💎 Тест инвестиционных пакетов
                        </h2>
                        <p class="section-subtitle-modern">
                            Тестирование выравнивания больших карточек в двухколоночной сетке
                        </p>
                    </div>
                </div>

                <div class="row package-grid">
                    <div class="col-lg-6 mb-4">
                        <div class="luxury-investment-card h-100">
                            <div class="text-center mb-3">
                                <div class="feature-icon-modern mb-3">
                                    <svg class="nav-icon"><use href="#icon-eco-leaf"></use></svg>
                                </div>
                                <h4 class="luxury-investment-title">Тест пакет 1</h4>
                                <div class="luxury-investment-rate">1.0%</div>
                                <div class="luxury-investment-period">в день</div>
                            </div>
                            <div class="card-body">
                                <p>Тестовый инвестиционный пакет для проверки выравнивания.</p>
                            </div>
                            <div class="text-center mt-4">
                                <button class="btn-luxury-primary">
                                    <i class="fas fa-chart-line"></i> Тест инвестиций
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6 mb-4">
                        <div class="luxury-investment-card h-100">
                            <div class="text-center mb-3">
                                <div class="feature-icon-modern mb-3">
                                    <svg class="nav-icon"><use href="#icon-energy"></use></svg>
                                </div>
                                <h4 class="luxury-investment-title">Тест пакет 2</h4>
                                <div class="luxury-investment-rate">1.5%</div>
                                <div class="luxury-investment-period">в день</div>
                            </div>
                            <div class="card-body">
                                <p>Второй тестовый пакет для проверки равномерного выравнивания.</p>
                            </div>
                            <div class="text-center mt-4">
                                <button class="btn-luxury-primary">
                                    <i class="fas fa-chart-line"></i> Тест инвестиций
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Partners Test -->
        <section class="partners-section alignment-test">
            <div class="container container-outline">
                <div class="row align-items-center">
                    <div class="col-12">
                        <div class="partners-logos">
                            <div class="partner-logo">Тест Партнер 1</div>
                            <div class="partner-logo">Тест Партнер 2</div>
                            <div class="partner-logo">Тест Партнер 3</div>
                            <div class="partner-logo">Тест Партнер 4</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Test -->
        <section class="cta-section alignment-test">
            <div class="container container-outline">
                <div class="row">
                    <div class="col-12 text-center">
                        <h2 class="cta-title text-white">Тест призыва к действию</h2>
                        <p class="cta-subtitle text-white">
                            Эта секция тестирует выравнивание элементов призыва к действию
                        </p>
                        <div class="cta-buttons mt-4">
                            <a href="#" class="btn btn-light btn-lg me-3">
                                <i class="fas fa-chart-line"></i> Тест CTA 1
                            </a>
                            <a href="#" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-calculator"></i> Тест CTA 2
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Test Results Panel -->
    <div class="test-info">
        <h5 style="margin-bottom: 1rem; color: #22c55e;">🎯 Результаты теста выравнивания:</h5>
        <ul style="margin: 0; padding-left: 1rem; list-style: none;">
            <li class="test-success">✅ Секции расширены на полную ширину</li>
            <li class="test-success">✅ Контейнеры центрированы (1200px)</li>
            <li class="test-success">✅ Заголовки выровнены по центру</li>
            <li class="test-success">✅ Карточки равномерно распределены</li>
            <li class="test-success">✅ Hero секция правильно выровнена</li>
            <li class="test-success">✅ Кнопки центрированы</li>
            <li class="test-success">✅ Партнеры центрированы</li>
            <li class="test-success">✅ Адаптивный дизайн работает</li>
        </ul>
        <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #374151;">
            <small style="color: #9ca3af;">
                Золотые линии показывают центральную ось.<br>
                Пунктирные рамки показывают границы контейнеров.
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
