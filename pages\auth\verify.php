<?php
$page_title = "Подтверждение email";

$token = $_GET['token'] ?? '';
$result = null;

if ($token) {
    $result = verifyEmailToken($token);
}
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-6 col-md-8">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient text-white text-center">
                    <h3 class="mb-0">
                        <i class="fas fa-envelope-check"></i> Подтверждение email
                    </h3>
                </div>
                
                <div class="card-body p-4 text-center">
                    <?php if ($result): ?>
                        <?php if ($result['success']): ?>
                            <div class="success-animation mb-4">
                                <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                            </div>
                            <h4 class="text-success mb-3">Email успешно подтвержден!</h4>
                            <p class="text-muted mb-4">
                                Ваш аккаунт активирован. Теперь вы можете войти в систему и начать инвестировать.
                            </p>
                            
                            <?php if ($result['bonus']): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-gift"></i> 
                                    Вы получили бонус $<?php echo number_format($result['bonus'], 2); ?> за подтверждение email!
                                </div>
                            <?php endif; ?>
                            
                            <div class="d-grid gap-2">
                                <a href="index.php?page=login&verified=1" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt"></i> Войти в систему
                                </a>
                                <a href="index.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-home"></i> На главную
                                </a>
                            </div>
                            
                        <?php else: ?>
                            <div class="error-animation mb-4">
                                <i class="fas fa-times-circle text-danger" style="font-size: 4rem;"></i>
                            </div>
                            <h4 class="text-danger mb-3">Ошибка подтверждения</h4>
                            <p class="text-muted mb-4">
                                <?php echo $result['message']; ?>
                            </p>
                            
                            <div class="d-grid gap-2">
                                <a href="index.php?page=resend-verification" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> Отправить ссылку повторно
                                </a>
                                <a href="index.php?page=login" class="btn btn-outline-secondary">
                                    <i class="fas fa-sign-in-alt"></i> Войти в систему
                                </a>
                            </div>
                        <?php endif; ?>
                        
                    <?php else: ?>
                        <div class="warning-animation mb-4">
                            <i class="fas fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                        </div>
                        <h4 class="text-warning mb-3">Недействительная ссылка</h4>
                        <p class="text-muted mb-4">
                            Ссылка для подтверждения email недействительна или истекла.
                        </p>
                        
                        <div class="d-grid gap-2">
                            <a href="index.php?page=resend-verification" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> Отправить новую ссылку
                            </a>
                            <a href="index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-home"></i> На главную
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Информация о преимуществах -->
            <div class="card mt-4 border-0 bg-light">
                <div class="card-body">
                    <h6 class="card-title text-center mb-3">
                        <i class="fas fa-star text-warning"></i> Что дает подтверждение email?
                    </h6>
                    <div class="row text-center">
                        <div class="col-md-4 mb-3">
                            <i class="fas fa-shield-alt text-success mb-2" style="font-size: 2rem;"></i>
                            <h6>Безопасность</h6>
                            <small class="text-muted">Защита аккаунта от несанкционированного доступа</small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <i class="fas fa-bell text-info mb-2" style="font-size: 2rem;"></i>
                            <h6>Уведомления</h6>
                            <small class="text-muted">Получение важных уведомлений о транзакциях</small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <i class="fas fa-money-bill-wave text-primary mb-2" style="font-size: 2rem;"></i>
                            <h6>Полный доступ</h6>
                            <small class="text-muted">Возможность выводить средства без ограничений</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.success-animation i,
.error-animation i,
.warning-animation i {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
</style>

<?php
/**
 * Подтверждение email токена
 */
function verifyEmailToken($token) {
    global $conn;
    
    try {
        // Поиск пользователя по токену
        $stmt = $conn->prepare("
            SELECT id, email, first_name, email_verified, balance
            FROM users 
            WHERE email_verification_token = ? 
            AND status != 'banned'
        ");
        $stmt->execute([$token]);
        $user = $stmt->fetch();
        
        if (!$user) {
            return ['success' => false, 'message' => 'Недействительная ссылка подтверждения.'];
        }
        
        if ($user['email_verified']) {
            return ['success' => false, 'message' => 'Email уже подтвержден.'];
        }
        
        $conn->beginTransaction();
        
        // Подтверждаем email и активируем аккаунт
        $stmt = $conn->prepare("
            UPDATE users 
            SET email_verified = 1, 
                email_verification_token = NULL, 
                status = 'active'
            WHERE id = ?
        ");
        $stmt->execute([$user['id']]);
        
        // Бонус за подтверждение email
        $verification_bonus = 5.00;
        $new_balance = $user['balance'] + $verification_bonus;
        
        $stmt = $conn->prepare("UPDATE users SET balance = ? WHERE id = ?");
        $stmt->execute([$new_balance, $user['id']]);
        
        // Создаем транзакцию бонуса
        $stmt = $conn->prepare("
            INSERT INTO transactions (user_id, type, amount, balance_before, balance_after, description) 
            VALUES (?, 'deposit', ?, ?, ?, 'Бонус за подтверждение email')
        ");
        $stmt->execute([$user['id'], $verification_bonus, $user['balance'], $new_balance]);
        
        // Создаем уведомление
        $stmt = $conn->prepare("
            INSERT INTO notifications (user_id, title, message, type) 
            VALUES (?, ?, ?, 'success')
        ");
        $stmt->execute([
            $user['id'],
            'Добро пожаловать!',
            'Ваш email подтвержден. Вы получили бонус $' . number_format($verification_bonus, 2)
        ]);
        
        // Логирование
        logAction('email_verified', "User ID: {$user['id']}, Email: {$user['email']}");
        
        $conn->commit();
        
        return [
            'success' => true, 
            'message' => 'Email успешно подтвержден!',
            'bonus' => $verification_bonus
        ];
        
    } catch (Exception $e) {
        $conn->rollBack();
        error_log("Email verification error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при подтверждении email.'];
    }
}
?>
