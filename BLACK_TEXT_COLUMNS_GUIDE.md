# Руководство по использованию черного текста в колонках - GreenChain EcoFund

## Обзор

В системе адаптивной контрастности GreenChain EcoFund добавлены специальные CSS классы для применения черного цвета текста (#000000) в определенных колонках и элементах.

## Доступные CSS классы

### 1. Основные классы для колонок

#### `.black-text-column` - Черный текст для любых колонок
```html
<div class="col-lg-6 black-text-column">
    <p>Весь текст в этой колонке будет черным</p>
</div>
```

#### `.col-black-text` - Альтернативный класс для колонок
```html
<div class="col-md-4 col-black-text">
    <h3>Заголовок</h3>
    <p>Описание</p>
</div>
```

### 2. Классы для таблиц

#### `.table-black-text` - Черный текст для всей таблицы
```html
<table class="table table-black-text">
    <thead>
        <tr>
            <th>Заголовок 1</th>
            <th>Заголовок 2</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Данные 1</td>
            <td>Данные 2</td>
        </tr>
    </tbody>
</table>
```

#### `.col-black` / `.black-column` - Черный текст для конкретных колонок таблицы
```html
<table class="table">
    <thead>
        <tr>
            <th>Обычная колонка</th>
            <th class="black-column">Черная колонка</th>
            <th class="col-black">Еще одна черная</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Обычный текст</td>
            <td class="black-column">Черный текст</td>
            <td class="col-black">Черный текст</td>
        </tr>
    </tbody>
</table>
```

### 3. Классы для карточек

#### `.stats-card.black-text` - Черный текст для статистических карточек
```html
<div class="stats-card black-text">
    <div class="stats-value">$125,000</div>
    <div class="stats-label">Баланс</div>
</div>
```

#### `.investment-card.black-text` - Черный текст для инвестиционных карточек
```html
<div class="investment-card black-text">
    <h5 class="investment-title">Солнечная ферма</h5>
    <div class="investment-amount">$50,000</div>
</div>
```

#### `.leaderboard-item.black-text` - Черный текст для элементов лидерборда
```html
<div class="leaderboard-item black-text">
    <div class="leader-name">Иван Петров</div>
    <div class="leader-stats">$25,000</div>
</div>
```

### 4. Bootstrap колонки с черным текстом

Для любых Bootstrap колонок можно добавить класс `.black-text`:

```html
<!-- Примеры для разных размеров экранов -->
<div class="col-lg-3 black-text">Черный текст на больших экранах</div>
<div class="col-md-6 black-text">Черный текст на средних экранах</div>
<div class="col-12 black-text">Черный текст на всех экранах</div>
```

### 5. Специальные классы

#### `.black-text-force` - Принудительный черный текст
```html
<div class="black-text-force">
    <p>Этот текст всегда будет черным, независимо от других стилей</p>
</div>
```

#### `.black-text-smart` - Умный черный текст
```html
<div class="black-text-smart">
    <p>Черный на светлом фоне, белый на темном для читаемости</p>
</div>
```

## Примеры использования

### Пример 1: Дашборд с черным текстом в статистике

```html
<div class="row">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card black-text">
            <div class="stats-icon">
                <i class="fas fa-wallet"></i>
            </div>
            <div class="stats-content">
                <div class="stats-value">$125,000</div>
                <div class="stats-label">Доступный баланс</div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3 black-text">
        <div class="stats-card">
            <div class="stats-value">$85,000</div>
            <div class="stats-label">Всего инвестировано</div>
        </div>
    </div>
</div>
```

### Пример 2: Таблица транзакций с черными колонками

```html
<div class="table-responsive">
    <table class="table table-hover">
        <thead>
            <tr>
                <th>Дата</th>
                <th class="black-column">Тип операции</th>
                <th>Сумма</th>
                <th class="col-black">Статус</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>15.06.2024</td>
                <td class="black-column">Инвестиция</td>
                <td>$5,000</td>
                <td class="col-black">Активна</td>
            </tr>
        </tbody>
    </table>
</div>
```

### Пример 3: Лидерборд с черным текстом

```html
<div class="row">
    <div class="col-lg-4">
        <div class="leaderboard-item black-text">
            <div class="leader-position">1</div>
            <div class="leader-name">Александр Иванов</div>
            <div class="leader-stats">$125,000</div>
        </div>
    </div>
</div>
```

## Технические детали

### CSS Специфичность
Все классы используют `!important` для гарантированного применения:
```css
.black-text-force * {
    color: #000000 !important;
    text-shadow: none !important;
}
```

### Совместимость с адаптивной системой
Классы работают совместно с системой адаптивной контрастности:
- `.black-text-smart` автоматически адаптируется к фону
- На темных секциях становится белым для читаемости
- На светлых секциях остается черным

### Поддерживаемые элементы
- Все текстовые элементы (h1-h6, p, span, div)
- Таблицы и их ячейки
- Карточки и их содержимое
- Bootstrap колонки всех размеров
- Кнопки и формы (с ограничениями)

## Рекомендации по использованию

### ✅ Когда использовать:
1. **Важная информация** - для выделения критических данных
2. **Финансовые показатели** - суммы, проценты, статистика
3. **Статусы операций** - активные, завершенные, ошибки
4. **Таблицы данных** - для улучшения читаемости конкретных колонок

### ⚠️ Осторожно:
1. **Темные фоны** - используйте `.black-text-smart` вместо `.black-text-force`
2. **Контрастность** - проверяйте соответствие WCAG стандартам
3. **Переизбыток** - не применяйте ко всем элементам сразу

### ❌ Не рекомендуется:
1. **Навигация** - может нарушить общий дизайн
2. **Кнопки** - лучше использовать стандартные стили
3. **Весь контент** - потеряется иерархия информации

## Тестирование

Для тестирования всех возможностей откройте:
```
http://127.0.0.1:8080/test-contrast.html
```

В разделе "Тест: Черный текст в колонках" представлены все примеры использования.

## Интеграция в существующие страницы

### Шаг 1: Убедитесь, что подключен CSS файл
```html
<link rel="stylesheet" href="assets/css/adaptive-contrast.css">
```

### Шаг 2: Добавьте нужные классы к элементам
```html
<!-- Было -->
<div class="col-lg-6">
    <div class="stats-card">
        <div class="stats-value">$125,000</div>
    </div>
</div>

<!-- Стало -->
<div class="col-lg-6 black-text">
    <div class="stats-card">
        <div class="stats-value">$125,000</div>
    </div>
</div>
```

### Шаг 3: Проверьте результат
Убедитесь, что текст стал черным и остается читаемым на всех фонах.

## Поддержка браузеров

- ✅ Chrome 88+
- ✅ Firefox 85+  
- ✅ Safari 14+
- ✅ Edge 88+
- ⚠️ IE 11 (ограниченная поддержка)

## Заключение

Система черного текста в колонках предоставляет гибкие возможности для точного контроля цвета текста в различных элементах интерфейса GreenChain EcoFund, сохраняя при этом общую концепцию адаптивной контрастности и соответствие стандартам доступности.
