    </main>

    <!-- Footer -->
    <footer class="modern-footer">
        <div class="container">
            <div class="footer-content">
                <div class="row">
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-section-title text-brand-gold">🌱 GreenChain EcoFund</h5>
                            <p class="company-description">
                                Инновационная инвестиционная платформа, объединяющая экологические проекты и экомайнинг
                                для создания устойчивого будущего планеты. Присоединяйтесь к зеленой революции!
                            </p>
                            <div class="social-links-modern">
                                <a href="#" class="social-link-modern facebook footer-glow">
                                    <svg class="social-icon"><use href="#icon-facebook"></use></svg>
                                </a>
                                <a href="#" class="social-link-modern twitter">
                                    <svg class="social-icon"><use href="#icon-twitter"></use></svg>
                                </a>
                                <a href="#" class="social-link-modern linkedin">
                                    <svg class="social-icon"><use href="#icon-linkedin"></use></svg>
                                </a>
                                <a href="#" class="social-link-modern telegram">
                                    <svg class="social-icon"><use href="#icon-telegram"></use></svg>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-section-title">⚡ Платформа</h5>
                            <ul class="footer-links-modern">
                                <li><a href="index.php">Главная</a></li>
                                <li><a href="index.php?page=map">Карта проектов</a></li>
                                <li><a href="index.php?page=calculator">Калькулятор доходности</a></li>
                                <li><a href="index.php?page=education">Эко-обучение</a></li>
                                <li><a href="index.php?page=leaderboard">Лидерборд</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-section-title">💰 Инвестиции</h5>
                            <ul class="footer-links-modern">
                                <li><a href="index.php?page=invest">Инвестировать</a></li>
                                <li><a href="index.php?page=withdraw">Вывод средств</a></li>
                                <li><a href="index.php?page=referrals">Реферальная программа</a></li>
                                <li><a href="index.php?page=tasks">Эко-задания</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-section-title">🛠️ Поддержка</h5>
                            <ul class="footer-links-modern">
                                <li><a href="#" data-bs-toggle="modal" data-bs-target="#supportModal">Техподдержка 24/7</a></li>
                                <li><a href="#" data-bs-toggle="modal" data-bs-target="#faqModal">Частые вопросы</a></li>
                                <li><a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Условия использования</a></li>
                                <li><a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">Конфиденциальность</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-section-title">📞 Контакты</h5>
                            <div class="contact-info-modern">
                                <div class="contact-item">
                                    <svg class="contact-icon"><use href="#icon-support"></use></svg>
                                    <span><EMAIL></span>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-phone contact-icon"></i>
                                    <span>+7 (800) 123-45-67</span>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-clock contact-icon"></i>
                                    <span>24/7 поддержка</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <hr class="footer-divider-modern">

                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="copyright-modern">
                            &copy; <?php echo date('Y'); ?> <span class="text-brand-gold">GreenChain EcoFund</span>. Все права защищены.
                            <span class="text-brand-gold">Создаем зеленое будущее вместе!</span>
                        </p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="footer-stats-modern">
                            <div class="stat-item-modern">
                                <svg class="stat-icon-modern"><use href="#icon-referrals"></use></svg>
                                <span><span class="stat-value-modern" id="total-users">0</span> эко-инвесторов</span>
                            </div>
                            <div class="stat-item-modern">
                                <svg class="stat-icon-modern"><use href="#icon-wallet"></use></svg>
                                <span>$<span class="stat-value-modern" id="total-invested">0</span> в эко-проекты</span>
                            </div>
                            <div class="stat-item-modern">
                                <svg class="stat-icon-modern"><use href="#icon-eco-leaf"></use></svg>
                                <span><span class="stat-value-modern">98.5%</span> успешность</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Модальные окна -->
    <!-- Support Modal -->
    <div class="modal fade" id="supportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Техническая поддержка</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="supportForm">
                        <div class="mb-3">
                            <label for="supportSubject" class="form-label">Тема обращения</label>
                            <select class="form-select" id="supportSubject" required>
                                <option value="">Выберите тему</option>
                                <option value="technical">Технические проблемы</option>
                                <option value="investment">Вопросы по инвестициям</option>
                                <option value="withdrawal">Проблемы с выводом</option>
                                <option value="account">Проблемы с аккаунтом</option>
                                <option value="other">Другое</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="supportMessage" class="form-label">Сообщение</label>
                            <textarea class="form-control" id="supportMessage" rows="4" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                    <button type="button" class="btn btn-primary" onclick="submitSupport()">Отправить</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Top Button -->
    <button class="btn-back-to-top" id="backToTop">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Live Chat Widget -->
    <div class="live-chat-widget" id="liveChatWidget">
        <div class="chat-toggle" onclick="toggleChat()">
            <i class="fas fa-comments"></i>
        </div>
        <div class="chat-window" id="chatWindow" style="display: none;">
            <div class="chat-header">
                <h6>Онлайн поддержка</h6>
                <button class="btn-close-chat" onclick="toggleChat()">×</button>
            </div>
            <div class="chat-messages" id="chatMessages">
                <div class="message bot-message">
                    Здравствуйте! Как я могу вам помочь?
                </div>
            </div>
            <div class="chat-input">
                <input type="text" id="chatInput" placeholder="Введите сообщение..." onkeypress="handleChatEnter(event)">
                <button onclick="sendChatMessage()"><i class="fas fa-paper-plane"></i></button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/modern.js"></script>
    <script src="assets/js/realtime.js"></script>
    <script src="assets/js/adaptive-contrast.js"></script>
    
    <!-- Page specific scripts -->
    <?php if (isset($page_scripts)): ?>
        <?php foreach ($page_scripts as $script): ?>
            <script src="<?php echo $script; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <script>
        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', function() {
            // Скрыть preloader
            setTimeout(function() {
                document.getElementById('preloader').style.display = 'none';
            }, 1000);
            
            // Обновление статистики в футере
            updateFooterStats();
            
            // Запуск обновлений в реальном времени
            if (typeof startRealTimeUpdates === 'function') {
                startRealTimeUpdates();
            }

            // Инициализация современной навигации
            initModernNavigation();
        });

        // Современная навигация
        function initModernNavigation() {
            const mobileToggle = document.getElementById('mobileNavToggle');
            const mobileNav = document.getElementById('mobileNav');
            const header = document.getElementById('modernHeader');

            // Мобильное меню
            if (mobileToggle && mobileNav) {
                mobileToggle.addEventListener('click', function() {
                    mobileNav.classList.toggle('show');

                    // Изменение иконки
                    const icon = mobileToggle.querySelector('use');
                    if (mobileNav.classList.contains('show')) {
                        icon.setAttribute('href', '#icon-close');
                    } else {
                        icon.setAttribute('href', '#icon-menu');
                    }
                });

                // Закрытие при клике вне меню
                document.addEventListener('click', function(e) {
                    if (!mobileToggle.contains(e.target) && !mobileNav.contains(e.target)) {
                        mobileNav.classList.remove('show');
                        const icon = mobileToggle.querySelector('use');
                        icon.setAttribute('href', '#icon-menu');
                    }
                });
            }

            // Эффект при скролле
            if (header) {
                window.addEventListener('scroll', function() {
                    if (window.scrollY > 50) {
                        header.classList.add('scrolled');
                    } else {
                        header.classList.remove('scrolled');
                    }
                });
            }
        }
    </script>
</body>
</html>
