/* ===== ПОЛНОЕ ЦЕНТРИРОВАНИЕ САЙТА GREENCHAIN ECOFUND ===== */

/*
 * Этот файл обеспечивает полное центрирование всех элементов сайта
 * Сохраняет все существующие стили, цвета, анимации и функциональность
 * Применяется ко всем страницам и устройствам
 */

/* ===== ГЛОБАЛЬНЫЕ ПЕРЕМЕННЫЕ ДЛЯ ЦЕНТРИРОВАНИЯ ===== */
:root {
    --center-max-width: 1400px;
    --center-padding: 2rem;
    --center-gap: 1.5rem;
    --center-transition: all 0.3s ease-in-out;
}

/* ===== БАЗОВОЕ ЦЕНТРИРОВАНИЕ BODY И HTML ===== */
html {
    text-align: center !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    min-height: 100vh !important;
}

body {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    max-width: var(--center-max-width) !important;
    margin: 0 auto !important;
    padding: 0 var(--center-padding) !important;
}

/* ===== ОСНОВНЫЕ КОНТЕЙНЕРЫ ===== */
.container,
.container-fluid,
.container-xxl,
.container-xl,
.container-lg,
.container-md,
.container-sm {
    width: 100% !important;
    max-width: var(--center-max-width) !important;
    margin: 0 auto !important;
    padding: 0 var(--center-padding) !important;
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

/* ===== MAIN CONTENT ===== */
main,
.main-content {
    width: 100% !important;
    max-width: var(--center-max-width) !important;
    margin: 0 auto !important;
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

/* ===== СЕКЦИИ ===== */
section,
.content-section,
.content-section-modern,
.modern-hero,
.features-section,
.how-it-works-section,
.partners-section,
.cta-section {
    width: 100% !important;
    max-width: var(--center-max-width) !important;
    margin: 0 auto !important;
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 3rem var(--center-padding) !important;
}

/* ===== BOOTSTRAP СЕТКА ===== */
.row {
    width: 100% !important;
    max-width: var(--center-max-width) !important;
    margin: 0 auto !important;
    text-align: center !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    flex-wrap: wrap !important;
    gap: var(--center-gap) !important;
}

/* Все колонки Bootstrap */
[class*="col-"] {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 auto !important;
}

/* ===== ЗАГОЛОВКИ ===== */
h1, h2, h3, h4, h5, h6,
.hero-title,
.hero-title-modern,
.section-title,
.section-title-modern,
.section-title-eco,
.feature-title,
.feature-title-modern {
    text-align: center !important;
    width: 100% !important;
    margin: 0 auto 1.5rem !important;
    display: block !important;
}

/* ===== ПОДЗАГОЛОВКИ И ОПИСАНИЯ ===== */
.hero-subtitle,
.hero-subtitle-modern,
.section-subtitle,
.section-subtitle-modern,
.feature-description,
.feature-description-modern,
p {
    text-align: center !important;
    width: 100% !important;
    max-width: 800px !important;
    margin: 0 auto 1.5rem !important;
    display: block !important;
}

/* ===== КАРТОЧКИ ===== */
.feature-card-modern,
.investment-card,
.luxury-investment-card,
.feature-card,
.step-card,
.card {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 auto !important;
    width: 100% !important;
    max-width: 400px !important;
}

/* ===== СЕТКИ И ГРИД-КОНТЕЙНЕРЫ ===== */
.features-grid-modern,
.investment-grid,
.steps-grid,
.grid-container {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: var(--center-gap) !important;
    justify-content: center !important;
    align-items: center !important;
    justify-items: center !important;
    text-align: center !important;
    width: 100% !important;
    max-width: var(--center-max-width) !important;
    margin: 0 auto !important;
}

/* ===== КНОПКИ ===== */
.btn,
.btn-primary,
.btn-secondary,
.btn-hero-primary,
.btn-hero-secondary,
.nav-button {
    text-align: center !important;
    display: inline-flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 0 auto !important;
}

/* Контейнеры кнопок */
.hero-buttons,
.hero-buttons-modern,
.btn-container {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: var(--center-gap) !important;
    flex-wrap: wrap !important;
    width: 100% !important;
    margin: 2rem auto !important;
}

/* ===== СТАТИСТИКА ===== */
.hero-stats-modern,
.stats-container {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 2rem !important;
    flex-wrap: wrap !important;
    width: 100% !important;
    margin: 2rem auto !important;
}

.stat-card-modern,
.stat-card {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 120px !important;
}

/* ===== ИКОНКИ ===== */
.feature-icon-modern,
.feature-icon,
.step-icon,
.icon {
    text-align: center !important;
    margin: 0 auto 1.5rem !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

/* ===== НАВИГАЦИЯ ===== */
.modern-header,
.modern-navbar {
    width: 100% !important;
    max-width: var(--center-max-width) !important;
    margin: 0 auto !important;
    text-align: center !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.modern-navbar .container {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
}

/* ===== ФУТЕР ===== */
footer {
    width: 100% !important;
    max-width: var(--center-max-width) !important;
    margin: 0 auto !important;
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

footer .container {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

.footer-column {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

/* ===== АДАПТИВНОСТЬ ===== */
@media (max-width: 1200px) {
    :root {
        --center-max-width: 100%;
        --center-padding: 1.5rem;
    }
}

@media (max-width: 768px) {
    :root {
        --center-padding: 1rem;
        --center-gap: 1rem;
    }
    
    .hero-stats-modern {
        gap: 1rem !important;
    }
    
    .hero-buttons-modern {
        gap: 0.75rem !important;
    }
}

@media (max-width: 576px) {
    :root {
        --center-padding: 0.75rem;
        --center-gap: 0.75rem;
    }

    section {
        padding: 2rem var(--center-padding) !important;
    }
}

/* ===== ДОПОЛНИТЕЛЬНЫЕ ЭЛЕМЕНТЫ ===== */

/* Формы */
.form-group,
.form-control,
.input-group,
form {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 auto !important;
    text-align: center !important;
}

/* Таблицы */
table,
.table {
    margin: 0 auto !important;
    text-align: center !important;
}

/* Изображения */
img,
.img-fluid {
    display: block !important;
    margin: 0 auto !important;
}

/* Модальные окна */
.modal-content,
.modal-body,
.modal-header,
.modal-footer {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Алерты */
.alert {
    text-align: center !important;
    margin: 0 auto !important;
    max-width: 600px !important;
}

/* Бейджи */
.badge,
.label {
    display: inline-flex !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
}

/* Списки */
ul, ol {
    text-align: center !important;
    list-style-position: inside !important;
    margin: 0 auto !important;
    max-width: 600px !important;
}

li {
    text-align: center !important;
}

/* Панели навигации */
.nav,
.navbar-nav {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
}

/* Пагинация */
.pagination {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 0 auto !important;
}

/* Хлебные крошки */
.breadcrumb {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 0 auto !important;
}

/* Прогресс-бары */
.progress {
    margin: 0 auto !important;
    max-width: 400px !important;
}

/* Карусели */
.carousel,
.carousel-inner,
.carousel-item {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Аккордеоны */
.accordion,
.accordion-item,
.accordion-body {
    text-align: center !important;
    margin: 0 auto !important;
}

/* Вкладки */
.nav-tabs,
.nav-pills,
.tab-content,
.tab-pane {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

/* ===== ФИНАЛЬНЫЕ ПРАВИЛА ЦЕНТРИРОВАНИЯ ===== */

/* Все div элементы */
div:not(.row):not([class*="col-"]) {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Все span элементы */
span {
    text-align: center !important;
}

/* Все ссылки */
a {
    text-align: center !important;
    display: inline-flex !important;
    justify-content: center !important;
    align-items: center !important;
}

/* Принудительное центрирование для всех элементов */
* {
    text-align: center !important;
}

/* Исключения для элементов ввода */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
textarea,
select {
    text-align: left !important;
}
