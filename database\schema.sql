-- <PERSON><PERSON><PERSON>n EcoFund Database Schema
-- Создание базы данных и всех необходимых таблиц

-- Создание базы данных
CREATE DATABASE IF NOT EXISTS greenchain_ecofund CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE greenchain_ecofund;

-- Таблица пользователей
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HAR(50) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    phone VARCHAR(20),
    country VARCHAR(50),
    city VARCHAR(50),
    address TEXT,
    avatar VARCHAR(255),
    role ENUM('user', 'admin') DEFAULT 'user',
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
    balance DECIMAL(15,2) DEFAULT 0.00,
    total_invested DECIMAL(15,2) DEFAULT 0.00,
    total_profit DECIMAL(15,2) DEFAULT 0.00,
    total_withdrawn DECIMAL(15,2) DEFAULT 0.00,
    referrer_id INT,
    referral_code VARCHAR(20) UNIQUE,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(100),
    password_reset_token VARCHAR(100),
    password_reset_expires DATETIME,
    last_login DATETIME,
    last_activity DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_referrer (referrer_id),
    INDEX idx_referral_code (referral_code)
);

-- Таблица инвестиционных пакетов
CREATE TABLE investment_packages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    type ENUM('flexible', 'fixed') NOT NULL,
    daily_rate DECIMAL(5,2) NOT NULL,
    min_amount DECIMAL(15,2) NOT NULL,
    max_amount DECIMAL(15,2),
    duration_days INT,
    description TEXT,
    features JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Таблица инвестиций пользователей
CREATE TABLE user_investments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    package_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    daily_rate DECIMAL(5,2) NOT NULL,
    total_profit DECIMAL(15,2) DEFAULT 0.00,
    last_profit_date DATE,
    start_date DATE NOT NULL,
    end_date DATE,
    status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (package_id) REFERENCES investment_packages(id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_dates (start_date, end_date)
);

-- Таблица транзакций
CREATE TABLE transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    type ENUM('deposit', 'withdrawal', 'investment', 'profit', 'referral_bonus', 'task_reward') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    balance_before DECIMAL(15,2) NOT NULL,
    balance_after DECIMAL(15,2) NOT NULL,
    description TEXT,
    reference_id INT,
    reference_type VARCHAR(50),
    status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'completed',
    payment_method VARCHAR(50),
    payment_details JSON,
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Таблица рефералов
CREATE TABLE referrals (
    id INT PRIMARY KEY AUTO_INCREMENT,
    referrer_id INT NOT NULL,
    referred_id INT NOT NULL,
    level INT NOT NULL DEFAULT 1,
    bonus_rate DECIMAL(5,2) NOT NULL,
    total_earned DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_referral (referrer_id, referred_id),
    INDEX idx_referrer (referrer_id),
    INDEX idx_referred (referred_id)
);

-- Таблица заявок на пополнение баланса
CREATE TABLE payment_requests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'USDT',
    network VARCHAR(20) DEFAULT 'TRC-20',
    transaction_hash VARCHAR(100),
    wallet_address VARCHAR(100),
    status ENUM('pending', 'approved', 'rejected', 'cancelled') DEFAULT 'pending',
    admin_comment TEXT,
    admin_id INT,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_payment_user (user_id),
    INDEX idx_payment_status (status),
    INDEX idx_payment_date (created_at),
    INDEX idx_payment_hash (transaction_hash)
);

-- Таблица заявок на вывод средств
CREATE TABLE withdrawal_requests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'USDT',
    network VARCHAR(20) DEFAULT 'TRC-20',
    wallet_address VARCHAR(100) NOT NULL,
    transaction_hash VARCHAR(100),
    fee_amount DECIMAL(15,2) DEFAULT 0.00,
    final_amount DECIMAL(15,2) NOT NULL,
    status ENUM('pending', 'approved', 'rejected', 'completed', 'cancelled') DEFAULT 'pending',
    admin_comment TEXT,
    admin_id INT,
    processed_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_withdrawal_user (user_id),
    INDEX idx_withdrawal_status (status),
    INDEX idx_withdrawal_date (created_at),
    INDEX idx_withdrawal_wallet (wallet_address),
    INDEX idx_withdrawal_hash (transaction_hash)
);

-- Таблица заданий
CREATE TABLE tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    type ENUM('daily_login', 'investment', 'referral', 'profile_complete', 'custom') NOT NULL,
    target_value INT NOT NULL DEFAULT 1,
    reward_amount DECIMAL(15,2) NOT NULL,
    reward_type ENUM('balance', 'bonus_rate', 'special') DEFAULT 'balance',
    is_repeatable BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    start_date DATE,
    end_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_type (type),
    INDEX idx_active (is_active)
);

-- Таблица прогресса заданий пользователей
CREATE TABLE user_task_progress (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    task_id INT NOT NULL,
    current_value INT DEFAULT 0,
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at DATETIME,
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_task (user_id, task_id),
    INDEX idx_user_id (user_id),
    INDEX idx_completed (is_completed)
);

-- Таблица образовательного контента
CREATE TABLE educational_content (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) UNIQUE NOT NULL,
    content LONGTEXT NOT NULL,
    excerpt TEXT,
    category ENUM('articles', 'videos', 'guides', 'news') NOT NULL,
    tags JSON,
    featured_image VARCHAR(255),
    video_url VARCHAR(255),
    author_id INT,
    views_count INT DEFAULT 0,
    likes_count INT DEFAULT 0,
    is_published BOOLEAN DEFAULT FALSE,
    published_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_category (category),
    INDEX idx_published (is_published),
    INDEX idx_slug (slug)
);

-- Таблица проектов на карте
CREATE TABLE map_projects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL,
    type ENUM('eco_project', 'mining_farm', 'solar_farm', 'wind_farm') NOT NULL,
    latitude DECIMAL(10,8) NOT NULL,
    longitude DECIMAL(11,8) NOT NULL,
    country VARCHAR(50) NOT NULL,
    city VARCHAR(100),
    description TEXT,
    status ENUM('planning', 'construction', 'active', 'maintenance', 'completed') DEFAULT 'planning',
    capacity VARCHAR(100),
    investment_amount DECIMAL(15,2),
    roi_percentage DECIMAL(5,2),
    start_date DATE,
    completion_date DATE,
    images JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_location (latitude, longitude)
);

-- Таблица попыток входа (для безопасности)
CREATE TABLE login_attempts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(100) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    success BOOLEAN DEFAULT FALSE,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_ip (ip_address),
    INDEX idx_created_at (created_at)
);

-- Таблица логов активности
CREATE TABLE activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- Таблица настроек системы
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (setting_key)
);

-- Таблица уведомлений
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_read (is_read),
    INDEX idx_created_at (created_at)
);

-- Таблица тикетов поддержки
CREATE TABLE support_tickets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    subject VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    status ENUM('open', 'in_progress', 'resolved', 'closed') DEFAULT 'open',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    assigned_to INT,
    admin_response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    resolved_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority)
);

-- Таблица курсов валют
CREATE TABLE currency_rates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    currency_code VARCHAR(10) NOT NULL,
    rate_usd DECIMAL(15,8) NOT NULL,
    rate_change_24h DECIMAL(8,4),
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_currency (currency_code),
    INDEX idx_currency (currency_code)
);

-- Вставка начальных данных

-- Инвестиционные пакеты
INSERT INTO investment_packages (name, type, daily_rate, min_amount, max_amount, duration_days, description, features) VALUES
('Гибкий пакет', 'flexible', 0.50, 10.00, NULL, NULL, 'Гибкий пакет с возможностью вывода в любое время', '["Вывод прибыли в любое время", "Вывод основного вклада в любое время", "Минимальная сумма: $10", "Без ограничений по времени"]'),
('Фиксированный 30 дней', 'fixed', 1.20, 100.00, NULL, 30, 'Фиксированный пакет на 30 дней с повышенной доходностью', '["Вывод прибыли ежедневно", "Основной вклад заблокирован", "Срок: 30 дней", "Повышенная доходность"]'),
('Фиксированный 60 дней', 'fixed', 1.50, 500.00, NULL, 60, 'Фиксированный пакет на 60 дней с высокой доходностью', '["Вывод прибыли ежедневно", "Основной вклад заблокирован", "Срок: 60 дней", "Высокая доходность"]'),
('Фиксированный 90 дней', 'fixed', 1.80, 1000.00, NULL, 90, 'Фиксированный пакет на 90 дней с максимальной доходностью', '["Вывод прибыли ежедневно", "Основной вклад заблокирован", "Срок: 90 дней", "Максимальная доходность"]');

-- Задания
INSERT INTO tasks (title, description, type, target_value, reward_amount, reward_type, is_repeatable) VALUES
('Ежедневный вход', 'Заходите в систему каждый день и получайте бонус', 'daily_login', 1, 1.00, 'balance', TRUE),
('Первая инвестиция', 'Сделайте свою первую инвестицию', 'investment', 1, 10.00, 'balance', FALSE),
('Пригласите друга', 'Пригласите друга по реферальной ссылке', 'referral', 1, 25.00, 'balance', FALSE),
('Заполните профиль', 'Заполните все поля в профиле', 'profile_complete', 1, 5.00, 'balance', FALSE),
('Инвестор месяца', 'Инвестируйте $1000 за месяц', 'investment', 1000, 100.00, 'balance', FALSE);

-- Проекты на карте
INSERT INTO map_projects (name, type, latitude, longitude, country, city, description, status, capacity, investment_amount, roi_percentage) VALUES
('Солнечная ферма "Зеленая энергия"', 'solar_farm', 55.7558, 37.6176, 'Россия', 'Москва', 'Крупная солнечная электростанция мощностью 100 МВт', 'active', '100 МВт', 5000000.00, 12.5),
('Ветряная ферма "Северный ветер"', 'wind_farm', 59.9311, 30.3609, 'Россия', 'Санкт-Петербург', 'Ветряная электростанция на побережье Финского залива', 'construction', '75 МВт', 3500000.00, 15.2),
('Эко-майнинг центр "GreenMine"', 'mining_farm', 56.8431, 60.6454, 'Россия', 'Екатеринбург', 'Майнинг-ферма на возобновляемой энергии', 'active', '50 МВт', 2000000.00, 18.7),
('Проект лесовосстановления', 'eco_project', 55.0415, 82.9346, 'Россия', 'Новосибирск', 'Восстановление лесных массивов в Сибири', 'planning', '1000 га', 1500000.00, 8.3);

-- Настройки системы
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('site_name', 'GreenChain EcoFund', 'string', 'Название сайта', TRUE),
('site_description', 'Инвестиционная платформа для экологических проектов', 'string', 'Описание сайта', TRUE),
('min_withdrawal', '10', 'number', 'Минимальная сумма для вывода', TRUE),
('max_withdrawal_daily', '10000', 'number', 'Максимальная сумма вывода в день', FALSE),
('referral_levels', '3', 'number', 'Количество уровней в реферальной программе', TRUE),
('referral_rates', '[5, 3, 1]', 'json', 'Проценты по уровням рефералов', TRUE),
('maintenance_mode', 'false', 'boolean', 'Режим технического обслуживания', FALSE);

-- Создание администратора по умолчанию
INSERT INTO users (username, email, password_hash, first_name, last_name, role, balance, referral_code, email_verified) VALUES
('admin', '<EMAIL>', '$argon2id$v=19$m=65536,t=4,p=3$example_hash', 'Admin', 'User', 'admin', 1000.00, 'ADMIN001', TRUE);

-- Курсы валют (примерные)
INSERT INTO currency_rates (currency_code, rate_usd, rate_change_24h) VALUES
('BTC', 45000.00, 2.5),
('ETH', 3200.00, 1.8),
('USDT', 1.00, 0.0),
('BNB', 320.00, -0.5);

-- Создание триггеров и процедур

DELIMITER //

-- Триггер для автоматического создания реферального кода
CREATE TRIGGER before_user_insert
BEFORE INSERT ON users
FOR EACH ROW
BEGIN
    IF NEW.referral_code IS NULL THEN
        SET NEW.referral_code = CONCAT('REF', LPAD(FLOOR(RAND() * 999999), 6, '0'));
    END IF;
END//

-- Триггер для обновления статистики пользователя при создании транзакции
CREATE TRIGGER after_transaction_insert
AFTER INSERT ON transactions
FOR EACH ROW
BEGIN
    IF NEW.type = 'investment' THEN
        UPDATE users SET total_invested = total_invested + NEW.amount WHERE id = NEW.user_id;
    ELSEIF NEW.type = 'profit' OR NEW.type = 'referral_bonus' OR NEW.type = 'task_reward' THEN
        UPDATE users SET total_profit = total_profit + NEW.amount WHERE id = NEW.user_id;
    ELSEIF NEW.type = 'withdrawal' THEN
        UPDATE users SET total_withdrawn = total_withdrawn + NEW.amount WHERE id = NEW.user_id;
    END IF;
END//

-- Процедура для расчета ежедневной прибыли
DROP PROCEDURE IF EXISTS CalculateDailyProfits//
CREATE PROCEDURE CalculateDailyProfits()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE investment_id INT;
    DECLARE user_id INT;
    DECLARE amount DECIMAL(15,2);
    DECLARE daily_rate DECIMAL(5,2);
    DECLARE profit_amount DECIMAL(15,2);
    DECLARE current_balance DECIMAL(15,2);

    DECLARE cur CURSOR FOR
        SELECT ui.id, ui.user_id, ui.amount, ui.daily_rate
        FROM user_investments ui
        WHERE ui.status = 'active'
        AND (ui.last_profit_date IS NULL OR ui.last_profit_date < CURDATE())
        AND (ui.end_date IS NULL OR ui.end_date >= CURDATE());

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN cur;

    read_loop: LOOP
        FETCH cur INTO investment_id, user_id, amount, daily_rate;
        IF done THEN
            LEAVE read_loop;
        END IF;

        SET profit_amount = amount * (daily_rate / 100);

        -- Получаем текущий баланс
        SELECT balance INTO current_balance FROM users WHERE id = user_id;

        -- Обновляем баланс пользователя
        UPDATE users SET balance = balance + profit_amount WHERE id = user_id;

        -- Создаем транзакцию
        INSERT INTO transactions (user_id, type, amount, balance_before, balance_after, description, reference_id, reference_type)
        VALUES (user_id, 'profit', profit_amount, current_balance, current_balance + profit_amount,
                'Ежедневная прибыль от инвестиции', investment_id, 'investment');

        -- Обновляем инвестицию
        UPDATE user_investments
        SET total_profit = total_profit + profit_amount, last_profit_date = CURDATE()
        WHERE id = investment_id;

    END LOOP;

    CLOSE cur;
END//

-- Процедура для обработки реферальных бонусов
DROP PROCEDURE IF EXISTS ProcessReferralBonus//
CREATE PROCEDURE ProcessReferralBonus(IN referred_user_id INT, IN investment_amount DECIMAL(15,2))
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE referrer_id INT;
    DECLARE level_num INT DEFAULT 1;
    DECLARE bonus_rate DECIMAL(5,2);
    DECLARE bonus_amount DECIMAL(15,2);
    DECLARE current_balance DECIMAL(15,2);
    DECLARE max_levels INT DEFAULT 3;

    -- Получаем реферера
    SELECT referrer_id INTO referrer_id FROM users WHERE id = referred_user_id;

    WHILE referrer_id IS NOT NULL AND level_num <= max_levels DO
        -- Определяем процент бонуса для уровня
        CASE level_num
            WHEN 1 THEN SET bonus_rate = 5.0;
            WHEN 2 THEN SET bonus_rate = 3.0;
            WHEN 3 THEN SET bonus_rate = 1.0;
            ELSE SET bonus_rate = 0.0;
        END CASE;

        IF bonus_rate > 0 THEN
            SET bonus_amount = investment_amount * (bonus_rate / 100);

            -- Получаем текущий баланс реферера
            SELECT balance INTO current_balance FROM users WHERE id = referrer_id;

            -- Обновляем баланс реферера
            UPDATE users SET balance = balance + bonus_amount WHERE id = referrer_id;

            -- Создаем транзакцию
            INSERT INTO transactions (user_id, type, amount, balance_before, balance_after, description, reference_id, reference_type)
            VALUES (referrer_id, 'referral_bonus', bonus_amount, current_balance, current_balance + bonus_amount,
                    CONCAT('Реферальный бонус ', level_num, ' уровня'), referred_user_id, 'referral');

            -- Обновляем статистику реферала
            UPDATE referrals
            SET total_earned = total_earned + bonus_amount
            WHERE referrer_id = referrer_id AND referred_id = referred_user_id;
        END IF;

        -- Переходим к следующему уровню
        SELECT referrer_id INTO referrer_id FROM users WHERE id = referrer_id;
        SET level_num = level_num + 1;
    END WHILE;
END//

DELIMITER ;
