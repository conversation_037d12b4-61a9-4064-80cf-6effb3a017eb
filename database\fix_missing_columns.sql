-- Исправление недостающих колонок в базе данных GreenChain EcoFund

-- Добавляем недостающие колонки в таблицу users
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS remember_token VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS remember_token_expires TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS password_reset_token VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS password_reset_expires TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS email_verification_token VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS referral_code VARCHAR(50) NULL,
ADD COLUMN IF NOT EXISTS total_invested DECIMAL(15,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS total_profit DECIMAL(15,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS last_activity TIMESTAMP NULL;

-- Создаем индексы для производительности
CREATE INDEX IF NOT EXISTS idx_users_remember_token ON users(remember_token);
CREATE INDEX IF NOT EXISTS idx_users_password_reset_token ON users(password_reset_token);
CREATE INDEX IF NOT EXISTS idx_users_email_verification_token ON users(email_verification_token);
CREATE INDEX IF NOT EXISTS idx_users_referral_code ON users(referral_code);

-- Создаем недостающие таблицы если их нет

-- Таблица для логов активности
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL,
    action VARCHAR(100) NOT NULL,
    details TEXT NULL,
    ip_address VARCHAR(45) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Таблица для попыток входа
CREATE TABLE IF NOT EXISTS login_attempts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) NOT NULL,
    success BOOLEAN DEFAULT FALSE,
    ip_address VARCHAR(45) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Таблица настроек системы
CREATE TABLE IF NOT EXISTS settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NULL,
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Таблица категорий образования
CREATE TABLE IF NOT EXISTS education_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    icon VARCHAR(50) NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Таблица статей образования
CREATE TABLE IF NOT EXISTS education_articles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT NULL,
    featured_image VARCHAR(255) NULL,
    is_published BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    views_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES education_categories(id) ON DELETE CASCADE
);

-- Таблица достижений пользователей
CREATE TABLE IF NOT EXISTS user_achievements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    achievement_type VARCHAR(50) NOT NULL,
    achievement_name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    icon VARCHAR(50) NULL,
    points INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Таблица проектов на карте
CREATE TABLE IF NOT EXISTS map_projects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    location VARCHAR(255) NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    project_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'active',
    investment_amount DECIMAL(15,2) DEFAULT 0.00,
    roi_percentage DECIMAL(5,2) DEFAULT 0.00,
    images JSON NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Вставляем базовые настройки
INSERT IGNORE INTO settings (setting_key, setting_value, description) VALUES
('referral_rates', '[5, 3, 1]', 'Проценты реферальных бонусов по уровням'),
('site_maintenance', '0', 'Режим технического обслуживания'),
('min_withdrawal', '10', 'Минимальная сумма для вывода'),
('withdrawal_fee', '2', 'Комиссия за вывод средств (%)');

-- Вставляем базовые категории образования
INSERT IGNORE INTO education_categories (id, name, description, icon, sort_order) VALUES
(1, 'Основы инвестирования', 'Базовые знания об инвестициях', 'fas fa-chart-line', 1),
(2, 'Экологические проекты', 'Информация об эко-проектах', 'fas fa-leaf', 2),
(3, 'Управление рисками', 'Как управлять инвестиционными рисками', 'fas fa-shield-alt', 3),
(4, 'Криптовалюты', 'Цифровые валюты и блокчейн', 'fab fa-bitcoin', 4);

-- Вставляем примеры проектов на карте
INSERT IGNORE INTO map_projects (name, description, location, latitude, longitude, project_type, investment_amount, roi_percentage) VALUES
('Солнечная ферма "Зеленая энергия"', 'Крупнейшая солнечная электростанция в регионе', 'Краснодарский край, Россия', 45.0355, 38.9753, 'solar', 2500000.00, 12.5),
('Ветропарк "Северный ветер"', 'Современный ветропарк с высокой эффективностью', 'Мурманская область, Россия', 68.9585, 33.0827, 'wind', 1800000.00, 10.8),
('Гидроэлектростанция "Чистая вода"', 'Экологически чистая ГЭС малой мощности', 'Алтайский край, Россия', 52.0394, 85.0913, 'hydro', 3200000.00, 15.2);

-- Обновляем реферальные коды для существующих пользователей
UPDATE users SET referral_code = CONCAT('REF', LPAD(id, 6, '0')) WHERE referral_code IS NULL;

-- Обновляем статус email_verified для существующих пользователей
UPDATE users SET email_verified = TRUE WHERE email_verified IS NULL;

COMMIT;
