// <PERSON><PERSON><PERSON>n EcoFund - Main JavaScript

console.log('🚀 main.js начинает загрузку...');

// Глобальные переменные
let isLoggedIn = false;
let currentUser = null;
let realTimeInterval = null;

console.log('📝 Глобальные переменные инициализированы');

// Инициализация при загрузке DOM
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎬 DOM загружен, запускаем initializeApp...');
    try {
        initializeApp();
        console.log('✅ initializeApp завершена успешно');
    } catch (error) {
        console.error('❌ Ошибка в initializeApp:', error);
    }
});

// Основная инициализация приложения
function initializeApp() {
    console.log('🔧 Начинаем инициализацию приложения...');

    try {
        // Проверка авторизации
        console.log('🔍 Проверяем авторизацию...');
        checkAuthStatus();
        console.log('✅ Авторизация проверена');

        // Инициализация компонентов
        console.log('🧩 Инициализируем компоненты...');
        initializeComponents();
        console.log('✅ Компоненты инициализированы');

        // Запуск обновлений в реальном времени
        console.log('⏱️ Запускаем обновления в реальном времени...');
        startRealTimeUpdates();
        console.log('✅ Обновления запущены');

        // Инициализация событий
        console.log('🎯 Инициализируем обработчики событий...');
        initializeEventListeners();
        console.log('✅ Обработчики событий инициализированы');

        // Анимации при загрузке
        console.log('🎨 Инициализируем анимации...');
        initializeAnimations();
        console.log('✅ Анимации инициализированы');

        console.log('🎉 Инициализация приложения завершена успешно!');
    } catch (error) {
        console.error('❌ Ошибка в initializeApp:', error);
    }
}

// Проверка статуса авторизации
function checkAuthStatus() {
    // Проверяем наличие данных пользователя в сессии
    const userElement = document.getElementById('user-data') || document.querySelector('[data-user-id]');
    if (userElement) {
        isLoggedIn = true;
        currentUser = {
            id: userElement.dataset.userId,
            name: userElement.dataset.userName,
            role: userElement.dataset.userRole
        };
        console.log('User authenticated:', currentUser);
    } else {
        isLoggedIn = false;
        currentUser = null;
        console.log('User not authenticated');
    }
}

// Инициализация компонентов
function initializeComponents() {
    // Инициализация tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Инициализация popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Инициализация кнопки "Наверх"
    initializeBackToTop();
    
    // Инициализация чата
    initializeLiveChat();
    
    // Инициализация форм
    initializeForms();
}

// Инициализация событий
function initializeEventListeners() {
    console.log('🎯 Добавляем глобальный обработчик кликов...');

    // Обработка кликов по кнопкам
    document.addEventListener('click', function(e) {
        // УДАЛЕНО: Обработчик кнопок инвестирования

        // Кнопки вывода средств
        if (e.target.matches('.withdraw-btn')) {
            handleWithdrawClick(e);
        }

        // Кнопки копирования
        if (e.target.matches('.copy-btn')) {
            handleCopyClick(e);
        }

        // Кнопки быстрых сумм
        if (e.target.matches('.amount-btn')) {
            handleAmountClick(e);
        }
    });
    
    // Обработка изменений в формах
    document.addEventListener('input', function(e) {
        if (e.target.matches('.amount-input')) {
            updateCalculations(e.target);
        }
    });
    
    // Обработка отправки форм
    document.addEventListener('submit', function(e) {
        if (e.target.matches('.ajax-form')) {
            handleAjaxForm(e);
        }
    });
}

// Инициализация анимаций
function initializeAnimations() {
    // Анимация появления элементов при скролле
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Наблюдение за элементами
    document.querySelectorAll('.card, .stats-card, .package-card').forEach(el => {
        observer.observe(el);
    });
}

// Кнопка "Наверх"
function initializeBackToTop() {
    const backToTopBtn = document.getElementById('backToTop');
    if (!backToTopBtn) return;
    
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.style.display = 'flex';
        } else {
            backToTopBtn.style.display = 'none';
        }
    });
    
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Живой чат
function initializeLiveChat() {
    // Инициализация уже выполнена в HTML
}

function toggleChat() {
    const chatWindow = document.getElementById('chatWindow');
    if (chatWindow.style.display === 'none') {
        chatWindow.style.display = 'block';
        chatWindow.classList.add('animate-slide-in-right');
    } else {
        chatWindow.style.display = 'none';
        chatWindow.classList.remove('animate-slide-in-right');
    }
}

function sendChatMessage() {
    const input = document.getElementById('chatInput');
    const message = input.value.trim();
    
    if (message) {
        addChatMessage(message, 'user');
        input.value = '';
        
        // Имитация ответа бота
        setTimeout(() => {
            const botResponse = getBotResponse(message);
            addChatMessage(botResponse, 'bot');
        }, 1000);
    }
}

function addChatMessage(message, sender) {
    const messagesContainer = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;
    messageDiv.textContent = message;
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function getBotResponse(message) {
    const responses = {
        'привет': 'Здравствуйте! Как дела с инвестициями?',
        'помощь': 'Я могу помочь вам с вопросами по инвестициям, выводу средств и общей информации о платформе.',
        'баланс': 'Ваш текущий баланс отображается в правом верхнем углу.',
        'инвестиции': 'Вы можете инвестировать в гибкие или фиксированные пакеты. Перейдите в раздел "Инвестировать".',
        'вывод': 'Для вывода средств перейдите в раздел "Вывести средства" в личном кабинете.'
    };
    
    const lowerMessage = message.toLowerCase();
    for (const key in responses) {
        if (lowerMessage.includes(key)) {
            return responses[key];
        }
    }
    
    return 'Спасибо за ваш вопрос! Наш специалист свяжется с вами в ближайшее время.';
}

function handleChatEnter(event) {
    if (event.key === 'Enter') {
        sendChatMessage();
    }
}

// Инициализация форм
function initializeForms() {
    // Валидация форм в реальном времени
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
    
    // Маски для полей ввода
    initializeInputMasks();
}

// Маски для полей ввода
function initializeInputMasks() {
    // Маска для суммы
    const amountInputs = document.querySelectorAll('.amount-input');
    amountInputs.forEach(input => {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\d.]/g, '');
            if (value.split('.').length > 2) {
                value = value.substring(0, value.lastIndexOf('.'));
            }
            e.target.value = value;
        });
    });
    
    // Маска для телефона
    const phoneInputs = document.querySelectorAll('.phone-input');
    phoneInputs.forEach(input => {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 0) {
                value = '+' + value;
            }
            e.target.value = value;
        });
    });
}

// УДАЛЕНО: Старая функция handleInvestClick

// Обработка кликов по кнопкам вывода
function handleWithdrawClick(e) {
    e.preventDefault();
    
    if (!isLoggedIn) {
        showLoginModal();
        return;
    }
    
    showWithdrawModal();
}

// Обработка кликов по кнопкам копирования
function handleCopyClick(e) {
    e.preventDefault();
    const textToCopy = e.target.dataset.copy;
    
    navigator.clipboard.writeText(textToCopy).then(() => {
        showNotification('Скопировано в буфер обмена!', 'success');
    }).catch(() => {
        // Fallback для старых браузеров
        const textArea = document.createElement('textarea');
        textArea.value = textToCopy;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('Скопировано в буфер обмена!', 'success');
    });
}

// Обработка кликов по кнопкам быстрых сумм
function handleAmountClick(e) {
    e.preventDefault();
    const amount = e.target.dataset.amount;
    const targetInput = document.querySelector(e.target.dataset.target);
    
    if (targetInput) {
        targetInput.value = amount;
        updateCalculations(targetInput);
    }
}

// Обновление расчетов
function updateCalculations(input) {
    const amount = parseFloat(input.value) || 0;
    const packageType = input.dataset.packageType || 'flexible';
    const rate = packageType === 'fixed' ? 1.2 : 0.5; // Процент в день
    
    // Обновляем отображение расчетов
    const dailyProfitElement = document.getElementById('dailyProfit');
    const monthlyProfitElement = document.getElementById('monthlyProfit');
    const yearlyProfitElement = document.getElementById('yearlyProfit');
    
    if (dailyProfitElement) {
        const dailyProfit = amount * (rate / 100);
        dailyProfitElement.textContent = formatMoney(dailyProfit);
    }
    
    if (monthlyProfitElement) {
        const monthlyProfit = amount * (rate / 100) * 30;
        monthlyProfitElement.textContent = formatMoney(monthlyProfit);
    }
    
    if (yearlyProfitElement) {
        const yearlyProfit = amount * (rate / 100) * 365;
        yearlyProfitElement.textContent = formatMoney(yearlyProfit);
    }
}

// Обработка AJAX форм
function handleAjaxForm(e) {
    e.preventDefault();
    const form = e.target;
    const formData = new FormData(form);
    const url = form.action || window.location.href;
    
    // Показываем индикатор загрузки
    const submitBtn = form.querySelector('[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Загрузка...';
    submitBtn.disabled = true;
    
    fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            if (data.redirect) {
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 1500);
            }
            if (data.reload) {
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            }
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Произошла ошибка. Попробуйте позже.', 'error');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
}

// Показ уведомлений
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show notification`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideInRight 0.3s ease-out;
    `;
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Автоматическое удаление через 5 секунд
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Показ модального окна входа
function showLoginModal() {
    showNotification('Для выполнения этого действия необходимо войти в систему.', 'warning');
    setTimeout(() => {
        window.location.href = 'index.php?page=login';
    }, 2000);
}

// УДАЛЕНО: Старая функция showInvestModal

// Форматирование денежных сумм
function formatMoney(amount, currency = '$') {
    return currency + parseFloat(amount).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

// Форматирование процентов
function formatPercent(percent) {
    return parseFloat(percent).toFixed(2) + '%';
}

// Обновление статистики в футере
function updateFooterStats() {
    fetch('api/stats.php')
        .then(response => response.json())
        .then(data => {
            const totalUsersElement = document.getElementById('total-users');
            const totalInvestedElement = document.getElementById('total-invested');
            
            if (totalUsersElement && data.total_users) {
                animateNumber(totalUsersElement, data.total_users);
            }
            
            if (totalInvestedElement && data.total_invested) {
                animateNumber(totalInvestedElement, data.total_invested);
            }
        })
        .catch(error => {
            console.error('Error updating footer stats:', error);
        });
}

// Анимация чисел
function animateNumber(element, targetValue) {
    const startValue = parseInt(element.textContent) || 0;
    const duration = 2000; // 2 секунды
    const startTime = Date.now();
    
    function updateNumber() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Easing function
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        
        const currentValue = Math.floor(startValue + (targetValue - startValue) * easeOutQuart);
        element.textContent = currentValue.toLocaleString();
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    updateNumber();
}

// Отправка формы поддержки
function submitSupport() {
    const subject = document.getElementById('supportSubject').value;
    const message = document.getElementById('supportMessage').value;
    
    if (!subject || !message) {
        showNotification('Пожалуйста, заполните все поля.', 'warning');
        return;
    }
    
    const formData = new FormData();
    formData.append('subject', subject);
    formData.append('message', message);
    formData.append('action', 'support');
    
    fetch('api/support.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Ваше обращение отправлено! Мы свяжемся с вами в ближайшее время.', 'success');
            bootstrap.Modal.getInstance(document.getElementById('supportModal')).hide();
            document.getElementById('supportForm').reset();
        } else {
            showNotification(data.message || 'Произошла ошибка при отправке.', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Произошла ошибка. Попробуйте позже.', 'error');
    });
}

// Экспорт функций для глобального использования
console.log('🌐 Экспортируем функции в window...');
window.toggleChat = toggleChat;
window.sendChatMessage = sendChatMessage;
window.handleChatEnter = handleChatEnter;
window.submitSupport = submitSupport;
window.showNotification = showNotification;
window.formatMoney = formatMoney;
window.formatPercent = formatPercent;
// УДАЛЕНО: Экспорт функций инвестирования
window.checkAuthStatus = checkAuthStatus;

console.log('✅ main.js полностью загружен и готов к работе!');
console.log('📋 Экспортированные функции:', {
    checkAuthStatus: typeof window.checkAuthStatus
});
