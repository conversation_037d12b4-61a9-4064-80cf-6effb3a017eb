-- ========================================
-- КРИТИЧЕСКИЕ ИСПРАВЛЕНИЯ БД
-- GreenChain EcoFund Platform
-- ========================================

-- 1. Добавление отсутствующей колонки remember_token
-- ========================================

-- Проверяем и добавляем колонку remember_token в таблицу users
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS remember_token VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS remember_token_expires TIMESTAMP NULL;

-- Добавляем индекс для быстрого поиска по токену
CREATE INDEX IF NOT EXISTS idx_remember_token ON users(remember_token);

-- 2. Создание таблицы settings если не существует
-- ========================================

CREATE TABLE IF NOT EXISTS settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NULL,
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key)
);

-- 3. Добавление базовых настроек системы
-- ========================================

INSERT IGNORE INTO settings (setting_key, setting_value, description) VALUES
('referral_rates', '[5, 3, 1]', 'Проценты реферальных бонусов по уровням'),
('site_maintenance', '0', 'Режим технического обслуживания'),
('min_withdrawal', '10', 'Минимальная сумма для вывода'),
('withdrawal_fee', '2', 'Комиссия за вывод средств (%)'),
('max_withdrawal_daily', '10000', 'Максимальная сумма вывода в день'),
('send_profit_notifications', '1', 'Отправлять уведомления о прибыли'),
('site_name', 'GreenChain EcoFund', 'Название сайта'),
('site_description', 'Инвестиционная платформа для экологических проектов', 'Описание сайта'),
('maintenance_mode', '0', 'Режим технического обслуживания'),
('email_notifications', '1', 'Включить email уведомления'),
('registration_enabled', '1', 'Разрешить регистрацию новых пользователей'),
('kyc_required', '0', 'Требовать верификацию KYC'),
('two_factor_enabled', '0', 'Включить двухфакторную аутентификацию'),
('session_timeout', '7200', 'Время жизни сессии в секундах'),
('max_login_attempts', '5', 'Максимальное количество попыток входа'),
('lockout_duration', '900', 'Время блокировки после превышения попыток (сек)'),
('profit_calculation_time', '00:00:00', 'Время ежедневного расчета прибыли'),
('backup_enabled', '1', 'Включить автоматическое резервное копирование'),
('log_retention_days', '30', 'Количество дней хранения логов'),
('api_rate_limit', '100', 'Лимит запросов к API в минуту'),
('file_upload_max_size', '5242880', 'Максимальный размер загружаемого файла (байт)'),
('allowed_file_types', 'jpg,jpeg,png,pdf,doc,docx', 'Разрешенные типы файлов'),
('currency_symbol', '$', 'Символ валюты'),
('decimal_places', '2', 'Количество знаков после запятой'),
('timezone', 'Europe/Moscow', 'Часовой пояс системы');

-- 4. Проверка и создание других необходимых таблиц
-- ========================================

-- Таблица для попыток входа (если не существует)
CREATE TABLE IF NOT EXISTS login_attempts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NULL,
    success BOOLEAN DEFAULT FALSE,
    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_agent TEXT NULL,
    INDEX idx_email_time (email, attempt_time),
    INDEX idx_ip_time (ip_address, attempt_time)
);

-- Таблица для сессий (если не существует)
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    session_data TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_last_activity (last_activity)
);

-- Таблица для логов системы (если не существует)
CREATE TABLE IF NOT EXISTS system_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') DEFAULT 'INFO',
    message TEXT NOT NULL,
    context JSON NULL,
    user_id INT NULL,
    ip_address VARCHAR(45) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_level_time (level, created_at),
    INDEX idx_user_time (user_id, created_at)
);

-- 5. Обновление существующих данных
-- ========================================

-- Обновляем пользователей без роли
UPDATE users SET role = 'user' WHERE role IS NULL OR role = '';

-- Обновляем пользователей без статуса
UPDATE users SET status = 'active' WHERE status IS NULL OR status = '';

-- Обновляем пользователей без баланса
UPDATE users SET balance = 0.00 WHERE balance IS NULL;

-- 6. Создание индексов для оптимизации
-- ========================================

-- Индексы для таблицы users
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- Индексы для таблицы investments (если существует)
CREATE INDEX IF NOT EXISTS idx_investments_user_id ON investments(user_id);
CREATE INDEX IF NOT EXISTS idx_investments_status ON investments(status);
CREATE INDEX IF NOT EXISTS idx_investments_created_at ON investments(created_at);

-- Индексы для таблицы transactions (если существует)
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);

-- 7. Проверка целостности данных
-- ========================================

-- Удаляем дублирующиеся записи в настройках (если есть)
DELETE s1 FROM settings s1
INNER JOIN settings s2 
WHERE s1.id > s2.id 
AND s1.setting_key = s2.setting_key;

-- 8. Финальная проверка
-- ========================================

-- Показываем статистику исправлений
SELECT 
    'users' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN remember_token IS NOT NULL THEN 1 END) as with_remember_token
FROM users

UNION ALL

SELECT 
    'settings' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN setting_key LIKE '%withdrawal%' THEN 1 END) as withdrawal_settings
FROM settings;

-- Показываем все настройки
SELECT setting_key, setting_value, description 
FROM settings 
ORDER BY setting_key;

-- ========================================
-- СКРИПТ ЗАВЕРШЕН
-- ========================================
