<?php
// API для обработки обращений в поддержку
require_once '../config/config.php';
require_once '../config/session_config.php';
session_start();
require_once '../includes/functions.php';

// Установка заголовков
header('Content-Type: application/json');

// Проверка метода запроса
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Проверка CSRF токена
if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

$action = $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'support':
            echo json_encode(handleSupportRequest());
            break;
            
        case 'feedback':
            echo json_encode(handleFeedback());
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
    }
} catch (Exception $e) {
    error_log("Support API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

/**
 * Обработка обращения в поддержку
 */
function handleSupportRequest() {
    global $conn;
    
    $subject = sanitizeInput($_POST['subject'] ?? '');
    $message = sanitizeInput($_POST['message'] ?? '');
    $priority = sanitizeInput($_POST['priority'] ?? 'medium');
    
    // Валидация
    if (empty($subject) || empty($message)) {
        return ['success' => false, 'message' => 'Пожалуйста, заполните все поля'];
    }
    
    if (strlen($subject) < 5 || strlen($subject) > 200) {
        return ['success' => false, 'message' => 'Тема должна содержать от 5 до 200 символов'];
    }
    
    if (strlen($message) < 10 || strlen($message) > 2000) {
        return ['success' => false, 'message' => 'Сообщение должно содержать от 10 до 2000 символов'];
    }
    
    $valid_priorities = ['low', 'medium', 'high', 'urgent'];
    if (!in_array($priority, $valid_priorities)) {
        $priority = 'medium';
    }
    
    try {
        $user_id = isLoggedIn() ? $_SESSION['user_id'] : null;
        
        // Если пользователь не авторизован, создаем анонимное обращение
        if (!$user_id) {
            $email = sanitizeInput($_POST['email'] ?? '');
            $name = sanitizeInput($_POST['name'] ?? '');
            
            if (empty($email) || !validateEmail($email)) {
                return ['success' => false, 'message' => 'Укажите корректный email'];
            }
            
            if (empty($name) || strlen($name) < 2) {
                return ['success' => false, 'message' => 'Укажите ваше имя'];
            }
            
            // Создаем временного пользователя или используем существующего
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $existing_user = $stmt->fetch();
            
            if ($existing_user) {
                $user_id = $existing_user['id'];
            } else {
                // Создаем временную запись
                $stmt = $conn->prepare("
                    INSERT INTO users (username, email, password_hash, first_name, last_name, status) 
                    VALUES (?, ?, ?, ?, ?, 'inactive')
                ");
                $username = 'guest_' . time();
                $stmt->execute([$username, $email, '', $name, '', ]);
                $user_id = $conn->lastInsertId();
            }
        }
        
        // Проверяем лимит обращений (не более 5 в час)
        $stmt = $conn->prepare("
            SELECT COUNT(*) as ticket_count 
            FROM support_tickets 
            WHERE user_id = ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ");
        $stmt->execute([$user_id]);
        $ticket_count = $stmt->fetch()['ticket_count'];
        
        if ($ticket_count >= 5) {
            return ['success' => false, 'message' => 'Превышен лимит обращений. Попробуйте позже.'];
        }
        
        // Создаем тикет
        $stmt = $conn->prepare("
            INSERT INTO support_tickets (user_id, subject, message, priority, status) 
            VALUES (?, ?, ?, ?, 'open')
        ");
        $stmt->execute([$user_id, $subject, $message, $priority]);
        $ticket_id = $conn->lastInsertId();
        
        // Логируем действие
        logAction('support_ticket_created', "Ticket ID: $ticket_id, Subject: $subject");
        
        // Отправляем уведомление администраторам
        notifyAdminsNewTicket($ticket_id, $subject, $priority);
        
        // Отправляем подтверждение пользователю
        if (isLoggedIn()) {
            createNotification($user_id, 'Обращение принято', 
                "Ваше обращение #$ticket_id принято в обработку. Мы ответим в ближайшее время.", 'info');
        }
        
        return [
            'success' => true, 
            'message' => 'Ваше обращение принято! Номер тикета: #' . $ticket_id,
            'ticket_id' => $ticket_id
        ];
        
    } catch (Exception $e) {
        error_log("Error creating support ticket: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при создании обращения'];
    }
}

/**
 * Обработка отзыва
 */
function handleFeedback() {
    global $conn;
    
    $rating = intval($_POST['rating'] ?? 0);
    $comment = sanitizeInput($_POST['comment'] ?? '');
    $category = sanitizeInput($_POST['category'] ?? 'general');
    
    // Валидация
    if ($rating < 1 || $rating > 5) {
        return ['success' => false, 'message' => 'Укажите оценку от 1 до 5'];
    }
    
    if (strlen($comment) > 1000) {
        return ['success' => false, 'message' => 'Комментарий слишком длинный'];
    }
    
    $valid_categories = ['general', 'platform', 'support', 'investment', 'withdrawal'];
    if (!in_array($category, $valid_categories)) {
        $category = 'general';
    }
    
    try {
        $user_id = isLoggedIn() ? $_SESSION['user_id'] : null;
        
        // Создаем запись отзыва (можно создать отдельную таблицу feedback)
        $subject = "Отзыв о платформе (оценка: $rating/5)";
        $message = "Категория: $category\nОценка: $rating/5\n\nКомментарий:\n$comment";
        
        $stmt = $conn->prepare("
            INSERT INTO support_tickets (user_id, subject, message, priority, status) 
            VALUES (?, ?, ?, 'low', 'open')
        ");
        $stmt->execute([$user_id, $subject, $message]);
        
        // Логируем действие
        logAction('feedback_submitted', "Rating: $rating, Category: $category");
        
        return [
            'success' => true, 
            'message' => 'Спасибо за ваш отзыв! Он поможет нам стать лучше.'
        ];
        
    } catch (Exception $e) {
        error_log("Error submitting feedback: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при отправке отзыва'];
    }
}

/**
 * Уведомление администраторов о новом тикете
 */
function notifyAdminsNewTicket($ticket_id, $subject, $priority) {
    global $conn;
    
    try {
        // Получаем всех администраторов
        $stmt = $conn->query("SELECT id FROM users WHERE role = 'admin' AND status = 'active'");
        $admins = $stmt->fetchAll();
        
        foreach ($admins as $admin) {
            createNotification(
                $admin['id'], 
                'Новое обращение в поддержку', 
                "Тикет #$ticket_id: $subject (Приоритет: $priority)",
                'info',
                "index.php?page=admin&section=support&ticket=$ticket_id"
            );
        }
        
    } catch (Exception $e) {
        error_log("Error notifying admins: " . $e->getMessage());
    }
}

/**
 * Создание уведомления
 */
function createNotification($user_id, $title, $message, $type = 'info', $action_url = null) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO notifications (user_id, title, message, type, action_url) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$user_id, $title, $message, $type, $action_url]);
        
    } catch (Exception $e) {
        error_log("Error creating notification: " . $e->getMessage());
    }
}
?>
