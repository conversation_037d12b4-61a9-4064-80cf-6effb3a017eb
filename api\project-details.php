<?php
// API для получения деталей проекта
require_once '../config/config.php';
require_once '../includes/functions.php';

// Установка заголовков
header('Content-Type: application/json');

$project_id = intval($_GET['id'] ?? 0);

if (!$project_id) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid project ID']);
    exit;
}

try {
    $project = getProjectDetails($project_id);
    
    if (!$project) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Project not found']);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'project' => $project
    ]);
    
} catch (Exception $e) {
    error_log("Project details API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

/**
 * Получение деталей проекта
 */
function getProjectDetails($project_id) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT * FROM map_projects 
        WHERE id = ? AND is_active = 1
    ");
    $stmt->execute([$project_id]);
    $project = $stmt->fetch();
    
    if (!$project) {
        return null;
    }
    
    // Форматируем данные
    $project['investment_amount'] = floatval($project['investment_amount']);
    $project['roi_percentage'] = floatval($project['roi_percentage']);
    $project['latitude'] = floatval($project['latitude']);
    $project['longitude'] = floatval($project['longitude']);
    
    // Декодируем JSON поля
    if ($project['images']) {
        $project['images'] = json_decode($project['images'], true);
    }
    
    return $project;
}
?>
