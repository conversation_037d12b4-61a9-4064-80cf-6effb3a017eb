<?php
// Тестирование структуры базы данных
require_once 'config/config.php';

echo "<h2>Проверка структуры базы данных</h2>\n";

try {
    // Проверка подключения к БД
    echo "<h3>Статус подключения к базе данных:</h3>\n";
    if (isset($conn) && $conn instanceof PDO) {
        echo "✅ Подключение к базе данных успешно установлено<br>\n";
        echo "📊 База данных: " . DB_NAME . "<br>\n";
        echo "🖥️ Хост: " . DB_HOST . "<br>\n";
    } else {
        echo "❌ Ошибка подключения к базе данных<br>\n";
        exit;
    }

    // Проверка таблицы payment_requests
    echo "<h3>Таблица payment_requests:</h3>\n";
    try {
        $stmt = $conn->query("DESCRIBE payment_requests");
        if ($stmt) {
            echo "✅ Таблица payment_requests найдена<br>\n";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
            echo "<tr style='background-color: #f0f0f0;'><th>Поле</th><th>Тип</th><th>Null</th><th>Ключ</th><th>По умолчанию</th></tr>\n";
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                echo "<tr>";
                echo "<td>{$row['Field']}</td>";
                echo "<td>{$row['Type']}</td>";
                echo "<td>{$row['Null']}</td>";
                echo "<td>{$row['Key']}</td>";
                echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        }
    } catch (PDOException $e) {
        echo "❌ Таблица payment_requests не найдена: " . $e->getMessage() . "<br>\n";
    }

    // Проверка таблицы withdrawal_requests
    echo "<h3>Таблица withdrawal_requests:</h3>\n";
    try {
        $stmt = $conn->query("DESCRIBE withdrawal_requests");
        if ($stmt) {
            echo "✅ Таблица withdrawal_requests найдена<br>\n";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
            echo "<tr style='background-color: #f0f0f0;'><th>Поле</th><th>Тип</th><th>Null</th><th>Ключ</th><th>По умолчанию</th></tr>\n";
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                echo "<tr>";
                echo "<td>{$row['Field']}</td>";
                echo "<td>{$row['Type']}</td>";
                echo "<td>{$row['Null']}</td>";
                echo "<td>{$row['Key']}</td>";
                echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        }
    } catch (PDOException $e) {
        echo "❌ Таблица withdrawal_requests не найдена: " . $e->getMessage() . "<br>\n";
    }

    // Проверка настроек платежной системы
    echo "<h3>Настройки платежной системы:</h3>\n";

    // Сначала проверим, существует ли таблица settings
    try {
        $stmt = $conn->query("SHOW TABLES LIKE 'settings'");
        $settings_table_exists = $stmt->rowCount() > 0;

        if ($settings_table_exists) {
            // Проверим структуру таблицы settings
            $stmt = $conn->query("DESCRIBE settings");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

            $value_column = null;
            if (in_array('value', $columns)) {
                $value_column = 'value';
            } elseif (in_array('setting_value', $columns)) {
                $value_column = 'setting_value';
            }

            if ($value_column) {
                $settings = [
                    'payment_system_enabled',
                    'payment_min_deposit',
                    'payment_max_deposit',
                    'payment_min_withdrawal',
                    'payment_max_withdrawal',
                    'payment_withdrawal_fee',
                    'payment_daily_withdrawal_limit',
                    'payment_usdt_wallet'
                ];

                echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
                echo "<tr style='background-color: #f0f0f0;'><th>Настройка</th><th>Значение</th><th>Статус</th></tr>\n";

                foreach ($settings as $setting) {
                    try {
                        $stmt = $conn->prepare("SELECT $value_column FROM settings WHERE name = ?");
                        $stmt->execute([$setting]);
                        $result = $stmt->fetch();
                        $value = $result ? $result[$value_column] : 'Не установлено';
                        $status = $result ? '✅' : '⚠️';

                        echo "<tr>";
                        echo "<td>$setting</td>";
                        echo "<td>$value</td>";
                        echo "<td>$status</td>";
                        echo "</tr>\n";
                    } catch (PDOException $e) {
                        echo "<tr>";
                        echo "<td>$setting</td>";
                        echo "<td>Ошибка: " . $e->getMessage() . "</td>";
                        echo "<td>❌</td>";
                        echo "</tr>\n";
                    }
                }
                echo "</table>\n";
            } else {
                echo "<p>⚠️ Таблица settings найдена, но структура неизвестна. Колонки: " . implode(', ', $columns) . "</p>\n";
            }
        } else {
            echo "<p>⚠️ Таблица settings не найдена в базе данных</p>\n";
        }
    } catch (PDOException $e) {
        echo "<p>❌ Ошибка проверки таблицы settings: " . $e->getMessage() . "</p>\n";
    }

    // Статистика заявок
    echo "<h3>Статистика заявок:</h3>\n";
    
    // Заявки на пополнение
    try {
        $stmt = $conn->query("SELECT status, COUNT(*) as count FROM payment_requests GROUP BY status");
        echo "<h4>📈 Заявки на пополнение:</h4>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 50%; margin: 10px 0;'>\n";
        echo "<tr style='background-color: #e8f5e8;'><th>Статус</th><th>Количество</th></tr>\n";
        $total_deposits = 0;
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr><td>{$row['status']}</td><td>{$row['count']}</td></tr>\n";
            $total_deposits += $row['count'];
        }
        echo "<tr style='background-color: #d4edda; font-weight: bold;'><td>ВСЕГО</td><td>$total_deposits</td></tr>\n";
        echo "</table>\n";
    } catch (PDOException $e) {
        echo "<p>⚠️ Нет данных о заявках на пополнение: " . $e->getMessage() . "</p>\n";
    }

    // Заявки на вывод
    try {
        $stmt = $conn->query("SELECT status, COUNT(*) as count FROM withdrawal_requests GROUP BY status");
        echo "<h4>📉 Заявки на вывод:</h4>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 50%; margin: 10px 0;'>\n";
        echo "<tr style='background-color: #fff3cd;'><th>Статус</th><th>Количество</th></tr>\n";
        $total_withdrawals = 0;
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr><td>{$row['status']}</td><td>{$row['count']}</td></tr>\n";
            $total_withdrawals += $row['count'];
        }
        echo "<tr style='background-color: #ffeaa7; font-weight: bold;'><td>ВСЕГО</td><td>$total_withdrawals</td></tr>\n";
        echo "</table>\n";
    } catch (PDOException $e) {
        echo "<p>⚠️ Нет данных о заявках на вывод: " . $e->getMessage() . "</p>\n";
    }

    // Общая статистика
    echo "<h3>📊 Общая статистика системы:</h3>\n";
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<p><strong>✅ Система готова к работе</strong></p>\n";
    echo "<p>📅 Дата проверки: " . date('Y-m-d H:i:s') . "</p>\n";
    echo "<p>🔗 База данных: " . DB_NAME . " на " . DB_HOST . "</p>\n";
    echo "</div>\n";

} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "❌ <strong>Критическая ошибка:</strong> " . $e->getMessage() . "\n";
    echo "</div>\n";
}
?>
