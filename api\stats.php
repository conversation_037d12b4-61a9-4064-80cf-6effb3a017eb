<?php
// API для получения общей статистики
require_once '../config/config.php';

// Установка заголовков
header('Content-Type: application/json');
header('Cache-Control: public, max-age=300'); // Кеш на 5 минут

try {
    $stats = getPublicStats();
    echo json_encode($stats);
} catch (Exception $e) {
    error_log("Stats API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

/**
 * Получение публичной статистики
 */
function getPublicStats() {
    global $conn;
    
    // Кеширование результата
    $cache_file = '../cache/public_stats.json';
    $cache_time = 300; // 5 минут
    
    if (file_exists($cache_file) && (time() - filemtime($cache_file)) < $cache_time) {
        return json_decode(file_get_contents($cache_file), true);
    }
    
    try {
        // Общее количество пользователей
        $stmt = $conn->query("SELECT COUNT(*) as total_users FROM users WHERE status = 'active'");
        $total_users = $stmt->fetch()['total_users'];
        
        // Общая сумма инвестиций
        $stmt = $conn->query("SELECT SUM(total_invested) as total_invested FROM users");
        $total_invested = $stmt->fetch()['total_invested'] ?? 0;
        
        // Общая выплаченная прибыль
        $stmt = $conn->query("SELECT SUM(total_profit) as total_profit FROM users");
        $total_profit = $stmt->fetch()['total_profit'] ?? 0;
        
        // Количество активных инвестиций
        $stmt = $conn->query("SELECT COUNT(*) as active_investments FROM user_investments WHERE status = 'active'");
        $active_investments = $stmt->fetch()['active_investments'];
        
        // Количество проектов
        $stmt = $conn->query("SELECT COUNT(*) as total_projects FROM map_projects WHERE is_active = 1");
        $total_projects = $stmt->fetch()['total_projects'];
        
        // Статистика по странам
        $stmt = $conn->query("
            SELECT country, COUNT(*) as user_count, SUM(total_invested) as country_invested
            FROM users 
            WHERE status = 'active' AND country IS NOT NULL
            GROUP BY country 
            ORDER BY country_invested DESC 
            LIMIT 10
        ");
        $country_stats = $stmt->fetchAll();
        
        // Статистика по типам проектов
        $stmt = $conn->query("
            SELECT 
                type,
                COUNT(*) as project_count,
                SUM(investment_amount) as total_investment,
                AVG(roi_percentage) as avg_roi
            FROM map_projects 
            WHERE is_active = 1
            GROUP BY type
        ");
        $project_type_stats = $stmt->fetchAll();
        
        // Недавние инвестиции (анонимно)
        $stmt = $conn->query("
            SELECT 
                ui.amount,
                ui.created_at,
                ip.name as package_name,
                u.country
            FROM user_investments ui
            JOIN investment_packages ip ON ui.package_id = ip.id
            JOIN users u ON ui.user_id = u.id
            WHERE ui.status = 'active'
            ORDER BY ui.created_at DESC
            LIMIT 10
        ");
        $recent_investments = $stmt->fetchAll();
        
        // Форматируем данные
        foreach ($country_stats as &$country) {
            $country['country_invested'] = floatval($country['country_invested']);
        }
        
        foreach ($project_type_stats as &$project) {
            $project['total_investment'] = floatval($project['total_investment']);
            $project['avg_roi'] = floatval($project['avg_roi']);
        }
        
        foreach ($recent_investments as &$investment) {
            $investment['amount'] = floatval($investment['amount']);
        }
        
        $result = [
            'success' => true,
            'total_users' => intval($total_users),
            'total_invested' => floatval($total_invested),
            'total_profit' => floatval($total_profit),
            'active_investments' => intval($active_investments),
            'total_projects' => intval($total_projects),
            'country_stats' => $country_stats,
            'project_type_stats' => $project_type_stats,
            'recent_investments' => $recent_investments,
            'last_updated' => date('Y-m-d H:i:s')
        ];
        
        // Сохраняем в кеш
        if (!is_dir('../cache')) {
            mkdir('../cache', 0755, true);
        }
        file_put_contents($cache_file, json_encode($result));
        
        return $result;
        
    } catch (Exception $e) {
        error_log("Error getting public stats: " . $e->getMessage());
        return ['success' => false, 'message' => 'Error fetching statistics'];
    }
}
?>
