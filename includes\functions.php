<?php
// Основные функции для GreenChain EcoFund

/**
 * Проверка авторизации пользователя
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Проверка прав администратора
 */
function isAdmin() {
    return isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * Получение данных текущего пользователя
 */
function getCurrentUser() {
    global $conn;
    if (!isLoggedIn()) return null;
    
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    return $stmt->fetch();
}

/**
 * Безопасное хеширование пароля
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_ARGON2ID);
}

/**
 * Проверка пароля
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Генерация случайного токена
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * Валидация email
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Валидация пароля
 */
function validatePassword($password) {
    return strlen($password) >= PASSWORD_MIN_LENGTH && 
           preg_match('/[A-Z]/', $password) && 
           preg_match('/[a-z]/', $password) && 
           preg_match('/[0-9]/', $password);
}

/**
 * Очистка входных данных
 */
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

/**
 * Форматирование суммы
 */
function formatMoney($amount, $currency = '$') {
    return $currency . number_format($amount, 2, '.', ',');
}

/**
 * Форматирование процентов
 */
function formatPercent($percent) {
    return number_format($percent, 2) . '%';
}

/**
 * Логирование действий
 */
function logAction($action, $details = '') {
    global $conn;
    $user_id = isLoggedIn() ? $_SESSION['user_id'] : null;
    $ip_address = $_SERVER['REMOTE_ADDR'];
    
    $stmt = $conn->prepare("INSERT INTO activity_logs (user_id, action, details, ip_address, created_at) VALUES (?, ?, ?, ?, NOW())");
    $stmt->execute([$user_id, $action, $details, $ip_address]);
}

/**
 * Отправка email
 */
function sendEmail($to, $subject, $message, $isHTML = true) {
    // Здесь будет реализация отправки email через PHPMailer
    // Пока заглушка
    return true;
}

/**
 * Генерация CSRF токена
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = generateToken();
    }
    return $_SESSION['csrf_token'];
}

/**
 * Проверка CSRF токена
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Редирект с сообщением
 */
function redirect($url, $message = '', $type = 'info') {
    if ($message) {
        $_SESSION['flash_message'] = $message;
        $_SESSION['flash_type'] = $type;
    }

    // Проверяем, можно ли отправить заголовки
    if (!headers_sent()) {
        header("Location: $url");
        exit;
    } else {
        // Если заголовки уже отправлены, используем JavaScript редирект
        echo "<script>window.location.href = '$url';</script>";
        echo "<noscript><meta http-equiv='refresh' content='0;url=$url'></noscript>";
        exit;
    }
}

/**
 * Получение flash сообщения
 */
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

/**
 * Расчет дневной прибыли
 */
function calculateDailyProfit($amount, $package_type) {
    $rate = ($package_type === 'fixed') ? DAILY_PROFIT_FIXED : DAILY_PROFIT_FLEXIBLE;
    return $amount * ($rate / 100);
}

/**
 * Получение баланса пользователя
 */
function getUserBalance($user_id) {
    global $conn;
    try {
        $stmt = $conn->prepare("SELECT balance FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $result = $stmt->fetch();
        return $result ? floatval($result['balance']) : 0.00;
    } catch (Exception $e) {
        error_log("Error getting user balance: " . $e->getMessage());
        return 0.00;
    }
}

/**
 * Обновление баланса пользователя
 */
function updateUserBalance($user_id, $amount, $operation = 'add') {
    global $conn;
    $operator = ($operation === 'add') ? '+' : '-';
    $stmt = $conn->prepare("UPDATE users SET balance = balance $operator ? WHERE id = ?");
    return $stmt->execute([$amount, $user_id]);
}

/**
 * Проверка лимитов попыток входа
 */
function checkLoginAttempts($email) {
    global $conn;
    $stmt = $conn->prepare("SELECT COUNT(*) as attempts FROM login_attempts WHERE email = ? AND created_at > DATE_SUB(NOW(), INTERVAL ? SECOND)");
    $stmt->execute([$email, LOGIN_LOCKOUT_TIME]);
    $result = $stmt->fetch();
    return $result['attempts'] < MAX_LOGIN_ATTEMPTS;
}

/**
 * Запись попытки входа
 */
function recordLoginAttempt($email, $success = false) {
    global $conn;
    $stmt = $conn->prepare("INSERT INTO login_attempts (email, success, ip_address, created_at) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$email, $success, $_SERVER['REMOTE_ADDR']]);
}

/**
 * Получение настройки из базы данных
 */
function getSetting($key, $default = null) {
    global $conn;
    try {
        $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();
        return $result ? $result['setting_value'] : $default;
    } catch (Exception $e) {
        error_log("Error getting setting $key: " . $e->getMessage());
        return $default;
    }
}

/**
 * Установка настройки в базе данных
 */
function setSetting($key, $value, $description = null) {
    global $conn;
    try {
        $stmt = $conn->prepare("
            INSERT INTO settings (setting_key, setting_value, description)
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE
            setting_value = VALUES(setting_value),
            description = COALESCE(VALUES(description), description),
            updated_at = CURRENT_TIMESTAMP
        ");
        $stmt->execute([$key, $value, $description]);
        return true;
    } catch (Exception $e) {
        error_log("Error setting $key: " . $e->getMessage());
        return false;
    }
}

/**
 * Получение цвета статуса для проектов
 */
function getStatusColor($status) {
    $colors = [
        'active' => 'success',
        'completed' => 'primary',
        'pending' => 'warning',
        'cancelled' => 'danger',
        'paused' => 'secondary',
        'planning' => 'info',
        'construction' => 'warning',
        'maintenance' => 'secondary'
    ];
    return $colors[$status] ?? 'secondary';
}

/**
 * Получение названия статуса для проектов
 */
function getStatusName($status) {
    $names = [
        'active' => 'Активный',
        'completed' => 'Завершен',
        'pending' => 'Ожидание',
        'cancelled' => 'Отменен',
        'paused' => 'Приостановлен',
        'planning' => 'Планирование',
        'construction' => 'Строительство',
        'maintenance' => 'Обслуживание'
    ];
    return $names[$status] ?? 'Неизвестно';
}

/**
 * Получение проектов для карты
 */
function getMapProjects() {
    global $conn;
    try {
        $stmt = $conn->query("
            SELECT * FROM map_projects
            WHERE is_active = 1
            ORDER BY created_at DESC
        ");
        return $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("Error getting map projects: " . $e->getMessage());
        return [];
    }
}

/**
 * Получение статистики проектов
 */
function getProjectStats() {
    global $conn;
    try {
        $stmt = $conn->query("
            SELECT
                COUNT(*) as total_projects,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_projects,
                COALESCE(SUM(investment_amount), 0) as total_investment,
                COUNT(DISTINCT location) as countries_count
            FROM map_projects
            WHERE is_active = 1
        ");
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error getting project stats: " . $e->getMessage());
        return [
            'total_projects' => 0,
            'active_projects' => 0,
            'total_investment' => 0,
            'countries_count' => 0
        ];
    }
}

/**
 * Получение названия типа проекта
 */
function getProjectTypeName($type) {
    $names = [
        'solar_farm' => 'Солнечная ферма',
        'wind_farm' => 'Ветропарк',
        'mining_farm' => 'Майнинг ферма',
        'eco_project' => 'Эко-проект',
        'hydro' => 'Гидроэлектростанция'
    ];
    return $names[$type] ?? 'Неизвестный тип';
}

/**
 * Получение иконки для типа проекта
 */
function getProjectIcon($type) {
    $icons = [
        'solar_farm' => 'fas fa-sun',
        'wind_farm' => 'fas fa-wind',
        'mining_farm' => 'fas fa-microchip',
        'eco_project' => 'fas fa-leaf',
        'hydro' => 'fas fa-water'
    ];
    return $icons[$type] ?? 'fas fa-leaf';
}

/**
 * Получение категорий образования
 */
function getEducationCategories() {
    global $conn;
    try {
        $stmt = $conn->query("
            SELECT
                ec.*,
                COUNT(ea.id) as articles_count
            FROM education_categories ec
            LEFT JOIN education_articles ea ON ec.id = ea.category_id AND ea.is_published = 1
            WHERE ec.is_active = 1
            GROUP BY ec.id
            ORDER BY ec.sort_order, ec.name
        ");
        return $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("Error getting education categories: " . $e->getMessage());
        return [];
    }
}

/**
 * Получение рекомендуемых статей
 */
function getFeaturedArticles() {
    global $conn;
    try {
        $stmt = $conn->query("
            SELECT
                ea.*,
                ec.name as category_name,
                CEIL(CHAR_LENGTH(ea.content) / 1000) as reading_time
            FROM education_articles ea
            JOIN education_categories ec ON ea.category_id = ec.id
            WHERE ea.is_published = 1 AND ea.is_featured = 1
            ORDER BY ea.created_at DESC
            LIMIT 3
        ");
        return $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("Error getting featured articles: " . $e->getMessage());
        return [];
    }
}

/**
 * Получение последних статей
 */
function getLatestArticles() {
    global $conn;
    try {
        $stmt = $conn->query("
            SELECT
                ea.*,
                ec.name as category_name,
                CEIL(CHAR_LENGTH(ea.content) / 1000) as reading_time
            FROM education_articles ea
            JOIN education_categories ec ON ea.category_id = ec.id
            WHERE ea.is_published = 1
            ORDER BY ea.created_at DESC
            LIMIT 10
        ");
        return $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("Error getting latest articles: " . $e->getMessage());
        return [];
    }
}

/**
 * Получение статистики образования
 */
function getEducationStats() {
    global $conn;
    try {
        $stmt = $conn->query("
            SELECT
                (SELECT COUNT(*) FROM education_articles WHERE is_published = 1) as total_articles,
                (SELECT COUNT(*) FROM education_categories WHERE is_active = 1) as total_categories,
                (SELECT COALESCE(SUM(views_count), 0) FROM education_articles WHERE is_published = 1) as total_views
        ");
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error getting education stats: " . $e->getMessage());
        return [
            'total_articles' => 0,
            'total_categories' => 0,
            'total_views' => 0
        ];
    }
}

/**
 * Получение иконки категории
 */
function getCategoryIcon($slug) {
    $icons = [
        'basics' => 'fas fa-book',
        'investing' => 'fas fa-chart-line',
        'ecology' => 'fas fa-leaf',
        'technology' => 'fas fa-microchip',
        'energy' => 'fas fa-bolt',
        'mining' => 'fas fa-pickaxe'
    ];
    return $icons[$slug] ?? 'fas fa-folder';
}

/**
 * Получение названия способа оплаты
 */
function getPaymentMethodName($method) {
    $methods = [
        'bank_card' => 'Банковская карта',
        'bank_transfer' => 'Банковский перевод',
        'paypal' => 'PayPal',
        'crypto_btc' => 'Bitcoin',
        'crypto_eth' => 'Ethereum',
        'crypto_usdt' => 'USDT'
    ];
    return $methods[$method] ?? 'Неизвестный способ';
}

// УДАЛЕНО: Старая функция getInvestmentPackages

/**
 * Поиск статей по запросу
 */
function searchEducationArticles($query) {
    global $conn;
    try {
        $search_term = '%' . $query . '%';
        $stmt = $conn->prepare("
            SELECT
                ea.*,
                ec.name as category_name,
                CEIL(CHAR_LENGTH(ea.content) / 1000) as reading_time
            FROM education_articles ea
            JOIN education_categories ec ON ea.category_id = ec.id
            WHERE ea.is_published = 1
            AND (ea.title LIKE ? OR ea.content LIKE ? OR ea.tags LIKE ?)
            ORDER BY ea.created_at DESC
            LIMIT 20
        ");
        $stmt->execute([$search_term, $search_term, $search_term]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("Error searching articles: " . $e->getMessage());
        return [];
    }
}

/**
 * Получение доступного для вывода баланса
 */
function getAvailableBalance($user_id) {
    global $conn;
    try {
        $stmt = $conn->prepare("SELECT balance FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $result = $stmt->fetch();
        return $result ? $result['balance'] : 0;
    } catch (Exception $e) {
        error_log("Error getting available balance: " . $e->getMessage());
        return 0;
    }
}

/**
 * Получение истории выводов
 */
function getWithdrawalHistory($user_id) {
    global $conn;
    try {
        $stmt = $conn->prepare("
            SELECT * FROM transactions
            WHERE user_id = ? AND type = 'withdrawal'
            ORDER BY created_at DESC
            LIMIT 20
        ");
        $stmt->execute([$user_id]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("Error getting withdrawal history: " . $e->getMessage());
        return [];
    }
}

/**
 * Получение статистики выводов
 */
function getWithdrawalStats($user_id) {
    global $conn;
    try {
        $stmt = $conn->prepare("
            SELECT
                COALESCE(SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END), 0) as total_withdrawn,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
                MAX(CASE WHEN status = 'completed' THEN DATE(created_at) END) as last_withdrawal
            FROM transactions
            WHERE user_id = ? AND type = 'withdrawal'
        ");
        $stmt->execute([$user_id]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error getting withdrawal stats: " . $e->getMessage());
        return [
            'total_withdrawn' => 0,
            'pending_count' => 0,
            'last_withdrawal' => null
        ];
    }
}



/**
 * Получение курса криптовалют
 */
function getCryptoRates() {
    $cache_file = 'cache/crypto_rates.json';
    $cache_time = 300; // 5 минут

    if (file_exists($cache_file) && (time() - filemtime($cache_file)) < $cache_time) {
        return json_decode(file_get_contents($cache_file), true);
    }

    $data = file_get_contents(CRYPTO_API_URL);
    if ($data) {
        file_put_contents($cache_file, $data);
        return json_decode($data, true);
    }

    return null;
}

/**
 * Получение инвестиционных пакетов с фильтрацией
 */
function getInvestmentPackagesFiltered($category = '', $risk = '', $type = '', $sort_by = 'sort_order', $sort_order = 'ASC') {
    global $conn;
    try {
        $where_conditions = ['is_active = 1'];
        $params = [];

        if (!empty($category)) {
            $where_conditions[] = 'category = ?';
            $params[] = $category;
        }

        if (!empty($risk)) {
            $where_conditions[] = 'risk_level = ?';
            $params[] = $risk;
        }

        if (!empty($type)) {
            $where_conditions[] = 'type = ?';
            $params[] = $type;
        }

        $where_clause = implode(' AND ', $where_conditions);

        // Валидация полей сортировки
        $allowed_sort_fields = ['sort_order', 'daily_rate', 'min_amount', 'popularity_score', 'total_invested', 'name'];
        if (!in_array($sort_by, $allowed_sort_fields)) {
            $sort_by = 'sort_order';
        }

        $sort_order = strtoupper($sort_order) === 'DESC' ? 'DESC' : 'ASC';

        $sql = "
            SELECT
                *,
                COALESCE(total_investors, 0) as total_investors,
                COALESCE(total_invested, 0) as total_invested,
                COALESCE(popularity_score, 0) as popularity_score
            FROM investment_packages
            WHERE $where_clause
            ORDER BY $sort_by $sort_order, sort_order ASC
        ";

        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("Error getting filtered investment packages: " . $e->getMessage());
        return [];
    }
}

/**
 * Получение всех инвестиционных пакетов (для обратной совместимости)
 */
function getInvestmentPackages() {
    return getInvestmentPackagesFiltered();
}

/**
 * Получение категорий пакетов
 */
function getPackageCategories() {
    global $conn;
    try {
        $stmt = $conn->query("
            SELECT * FROM package_categories
            WHERE is_active = 1
            ORDER BY sort_order ASC
        ");
        return $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("Error getting package categories: " . $e->getMessage());
        return [];
    }
}

/**
 * Получение пакета по ID
 */
function getInvestmentPackageById($package_id) {
    global $conn;
    try {
        $stmt = $conn->prepare("
            SELECT * FROM investment_packages
            WHERE id = ? AND is_active = 1
        ");
        $stmt->execute([$package_id]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error getting investment package by ID: " . $e->getMessage());
        return null;
    }
}

/**
 * Обработка инвестирования
 */
function processInvestment($data, $user_id) {
    global $conn;

    try {
        $conn->beginTransaction();

        $package_id = intval($data['package_id']);
        $amount = floatval($data['amount']);

        // Получение данных пакета
        $package = getInvestmentPackageById($package_id);
        if (!$package) {
            $conn->rollBack();
            return ['success' => false, 'message' => 'Пакет не найден'];
        }

        // Проверка лимитов
        if ($amount < $package['min_amount']) {
            $conn->rollBack();
            return ['success' => false, 'message' => "Минимальная сумма инвестиции: " . formatMoney($package['min_amount'])];
        }

        if ($package['max_amount'] && $amount > $package['max_amount']) {
            $conn->rollBack();
            return ['success' => false, 'message' => "Максимальная сумма инвестиции: " . formatMoney($package['max_amount'])];
        }

        // Проверка баланса
        $user_balance = getUserBalance($user_id);
        if ($user_balance < $amount) {
            $conn->rollBack();
            return ['success' => false, 'message' => 'Недостаточно средств на балансе'];
        }

        // Проверка лимитированных слотов
        if ($package['is_limited'] && $package['used_slots'] >= $package['limited_slots']) {
            $conn->rollBack();
            return ['success' => false, 'message' => 'Все места в этом пакете заняты'];
        }

        // Создание инвестиции
        $end_date = $package['duration_days'] ? date('Y-m-d', strtotime("+{$package['duration_days']} days")) : null;

        $stmt = $conn->prepare("
            INSERT INTO user_investments (
                user_id, package_id, amount, daily_rate, start_date, end_date, status
            ) VALUES (?, ?, ?, ?, CURDATE(), ?, 'active')
        ");
        $stmt->execute([$user_id, $package_id, $amount, $package['daily_rate'], $end_date]);
        $investment_id = $conn->lastInsertId();

        // Списание с баланса
        $stmt = $conn->prepare("UPDATE users SET balance = balance - ? WHERE id = ?");
        $stmt->execute([$amount, $user_id]);

        // Обновление статистики пакета
        $stmt = $conn->prepare("
            UPDATE investment_packages
            SET total_invested = total_invested + ?,
                total_investors = total_investors + 1,
                used_slots = used_slots + 1,
                popularity_score = popularity_score + 1
            WHERE id = ?
        ");
        $stmt->execute([$amount, $package_id]);

        // Обновление статистики пользователя
        $stmt = $conn->prepare("UPDATE users SET total_invested = total_invested + ? WHERE id = ?");
        $stmt->execute([$amount, $user_id]);

        // Создание транзакции
        $stmt = $conn->prepare("
            INSERT INTO transactions (
                user_id, type, amount, balance_before, balance_after,
                description, reference_id, reference_type
            ) VALUES (?, 'investment', ?, ?, ?, ?, ?, 'investment')
        ");
        $stmt->execute([
            $user_id, $amount, $user_balance, $user_balance - $amount,
            "Инвестиция в пакет: {$package['name']}", $investment_id
        ]);

        // Логирование
        logAction('investment_created', "Package: {$package['name']}, Amount: $amount");

        $conn->commit();

        return [
            'success' => true,
            'message' => "Инвестиция в размере " . formatMoney($amount) . " успешно создана!",
            'investment_id' => $investment_id
        ];

    } catch (Exception $e) {
        $conn->rollBack();
        error_log("Investment processing error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при создании инвестиции'];
    }
}
?>
