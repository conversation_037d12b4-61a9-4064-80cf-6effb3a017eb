<?php
$page_title = "Обучение";

// Получение параметров поиска
$search_query = $_GET['search'] ?? '';
$category_filter = $_GET['category'] ?? '';

// Получение образовательных материалов
$categories = getEducationCategories();
$featured_articles = getFeaturedArticles();
$latest_articles = getLatestArticles();

// Если есть поиск, получаем результаты поиска
$search_results = [];
if (!empty($search_query)) {
    $search_results = searchEducationArticles($search_query);
}
?>

<div class="container">
    <!-- Заголовок страницы -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="welcome-header animate-fadeInUp">
                <h2 class="welcome-title">
                    <i class="fas fa-graduation-cap"></i> Образовательный центр
                </h2>
                <p class="welcome-subtitle">Изучайте основы инвестирования и экологических технологий</p>
            </div>
        </div>
    </div>

    <?php if (!empty($search_query)): ?>
    <!-- Результаты поиска -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-search"></i> Результаты поиска по запросу: <strong>"<?php echo htmlspecialchars($search_query); ?>"</strong>
                (найдено: <?php echo count($search_results); ?> статей)
            </div>
        </div>

        <?php if (!empty($search_results)): ?>
            <?php foreach ($search_results as $article): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="article-card">
                        <div class="article-image">
                            <img src="<?php echo $article['image'] ?: 'assets/images/default-article.jpg'; ?>"
                                 alt="<?php echo htmlspecialchars($article['title']); ?>">
                            <div class="article-category">
                                <span class="badge bg-primary"><?php echo htmlspecialchars($article['category_name']); ?></span>
                            </div>
                        </div>

                        <div class="article-content">
                            <h5 class="article-title">
                                <a href="index.php?page=article&id=<?php echo $article['id']; ?>">
                                    <?php echo htmlspecialchars($article['title']); ?>
                                </a>
                            </h5>

                            <p class="article-excerpt">
                                <?php echo htmlspecialchars(substr($article['content'], 0, 150)) . '...'; ?>
                            </p>

                            <div class="article-meta">
                                <span class="reading-time">
                                    <i class="fas fa-clock"></i> <?php echo $article['reading_time']; ?> мин
                                </span>
                                <span class="article-date">
                                    <i class="fas fa-calendar"></i> <?php echo date('d.m.Y', strtotime($article['created_at'])); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5>Ничего не найдено</h5>
                    <p class="text-muted">Попробуйте изменить поисковый запрос</p>
                </div>
            </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- Рекомендуемые статьи -->
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="section-title">
                <i class="fas fa-star text-warning"></i> Рекомендуемые статьи
            </h4>
        </div>
        
        <?php foreach ($featured_articles as $article): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="article-card featured">
                    <div class="article-image">
                        <img src="<?php echo $article['image'] ?: 'assets/images/default-article.jpg'; ?>" 
                             alt="<?php echo htmlspecialchars($article['title']); ?>">
                        <div class="article-category">
                            <span class="badge bg-primary"><?php echo htmlspecialchars($article['category_name']); ?></span>
                        </div>
                    </div>
                    
                    <div class="article-content">
                        <h5 class="article-title">
                            <a href="index.php?page=article&id=<?php echo $article['id']; ?>">
                                <?php echo htmlspecialchars($article['title']); ?>
                            </a>
                        </h5>
                        
                        <p class="article-excerpt">
                            <?php echo htmlspecialchars(substr($article['content'], 0, 150)) . '...'; ?>
                        </p>
                        
                        <div class="article-meta">
                            <div class="article-stats">
                                <span class="reading-time">
                                    <i class="fas fa-clock"></i> <?php echo $article['reading_time']; ?> мин
                                </span>
                                <span class="article-views">
                                    <i class="fas fa-eye"></i> <?php echo $article['views']; ?>
                                </span>
                            </div>
                            <div class="article-date">
                                <?php echo date('d.m.Y', strtotime($article['created_at'])); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    
    <!-- Категории -->
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="section-title">
                <i class="fas fa-folder-open text-info"></i> Категории обучения
            </h4>
        </div>
        
        <?php foreach ($categories as $category): ?>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="category-card">
                    <div class="category-icon">
                        <i class="<?php echo getCategoryIcon($category['slug']); ?>"></i>
                    </div>
                    
                    <div class="category-content">
                        <h5 class="category-title">
                            <a href="index.php?page=education&category=<?php echo $category['slug']; ?>">
                                <?php echo htmlspecialchars($category['name']); ?>
                            </a>
                        </h5>
                        
                        <p class="category-description">
                            <?php echo htmlspecialchars($category['description']); ?>
                        </p>
                        
                        <div class="category-stats">
                            <span class="articles-count">
                                <i class="fas fa-file-alt"></i> <?php echo $category['articles_count']; ?> статей
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    
    <!-- Последние статьи -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-newspaper"></i> Последние статьи
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($latest_articles)): ?>
                        <div class="empty-state">
                            <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
                            <h5>Статей пока нет</h5>
                            <p class="text-muted">Образовательные материалы скоро появятся!</p>
                        </div>
                    <?php else: ?>
                        <div class="articles-list">
                            <?php foreach ($latest_articles as $article): ?>
                                <div class="article-item">
                                    <div class="article-thumbnail">
                                        <img src="<?php echo $article['image'] ?: 'assets/images/default-article.jpg'; ?>" 
                                             alt="<?php echo htmlspecialchars($article['title']); ?>">
                                    </div>
                                    
                                    <div class="article-info">
                                        <div class="article-category-small">
                                            <span class="badge bg-outline-primary"><?php echo htmlspecialchars($article['category_name']); ?></span>
                                        </div>
                                        
                                        <h6 class="article-title-small">
                                            <a href="index.php?page=article&id=<?php echo $article['id']; ?>">
                                                <?php echo htmlspecialchars($article['title']); ?>
                                            </a>
                                        </h6>
                                        
                                        <p class="article-excerpt-small">
                                            <?php echo htmlspecialchars(substr($article['content'], 0, 100)) . '...'; ?>
                                        </p>
                                        
                                        <div class="article-meta-small">
                                            <span class="reading-time">
                                                <i class="fas fa-clock"></i> <?php echo $article['reading_time']; ?> мин
                                            </span>
                                            <span class="article-date">
                                                <?php echo date('d.m.Y', strtotime($article['created_at'])); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="index.php?page=articles" class="btn btn-outline-primary">
                                <i class="fas fa-list"></i> Все статьи
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Боковая панель -->
        <div class="col-lg-4">
            <!-- Популярные темы -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-fire text-danger"></i> Популярные темы
                    </h5>
                </div>
                <div class="card-body">
                    <div class="popular-topics">
                        <a href="index.php?page=education&search=зеленая+энергетика" class="topic-tag">Зеленая энергетика</a>
                        <a href="index.php?page=education&search=солнечные+панели" class="topic-tag">Солнечные панели</a>
                        <a href="index.php?page=education&search=ветряная+энергия" class="topic-tag">Ветряная энергия</a>
                        <a href="index.php?page=education&search=экомайнинг" class="topic-tag">Экомайнинг</a>
                        <a href="index.php?page=education&search=инвестиции" class="topic-tag">Инвестиции</a>
                        <a href="index.php?page=education&search=roi" class="topic-tag">ROI</a>
                        <a href="index.php?page=education&search=пассивный+доход" class="topic-tag">Пассивный доход</a>
                        <a href="index.php?page=education&search=экология" class="topic-tag">Экология</a>
                        <a href="index.php?page=education&search=устойчивое+развитие" class="topic-tag">Устойчивое развитие</a>
                        <a href="index.php?page=education&search=блокчейн" class="topic-tag">Блокчейн</a>
                    </div>
                </div>
            </div>
            
            <!-- Быстрые ссылки -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-link text-primary"></i> Полезные ссылки
                    </h5>
                </div>
                <div class="card-body">
                    <div class="quick-links">
                        <a href="index.php?page=calculator" class="quick-link">
                            <i class="fas fa-calculator"></i>
                            <span>Калькулятор доходности</span>
                        </a>
                        <a href="index.php?page=invest" class="quick-link">
                            <i class="fas fa-chart-line"></i>
                            <span>Инвестиционные пакеты</span>
                        </a>
                        <a href="index.php?page=map" class="quick-link">
                            <i class="fas fa-globe"></i>
                            <span>Карта проектов</span>
                        </a>
                        <a href="index.php?page=referrals" class="quick-link">
                            <i class="fas fa-users"></i>
                            <span>Реферальная программа</span>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Статистика обучения -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar text-success"></i> Статистика
                    </h5>
                </div>
                <div class="card-body">
                    <?php
                    $education_stats = getEducationStats();
                    ?>
                    <div class="education-stats">
                        <div class="stat-item">
                            <div class="stat-value"><?php echo $education_stats['total_articles']; ?></div>
                            <div class="stat-label">Статей</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value"><?php echo $education_stats['total_categories']; ?></div>
                            <div class="stat-label">Категорий</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value"><?php echo $education_stats['total_views']; ?></div>
                            <div class="stat-label">Просмотров</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.section-title {
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.article-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.article-card.featured {
    border: 2px solid var(--warning-color);
}

.article-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.article-card:hover .article-image img {
    transform: scale(1.05);
}

.article-category {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.article-content {
    padding: 1.5rem;
}

.article-title a {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 600;
}

.article-title a:hover {
    color: var(--primary-color);
}

.article-excerpt {
    color: #6c757d;
    margin: 1rem 0;
    line-height: 1.6;
}

.article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: #6c757d;
}

.article-stats {
    display: flex;
    gap: 1rem;
}

.category-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.category-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 1.5rem;
}

.category-title a {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 600;
}

.category-title a:hover {
    color: var(--primary-color);
}

.category-description {
    color: #6c757d;
    margin: 1rem 0;
    line-height: 1.6;
}

.category-stats {
    font-size: 0.9rem;
    color: #6c757d;
}

.article-item {
    display: flex;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.article-item:last-child {
    border-bottom: none;
}

.article-thumbnail {
    width: 80px;
    height: 80px;
    border-radius: 0.5rem;
    overflow: hidden;
    margin-right: 1rem;
    flex-shrink: 0;
}

.article-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.article-info {
    flex: 1;
}

.article-category-small {
    margin-bottom: 0.5rem;
}

.article-title-small a {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
}

.article-title-small a:hover {
    color: var(--primary-color);
}

.article-excerpt-small {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0.5rem 0;
    line-height: 1.5;
}

.article-meta-small {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #6c757d;
}

.popular-topics {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.topic-tag {
    background: #f8f9fa;
    color: var(--dark-color);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.topic-tag:hover {
    background: var(--primary-color);
    color: white;
}

.quick-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.quick-link {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
    text-decoration: none;
    color: var(--dark-color);
    transition: all 0.3s ease;
}

.quick-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

.quick-link i {
    margin-right: 0.75rem;
    width: 20px;
}

.education-stats {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1rem;
    text-align: center;
}

.stat-item {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

@media (max-width: 768px) {
    .article-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .article-stats {
        gap: 0.5rem;
    }
    
    .article-meta-small {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .education-stats {
        grid-template-columns: 1fr;
    }
}
</style>


