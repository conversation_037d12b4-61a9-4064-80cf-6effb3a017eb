/* ===== СОВРЕМЕННАЯ НАВИГАЦИЯ GREENCHAIN ECOFUND ===== */

/* ===== ОСНОВНОЙ КОНТЕЙНЕР НАВИГАЦИИ ===== */

.modern-header {
    background: linear-gradient(135deg, 
        rgba(15, 36, 25, 0.98) 0%, 
        rgba(26, 61, 46, 0.95) 50%, 
        rgba(45, 90, 61, 0.92) 100%) !important;
    backdrop-filter: blur(25px) !important;
    border-bottom: 2px solid rgba(255, 255, 255, 0.15) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1000 !important;
    transition: all 0.3s ease !important;
}

.modern-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.05) 50%, 
        transparent 100%);
    pointer-events: none;
}

.modern-navbar {
    padding: 1.25rem 0 !important;
    position: relative;
    z-index: 2;
}

/* ===== ЛОГОТИП ===== */

.modern-brand {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    padding: 0.5rem 1rem !important;
    border-radius: 8px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.modern-brand:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.brand-logo-modern {
    font-size: 2.25rem !important;
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.4)) !important;
    animation: logoFloat 3s ease-in-out infinite !important;
}

.brand-text-modern {
    font-size: 1.5rem !important;
    font-weight: 800 !important;
    color: #ffffff !important;
    text-shadow: 0 0 25px rgba(255, 255, 255, 0.4) !important;
    letter-spacing: 0.5px !important;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-3px) rotate(2deg); }
}

/* ===== НАВИГАЦИОННЫЕ КНОПКИ ===== */

.nav-button {
    display: inline-flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    padding: 0.875rem 1.5rem !important;
    background: rgba(255, 255, 255, 0.08) !important;
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
    border-radius: 8px !important;
    color: #ffffff !important;
    text-decoration: none !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important;
    letter-spacing: 0.3px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    backdrop-filter: blur(15px) !important;
    min-height: 48px !important;
    justify-content: center !important;
    position: relative !important;
    overflow: hidden !important;
}

.nav-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s ease;
}

.nav-button:hover::before {
    left: 100%;
}

.nav-button:hover {
    background: rgba(255, 255, 255, 0.15) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
    color: #ffffff !important;
    text-decoration: none !important;
}

.nav-button:active {
    transform: translateY(-1px) !important;
}

/* ===== СПЕЦИАЛЬНЫЕ ТИПЫ КНОПОК ===== */

/* Кнопка рефералов - зеленая */
.nav-button.referrals {
    background: linear-gradient(135deg, #16844a 0%, #059669 100%) !important;
    border-color: #16844a !important;
    box-shadow: 0 0 20px rgba(22, 132, 74, 0.4) !important;
    color: #ffffff !important;
}

.nav-button.referrals:hover {
    background: linear-gradient(135deg, #22c55e 0%, #16844a 100%) !important;
    box-shadow: 0 0 30px rgba(22, 132, 74, 0.6) !important;
    border-color: #22c55e !important;
}

/* Кнопка настроек - синяя */
.nav-button.settings {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4) !important;
}

.nav-button.settings:hover {
    background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%) !important;
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6) !important;
    border-color: #60a5fa !important;
}

/* Кнопка выхода - красная */
.nav-button.logout {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
    border-color: #ef4444 !important;
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.4) !important;
}

.nav-button.logout:hover {
    background: linear-gradient(135deg, #f87171 0%, #ef4444 100%) !important;
    box-shadow: 0 0 30px rgba(239, 68, 68, 0.6) !important;
    border-color: #f87171 !important;
}

/* Кнопка с эффектом свечения */
.nav-button.glow {
    animation: buttonGlow 2s ease-in-out infinite alternate !important;
}

@keyframes buttonGlow {
    0% { box-shadow: 0 0 20px rgba(22, 132, 74, 0.4); }
    100% { box-shadow: 0 0 35px rgba(22, 132, 74, 0.7); }
}

/* ===== ИНФОРМАЦИЯ О ПОЛЬЗОВАТЕЛЕ ===== */

.user-info {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
    padding: 0.75rem 1.25rem !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important;
    backdrop-filter: blur(15px) !important;
    transition: all 0.3s ease !important;
}

.user-info:hover {
    background: rgba(255, 255, 255, 0.15) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-2px) !important;
}

.user-avatar {
    width: 44px !important;
    height: 44px !important;
    border-radius: 50% !important;
    background: linear-gradient(135deg, #16844a 0%, #059669 100%) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: #ffffff !important;
    font-weight: 700 !important;
    font-size: 1.1rem !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 0 15px rgba(22, 132, 74, 0.3) !important;
}

.user-details {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.25rem !important;
}

.user-name {
    color: #ffffff !important;
    font-weight: 600 !important;
    font-size: 0.95rem !important;
    line-height: 1.2 !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
}

.user-balance {
    color: #22c55e !important;
    font-weight: 600 !important;
    font-size: 0.85rem !important;
    line-height: 1.2 !important;
    text-shadow: 0 0 10px rgba(34, 197, 94, 0.5) !important;
}

/* ===== ГРУППА НАВИГАЦИИ ===== */

.auth-nav {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
}

/* ===== МОБИЛЬНАЯ НАВИГАЦИЯ ===== */

.mobile-nav-toggle {
    display: none !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important;
    padding: 0.875rem !important;
    color: #ffffff !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(15px) !important;
}

.mobile-nav-toggle:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
    transform: translateY(-2px) !important;
}

.mobile-nav {
    display: none !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    background: linear-gradient(135deg, 
        rgba(15, 36, 25, 0.98) 0%, 
        rgba(26, 61, 46, 0.95) 100%) !important;
    backdrop-filter: blur(25px) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-bottom: 2px solid rgba(255, 255, 255, 0.15) !important;
    padding: 1.5rem !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
}

.mobile-nav.show {
    display: block !important;
    animation: slideDown 0.3s ease-out !important;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mobile-nav-buttons {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
}

/* ===== АДАПТИВНОСТЬ ===== */

@media (max-width: 992px) {
    .auth-nav {
        display: none !important;
    }
    
    .mobile-nav-toggle {
        display: block !important;
    }
    
    .user-info {
        padding: 0.5rem 0.75rem !important;
        gap: 0.75rem !important;
    }
    
    .user-avatar {
        width: 36px !important;
        height: 36px !important;
        font-size: 0.95rem !important;
    }
    
    .user-details {
        display: none !important;
    }
}

@media (max-width: 768px) {
    .modern-navbar {
        padding: 1rem 0 !important;
    }
    
    .brand-text-modern {
        font-size: 1.25rem !important;
    }
    
    .nav-button {
        padding: 0.75rem 1.25rem !important;
        font-size: 0.85rem !important;
        min-height: 44px !important;
    }
    
    .modern-brand {
        padding: 0.375rem 0.75rem !important;
    }
}

@media (max-width: 576px) {
    .brand-text-modern {
        font-size: 1.1rem !important;
    }
    
    .brand-logo-modern {
        font-size: 1.75rem !important;
    }
    
    .nav-button {
        padding: 0.625rem 1rem !important;
        font-size: 0.8rem !important;
        min-height: 40px !important;
    }
}
