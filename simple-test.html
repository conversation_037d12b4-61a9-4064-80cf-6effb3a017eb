<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Простой тест кнопок</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .console-output {
            background: #000;
            color: #0f0;
            font-family: monospace;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            border-radius: 5px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>🧪 Простой тест кнопок инвестирования</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Тестовые кнопки</h5>
                    </div>
                    <div class="card-body">
                        <!-- Симуляция авторизованного пользователя -->
                        <div id="user-data" style="display: none;" 
                             data-user-id="123" 
                             data-user-name="Test User" 
                             data-user-role="user">
                        </div>
                        
                        <button class="btn btn-primary invest-btn mb-2" data-package-type="flexible">
                            🌱 Гибкий пакет
                        </button><br>
                        
                        <button class="btn btn-success invest-btn mb-2" data-package-id="1" data-package-type="fixed">
                            🏢 Фиксированный пакет
                        </button><br>
                        
                        <button class="btn btn-warning invest-btn mb-2">
                            ❓ Без данных
                        </button><br>
                        
                        <button onclick="testFunction()" class="btn btn-info">
                            🔧 Тест функции
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Консоль</h5>
                    </div>
                    <div class="card-body">
                        <div id="console-output" class="console-output"></div>
                        <button onclick="clearOutput()" class="btn btn-sm btn-secondary mt-2">Очистить</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Подключаем тестовую версию main.js -->
    <script src="test-main.js"></script>
    
    <script>
        // Перехват console.log
        const output = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        
        function addToOutput(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#f00' : '#0f0';
            output.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            output.scrollTop = output.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToOutput(args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToOutput(args.join(' '), 'error');
        };
        
        function clearOutput() {
            output.innerHTML = '';
        }
        
        function testFunction() {
            console.log('🧪 Ручной тест функции...');
            if (typeof window.handleInvestClick === 'function') {
                const fakeEvent = {
                    preventDefault: () => {},
                    target: {
                        dataset: {
                            packageId: '999',
                            packageType: 'test'
                        }
                    }
                };
                window.handleInvestClick(fakeEvent);
            } else {
                console.error('❌ handleInvestClick не найдена');
            }
        }
        
        // Проверка после загрузки
        window.addEventListener('load', function() {
            console.log('🔍 Проверка после полной загрузки страницы...');
            console.log('handleInvestClick:', typeof window.handleInvestClick);
            console.log('showInvestModal:', typeof window.showInvestModal);
            console.log('isLoggedIn:', window.isLoggedIn);
        });
    </script>
</body>
</html>
