# GreenChain EcoFund - Инвестиционная платформа

Современная веб-платформа для инвестиций в экологические проекты с полным функционалом управления портфелем, реферальной системой и образовательным контентом.

## 🌟 Основные возможности

### Для пользователей
- **Регистрация и аутентификация** с подтверждением email
- **Личный кабинет** с полной статистикой и управлением
- **Инвестиционные пакеты** (фиксированные и гибкие)
- **Автоматическое начисление прибыли** ежедневно
- **Калькулятор доходности** с прогнозами
- **Интерактивная карта проектов** по всему миру
- **Реферальная система** с многоуровневыми бонусами
- **Система заданий и достижений**
- **Образовательный центр** с материалами
- **Лидерборд** топ-инвесторов
- **Система уведомлений** и поддержки
- **PWA поддержка** для мобильных устройств

### Для администраторов
- **Полная административная панель**
- **Управление пользователями** и их балансами
- **Управление инвестиционными пакетами**
- **Мониторинг транзакций** и выводов
- **Управление контентом** и проектами
- **Аналитика и отчеты**
- **Система поддержки** с тикетами

## 🛠 Технологии

- **Backend**: PHP 8.0+, MySQL 8.0+
- **Frontend**: HTML5, CSS3, JavaScript ES6+, Bootstrap 5
- **Библиотеки**: Chart.js, Leaflet, Font Awesome
- **PWA**: Service Worker, Web App Manifest
- **Безопасность**: CSRF защита, хеширование паролей, SQL injection защита

## 📋 Требования

- PHP 8.0 или выше
- MySQL 8.0 или выше
- Apache/Nginx веб-сервер
- SSL сертификат (рекомендуется)
- Composer (для зависимостей)

## 🚀 Установка

### 1. Клонирование репозитория
```bash
git clone https://github.com/your-username/greenchain-ecofund.git
cd greenchain-ecofund
```

### 2. Настройка базы данных
```sql
-- Создайте базу данных
CREATE DATABASE greenchain_ecofund CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Импортируйте структуру
mysql -u username -p greenchain_ecofund < database/schema.sql

-- Импортируйте начальные данные
mysql -u username -p greenchain_ecofund < database/initial_data.sql
```

### 3. Настройка конфигурации
```php
// Скопируйте и отредактируйте config/config.php
cp config/config.example.php config/config.php

// Настройте параметры подключения к БД
define('DB_HOST', 'localhost');
define('DB_NAME', 'greenchain_ecofund');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');

// Настройте другие параметры
define('SITE_URL', 'https://your-domain.com');
define('ADMIN_EMAIL', '<EMAIL>');
```

### 4. Настройка веб-сервера

#### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Безопасность
<Files "config/*">
    Require all denied
</Files>
```

#### Nginx
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}

location ~ /config/ {
    deny all;
}
```

### 5. Настройка прав доступа
```bash
chmod 755 uploads/
chmod 755 uploads/avatars/
chmod 644 config/config.php
```

### 6. Настройка cron для автоматических задач
```bash
# Добавьте в crontab
# Начисление ежедневной прибыли (каждый день в 00:01)
1 0 * * * /usr/bin/php /path/to/your/site/cron/daily_profit.php

# Обновление статистики (каждый час)
0 * * * * /usr/bin/php /path/to/your/site/cron/update_stats.php

# Отправка email уведомлений (каждые 5 минут)
*/5 * * * * /usr/bin/php /path/to/your/site/cron/send_emails.php
```

## 🔧 Конфигурация

### Основные настройки
```php
// config/config.php

// Настройки сайта
define('SITE_NAME', 'GreenChain EcoFund');
define('SITE_DESCRIPTION', 'Инвестиционная платформа для экологических проектов');

// Настройки email
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');

// Настройки безопасности
define('JWT_SECRET', 'your-secret-key');
define('ENCRYPTION_KEY', 'your-encryption-key');

// Настройки платежей (при необходимости)
define('PAYMENT_GATEWAY_API_KEY', 'your-api-key');
```

### Настройки инвестиционных пакетов
Пакеты настраиваются через административную панель или напрямую в базе данных:

```sql
INSERT INTO investment_packages (name, type, daily_rate, min_amount, max_amount, duration_days) VALUES
('Стартовый', 'fixed', 1.5, 10, 1000, 30),
('Продвинутый', 'fixed', 2.0, 1000, 10000, 60),
('Профессиональный', 'flexible', 2.5, 10000, NULL, NULL);
```

## 📱 PWA (Progressive Web App)

Платформа поддерживает PWA функциональность:

- **Офлайн работа** с кэшированием
- **Push уведомления**
- **Установка на домашний экран**
- **Адаптивный дизайн**

### Настройка push уведомлений
```javascript
// Получение разрешения на уведомления
if ('Notification' in window && 'serviceWorker' in navigator) {
    Notification.requestPermission();
}
```

## 🔒 Безопасность

### Реализованные меры безопасности:
- CSRF токены для всех форм
- Хеширование паролей с солью
- SQL injection защита через prepared statements
- XSS защита через htmlspecialchars
- Rate limiting для API запросов
- Валидация и санитизация всех входных данных

### Рекомендации:
- Используйте HTTPS в продакшене
- Регулярно обновляйте зависимости
- Настройте файрвол
- Делайте регулярные бэкапы БД

## 📊 Мониторинг и аналитика

### Логирование
Все важные действия логируются в таблицу `activity_logs`:
- Авторизация пользователей
- Финансовые операции
- Административные действия

### Метрики производительности
- Мониторинг FPS на клиенте
- Отслеживание использования памяти
- Анализ скорости загрузки

## 🧪 Тестирование

### Ручное тестирование
1. Регистрация нового пользователя
2. Подтверждение email
3. Создание инвестиции
4. Проверка начисления прибыли
5. Тестирование реферальной системы
6. Проверка административных функций

### Автоматизированное тестирование
```bash
# Запуск тестов (если настроены)
php vendor/bin/phpunit tests/
```

## 🚀 Развертывание

### Продакшен чеклист:
- [ ] Настроен SSL сертификат
- [ ] Обновлены все пароли и ключи
- [ ] Настроен мониторинг
- [ ] Настроены бэкапы
- [ ] Проведено нагрузочное тестирование
- [ ] Настроена система логирования
- [ ] Проверена безопасность

## 📞 Поддержка

### Контакты:
- Email: <EMAIL>
- Telegram: @greenchain_support
- Документация: [docs.greenchain.eco](https://docs.greenchain.eco)

### Часто задаваемые вопросы:
1. **Как начать инвестировать?** - Зарегистрируйтесь, пополните баланс и выберите подходящий пакет
2. **Когда начисляется прибыль?** - Ежедневно в 00:01 UTC
3. **Как работает реферальная программа?** - Получайте 5% с инвестиций прямых рефералов

## 📄 Лицензия

Этот проект распространяется под лицензией MIT. См. файл [LICENSE](LICENSE) для подробностей.

## 🤝 Вклад в проект

Мы приветствуем вклад в развитие проекта! Пожалуйста:
1. Форкните репозиторий
2. Создайте ветку для новой функции
3. Внесите изменения
4. Создайте Pull Request

## 📈 Roadmap

### Планируемые функции:
- [ ] Мобильное приложение (React Native)
- [ ] Интеграция с блокчейном
- [ ] Расширенная аналитика
- [ ] Социальные функции
- [ ] API для третьих сторон

---

**GreenChain EcoFund** - инвестируйте в будущее планеты! 🌱
