<?php
$page_title = "Задания и достижения";

// Проверка авторизации
if (!isLoggedIn()) {
    redirect('index.php?page=login&redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

$user = getCurrentUser();
$user_tasks = getUserTasks($user['id']);
$achievements = getUserAchievements($user['id']);
$task_stats = getTaskStats($user['id']);

// Обработка выполнения задания
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['complete_task'])) {
    $result = completeTask($_POST['task_id'], $user['id']);
    if ($result['success']) {
        redirect('index.php?page=tasks', $result['message'], 'success');
    } else {
        $error_message = $result['message'];
    }
}
?>

<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="page-header mb-4">
                <h2 class="page-title">
                    <i class="fas fa-tasks text-primary"></i> Задания и достижения
                </h2>
                <p class="page-subtitle">Выполняйте задания и получайте награды</p>
            </div>
        </div>
    </div>

    <!-- Статистика заданий -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-primary">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo $task_stats['completed_tasks']; ?></div>
                    <div class="stats-label">Выполнено заданий</div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-success">
                    <i class="fas fa-gift"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo formatMoney($task_stats['total_rewards']); ?></div>
                    <div class="stats-label">Получено наград</div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-warning">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo $task_stats['available_tasks']; ?></div>
                    <div class="stats-label">Доступно заданий</div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-info">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo count($achievements); ?></div>
                    <div class="stats-label">Достижений</div>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Активные задания -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list-check"></i> Активные задания
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($user_tasks['active'])): ?>
                        <div class="empty-state">
                            <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                            <h5>Нет активных заданий</h5>
                            <p class="text-muted">Все доступные задания выполнены!</p>
                        </div>
                    <?php else: ?>
                        <div class="tasks-list">
                            <?php foreach ($user_tasks['active'] as $task): ?>
                                <div class="task-card">
                                    <div class="task-icon">
                                        <i class="<?php echo getTaskIcon($task['type']); ?>"></i>
                                    </div>

                                    <div class="task-content">
                                        <div class="task-header">
                                            <h6 class="task-title"><?php echo htmlspecialchars($task['title']); ?></h6>
                                            <div class="task-reward">
                                                <span class="reward-amount">+<?php echo formatMoney($task['reward_amount']); ?></span>
                                                <span class="reward-type"><?php echo getRewardTypeName($task['reward_type']); ?></span>
                                            </div>
                                        </div>

                                        <p class="task-description"><?php echo htmlspecialchars($task['description']); ?></p>

                                        <div class="task-progress">
                                            <div class="progress-info">
                                                <span>Прогресс: <?php echo $task['current_value']; ?> / <?php echo $task['target_value']; ?></span>
                                                <span><?php echo round(($task['current_value'] / $task['target_value']) * 100, 1); ?>%</span>
                                            </div>
                                            <div class="progress">
                                                <div class="progress-bar" style="width: <?php echo min(100, ($task['current_value'] / $task['target_value']) * 100); ?>%"></div>
                                            </div>
                                        </div>

                                        <?php if ($task['current_value'] >= $task['target_value']): ?>
                                            <form method="POST" class="task-action">
                                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                <input type="hidden" name="task_id" value="<?php echo $task['id']; ?>">
                                                <button type="submit" name="complete_task" class="btn btn-success">
                                                    <i class="fas fa-check"></i> Получить награду
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <div class="task-hint">
                                                <i class="fas fa-lightbulb"></i>
                                                <?php echo getTaskHint($task['type']); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Выполненные задания -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-check-double"></i> Выполненные задания
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($user_tasks['completed'])): ?>
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-clipboard-check fa-2x mb-2"></i>
                            <p>Выполненных заданий пока нет</p>
                        </div>
                    <?php else: ?>
                        <div class="completed-tasks">
                            <?php foreach ($user_tasks['completed'] as $task): ?>
                                <div class="completed-task">
                                    <div class="task-info">
                                        <div class="task-name">
                                            <i class="<?php echo getTaskIcon($task['type']); ?>"></i>
                                            <?php echo htmlspecialchars($task['title']); ?>
                                        </div>
                                        <div class="completion-date">
                                            Выполнено: <?php echo date('d.m.Y H:i', strtotime($task['completed_at'])); ?>
                                        </div>
                                    </div>
                                    <div class="task-reward-received">
                                        <span class="reward-badge">+<?php echo formatMoney($task['reward_amount']); ?></span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Достижения -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-trophy"></i> Достижения
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($achievements)): ?>
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-medal fa-2x mb-2"></i>
                            <p>Достижений пока нет</p>
                        </div>
                    <?php else: ?>
                        <div class="achievements-list">
                            <?php foreach ($achievements as $achievement): ?>
                                <div class="achievement-item">
                                    <div class="achievement-icon">
                                        <i class="<?php echo getAchievementIcon($achievement['type']); ?>"></i>
                                    </div>
                                    <div class="achievement-info">
                                        <div class="achievement-name"><?php echo htmlspecialchars($achievement['name']); ?></div>
                                        <div class="achievement-desc"><?php echo htmlspecialchars($achievement['description']); ?></div>
                                        <div class="achievement-date">
                                            <?php echo date('d.m.Y', strtotime($achievement['earned_at'])); ?>
                                        </div>
                                    </div>
                                    <div class="achievement-badge">
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stats-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.stats-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.task-card {
    display: flex;
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.task-card:hover {
    background: white;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.task-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.task-content {
    flex: 1;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.task-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    color: var(--dark-color);
}

.task-reward {
    text-align: right;
}

.reward-amount {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--success-color);
    display: block;
}

.reward-type {
    font-size: 0.8rem;
    color: #6c757d;
}

.task-description {
    color: #6c757d;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.task-progress {
    margin-bottom: 1rem;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.task-hint {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
    padding: 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.9rem;
}

.completed-task {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.completed-task:last-child {
    border-bottom: none;
}

.task-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.task-name i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.completion-date {
    font-size: 0.8rem;
    color: #6c757d;
}

.reward-badge {
    background: var(--success-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.9rem;
    font-weight: 600;
}

.achievement-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.achievement-item:last-child {
    border-bottom: none;
}

.achievement-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--warning-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.achievement-info {
    flex: 1;
}

.achievement-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.achievement-desc {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.achievement-date {
    font-size: 0.8rem;
    color: #6c757d;
}

.achievement-badge {
    color: var(--warning-color);
    font-size: 1.2rem;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

@media (max-width: 768px) {
    .task-card {
        flex-direction: column;
        text-align: center;
    }

    .task-icon {
        margin: 0 auto 1rem;
    }

    .task-header {
        flex-direction: column;
        text-align: center;
    }

    .task-reward {
        text-align: center;
        margin-top: 0.5rem;
    }

    .completed-task {
        flex-direction: column;
        text-align: center;
    }

    .task-reward-received {
        margin-top: 0.5rem;
    }
}
</style>

<?php
/**
 * Получение заданий пользователя
 */
function getUserTasks($user_id) {
    global $conn;

    $tasks = ['active' => [], 'completed' => []];

    // Активные задания
    $stmt = $conn->prepare("
        SELECT
            t.*,
            COALESCE(utp.current_value, 0) as current_value,
            utp.is_completed,
            utp.completed_at
        FROM tasks t
        LEFT JOIN user_task_progress utp ON t.id = utp.task_id AND utp.user_id = ?
        WHERE t.is_active = 1
        AND (utp.is_completed IS NULL OR utp.is_completed = 0)
        ORDER BY t.id
    ");
    $stmt->execute([$user_id]);
    $tasks['active'] = $stmt->fetchAll();

    // Выполненные задания
    $stmt = $conn->prepare("
        SELECT
            t.*,
            utp.current_value,
            utp.completed_at
        FROM tasks t
        JOIN user_task_progress utp ON t.id = utp.task_id
        WHERE utp.user_id = ? AND utp.is_completed = 1
        ORDER BY utp.completed_at DESC
        LIMIT 10
    ");
    $stmt->execute([$user_id]);
    $tasks['completed'] = $stmt->fetchAll();

    return $tasks;
}

/**
 * Получение достижений пользователя
 */
function getUserAchievements($user_id) {
    global $conn;

    $stmt = $conn->prepare("
        SELECT
            a.*,
            ua.earned_at
        FROM achievements a
        JOIN user_achievements ua ON a.id = ua.achievement_id
        WHERE ua.user_id = ?
        ORDER BY ua.earned_at DESC
    ");
    $stmt->execute([$user_id]);

    return $stmt->fetchAll();
}

/**
 * Получение статистики заданий
 */
function getTaskStats($user_id) {
    global $conn;

    $stmt = $conn->prepare("
        SELECT
            COUNT(CASE WHEN utp.is_completed = 1 THEN 1 END) as completed_tasks,
            COUNT(CASE WHEN t.is_active = 1 AND (utp.is_completed IS NULL OR utp.is_completed = 0) THEN 1 END) as available_tasks,
            COALESCE(SUM(CASE WHEN utp.is_completed = 1 THEN t.reward_amount ELSE 0 END), 0) as total_rewards
        FROM tasks t
        LEFT JOIN user_task_progress utp ON t.id = utp.task_id AND utp.user_id = ?
    ");
    $stmt->execute([$user_id]);

    return $stmt->fetch();
}

/**
 * Выполнение задания
 */
function completeTask($task_id, $user_id) {
    global $conn;

    try {
        $conn->beginTransaction();

        // Проверяем задание и прогресс
        $stmt = $conn->prepare("
            SELECT
                t.*,
                utp.current_value,
                utp.is_completed
            FROM tasks t
            LEFT JOIN user_task_progress utp ON t.id = utp.task_id AND utp.user_id = ?
            WHERE t.id = ? AND t.is_active = 1
        ");
        $stmt->execute([$user_id, $task_id]);
        $task = $stmt->fetch();

        if (!$task) {
            $conn->rollBack();
            return ['success' => false, 'message' => 'Задание не найдено'];
        }

        if ($task['is_completed']) {
            $conn->rollBack();
            return ['success' => false, 'message' => 'Задание уже выполнено'];
        }

        if ($task['current_value'] < $task['target_value']) {
            $conn->rollBack();
            return ['success' => false, 'message' => 'Задание еще не выполнено'];
        }

        // Помечаем задание как выполненное
        $stmt = $conn->prepare("
            UPDATE user_task_progress
            SET is_completed = 1, completed_at = NOW()
            WHERE user_id = ? AND task_id = ?
        ");
        $stmt->execute([$user_id, $task_id]);

        // Получаем текущий баланс
        $stmt = $conn->prepare("SELECT balance FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $current_balance = $stmt->fetch()['balance'];

        // Начисляем награду
        $new_balance = $current_balance + $task['reward_amount'];
        $stmt = $conn->prepare("UPDATE users SET balance = ? WHERE id = ?");
        $stmt->execute([$new_balance, $user_id]);

        // Создаем транзакцию
        $stmt = $conn->prepare("
            INSERT INTO transactions (user_id, type, amount, balance_before, balance_after, description, reference_id, reference_type)
            VALUES (?, 'task_reward', ?, ?, ?, ?, ?, 'task')
        ");
        $stmt->execute([
            $user_id,
            $task['reward_amount'],
            $current_balance,
            $new_balance,
            'Награда за выполнение задания: ' . $task['title'],
            $task_id
        ]);

        // Проверяем достижения
        checkAchievements($user_id);

        // Логирование
        logAction('task_completed', "Task ID: $task_id, Reward: {$task['reward_amount']}");

        $conn->commit();

        return [
            'success' => true,
            'message' => 'Задание выполнено! Награда ' . formatMoney($task['reward_amount']) . ' зачислена на ваш баланс.'
        ];

    } catch (Exception $e) {
        $conn->rollBack();
        error_log("Task completion error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при выполнении задания'];
    }
}

/**
 * Проверка достижений
 */
function checkAchievements($user_id) {
    global $conn;

    try {
        // Получаем статистику пользователя
        $stmt = $conn->prepare("
            SELECT
                total_invested,
                total_profit,
                (SELECT COUNT(*) FROM user_task_progress WHERE user_id = ? AND is_completed = 1) as completed_tasks,
                (SELECT COUNT(*) FROM referrals WHERE referrer_id = ? AND is_active = 1) as total_referrals
            FROM users
            WHERE id = ?
        ");
        $stmt->execute([$user_id, $user_id, $user_id]);
        $stats = $stmt->fetch();

        // Проверяем различные достижения
        $achievements_to_check = [
            ['type' => 'first_investment', 'condition' => $stats['total_invested'] > 0],
            ['type' => 'investor_bronze', 'condition' => $stats['total_invested'] >= 100],
            ['type' => 'investor_silver', 'condition' => $stats['total_invested'] >= 1000],
            ['type' => 'investor_gold', 'condition' => $stats['total_invested'] >= 10000],
            ['type' => 'profit_maker', 'condition' => $stats['total_profit'] >= 50],
            ['type' => 'task_master', 'condition' => $stats['completed_tasks'] >= 10],
            ['type' => 'referral_starter', 'condition' => $stats['total_referrals'] >= 1],
            ['type' => 'referral_master', 'condition' => $stats['total_referrals'] >= 10]
        ];

        foreach ($achievements_to_check as $achievement_check) {
            if ($achievement_check['condition']) {
                grantAchievement($user_id, $achievement_check['type']);
            }
        }

    } catch (Exception $e) {
        error_log("Achievement check error: " . $e->getMessage());
    }
}

/**
 * Выдача достижения
 */
function grantAchievement($user_id, $achievement_type) {
    global $conn;

    try {
        // Проверяем, есть ли уже это достижение
        $stmt = $conn->prepare("
            SELECT ua.id
            FROM user_achievements ua
            JOIN achievements a ON ua.achievement_id = a.id
            WHERE ua.user_id = ? AND a.type = ?
        ");
        $stmt->execute([$user_id, $achievement_type]);

        if ($stmt->fetch()) {
            return; // Достижение уже есть
        }

        // Получаем достижение
        $stmt = $conn->prepare("SELECT * FROM achievements WHERE type = ?");
        $stmt->execute([$achievement_type]);
        $achievement = $stmt->fetch();

        if (!$achievement) {
            return; // Достижение не найдено
        }

        // Выдаем достижение
        $stmt = $conn->prepare("
            INSERT INTO user_achievements (user_id, achievement_id, earned_at)
            VALUES (?, ?, NOW())
        ");
        $stmt->execute([$user_id, $achievement['id']]);

        // Создаем уведомление
        $stmt = $conn->prepare("
            INSERT INTO notifications (user_id, title, message, type)
            VALUES (?, ?, ?, 'success')
        ");
        $stmt->execute([
            $user_id,
            'Новое достижение!',
            'Вы получили достижение: ' . $achievement['name']
        ]);

    } catch (Exception $e) {
        error_log("Grant achievement error: " . $e->getMessage());
    }
}

/**
 * Получение иконки задания
 */
function getTaskIcon($type) {
    $icons = [
        'daily_login' => 'fas fa-calendar-check',
        'first_investment' => 'fas fa-chart-line',
        'total_investment' => 'fas fa-dollar-sign',
        'referral_invite' => 'fas fa-user-plus',
        'profile_complete' => 'fas fa-user-cog',
        'social_share' => 'fas fa-share-alt'
    ];

    return $icons[$type] ?? 'fas fa-tasks';
}

/**
 * Получение подсказки для задания
 */
function getTaskHint($type) {
    $hints = [
        'daily_login' => 'Заходите в личный кабинет каждый день',
        'first_investment' => 'Создайте свою первую инвестицию',
        'total_investment' => 'Увеличьте общую сумму инвестиций',
        'referral_invite' => 'Пригласите друзей по реферальной ссылке',
        'profile_complete' => 'Заполните все поля в профиле',
        'social_share' => 'Поделитесь ссылкой в социальных сетях'
    ];

    return $hints[$type] ?? 'Выполните условия задания';
}

/**
 * Получение названия типа награды
 */
function getRewardTypeName($type) {
    $types = [
        'balance' => 'на баланс',
        'bonus' => 'бонус',
        'points' => 'очки'
    ];

    return $types[$type] ?? '';
}

/**
 * Получение иконки достижения
 */
function getAchievementIcon($type) {
    $icons = [
        'first_investment' => 'fas fa-seedling',
        'investor_bronze' => 'fas fa-medal',
        'investor_silver' => 'fas fa-medal',
        'investor_gold' => 'fas fa-trophy',
        'profit_maker' => 'fas fa-coins',
        'task_master' => 'fas fa-tasks',
        'referral_starter' => 'fas fa-handshake',
        'referral_master' => 'fas fa-crown'
    ];

    return $icons[$type] ?? 'fas fa-star';
}
?>