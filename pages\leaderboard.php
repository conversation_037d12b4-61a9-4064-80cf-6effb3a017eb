<?php
$page_title = "Лидерборд";

// Получение данных лидерборда
$top_investors = getTopInvestors();
$top_earners = getTopEarners();
$top_referrers = getTopReferrers();
$recent_achievements = getRecentAchievements();

// Получение позиции текущего пользователя
$user_position = null;
if (isLoggedIn()) {
    $user_position = getUserPosition($_SESSION['user_id']);
}
?>

<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="page-header mb-4">
                <h2 class="page-title">
                    <i class="fas fa-trophy text-warning"></i> Лидерборд
                </h2>
                <p class="page-subtitle">Топ инвесторов и самые активные участники платформы</p>
            </div>
        </div>
    </div>
    
    <!-- Позиция пользователя -->
    <?php if ($user_position): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="user-position-card">
                    <div class="position-info">
                        <h5>Ваша позиция в рейтинге</h5>
                        <div class="position-stats">
                            <div class="position-item">
                                <span class="position-label">По инвестициям:</span>
                                <span class="position-value">#<?php echo $user_position['investment_rank']; ?></span>
                            </div>
                            <div class="position-item">
                                <span class="position-label">По прибыли:</span>
                                <span class="position-value">#<?php echo $user_position['profit_rank']; ?></span>
                            </div>
                            <div class="position-item">
                                <span class="position-label">По рефералам:</span>
                                <span class="position-value">#<?php echo $user_position['referral_rank']; ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="position-action">
                        <a href="index.php?page=invest" class="btn btn-primary">
                            <i class="fas fa-arrow-up"></i> Улучшить позицию
                        </a>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <div class="row">
        <!-- Топ инвесторов -->
        <div class="col-lg-4 mb-4">
            <div class="card leaderboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line text-primary"></i> Топ инвесторов
                    </h5>
                    <small class="text-muted">По общей сумме инвестиций</small>
                </div>
                <div class="card-body">
                    <div class="leaderboard-list">
                        <?php foreach ($top_investors as $index => $investor): ?>
                            <div class="leaderboard-item <?php echo $index < 3 ? 'top-three' : ''; ?>">
                                <div class="rank-badge rank-<?php echo $index + 1; ?>">
                                    <?php if ($index < 3): ?>
                                        <i class="fas fa-<?php echo ['trophy', 'medal', 'award'][$index]; ?>"></i>
                                    <?php else: ?>
                                        <?php echo $index + 1; ?>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="user-info">
                                    <div class="user-avatar">
                                        <img src="<?php echo $investor['avatar'] ?: 'assets/images/default-avatar.png'; ?>" 
                                             alt="Avatar">
                                    </div>
                                    <div class="user-details">
                                        <div class="user-name">
                                            <?php echo htmlspecialchars($investor['first_name'] . ' ' . substr($investor['last_name'], 0, 1) . '.'); ?>
                                        </div>
                                        <div class="user-country">
                                            <?php if ($investor['country']): ?>
                                                <i class="fas fa-flag"></i> <?php echo htmlspecialchars($investor['country']); ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="user-stats">
                                    <div class="stat-value"><?php echo formatMoney($investor['total_invested']); ?></div>
                                    <div class="stat-label">Инвестировано</div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Топ по прибыли -->
        <div class="col-lg-4 mb-4">
            <div class="card leaderboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-coins text-success"></i> Топ по прибыли
                    </h5>
                    <small class="text-muted">По общей полученной прибыли</small>
                </div>
                <div class="card-body">
                    <div class="leaderboard-list">
                        <?php foreach ($top_earners as $index => $earner): ?>
                            <div class="leaderboard-item <?php echo $index < 3 ? 'top-three' : ''; ?>">
                                <div class="rank-badge rank-<?php echo $index + 1; ?>">
                                    <?php if ($index < 3): ?>
                                        <i class="fas fa-<?php echo ['trophy', 'medal', 'award'][$index]; ?>"></i>
                                    <?php else: ?>
                                        <?php echo $index + 1; ?>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="user-info">
                                    <div class="user-avatar">
                                        <img src="<?php echo $earner['avatar'] ?: 'assets/images/default-avatar.png'; ?>" 
                                             alt="Avatar">
                                    </div>
                                    <div class="user-details">
                                        <div class="user-name">
                                            <?php echo htmlspecialchars($earner['first_name'] . ' ' . substr($earner['last_name'], 0, 1) . '.'); ?>
                                        </div>
                                        <div class="user-country">
                                            <?php if ($earner['country']): ?>
                                                <i class="fas fa-flag"></i> <?php echo htmlspecialchars($earner['country']); ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="user-stats">
                                    <div class="stat-value"><?php echo formatMoney($earner['total_profit']); ?></div>
                                    <div class="stat-label">Прибыль</div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Топ рефереров -->
        <div class="col-lg-4 mb-4">
            <div class="card leaderboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users text-info"></i> Топ рефереров
                    </h5>
                    <small class="text-muted">По количеству приглашенных</small>
                </div>
                <div class="card-body">
                    <div class="leaderboard-list">
                        <?php foreach ($top_referrers as $index => $referrer): ?>
                            <div class="leaderboard-item <?php echo $index < 3 ? 'top-three' : ''; ?>">
                                <div class="rank-badge rank-<?php echo $index + 1; ?>">
                                    <?php if ($index < 3): ?>
                                        <i class="fas fa-<?php echo ['trophy', 'medal', 'award'][$index]; ?>"></i>
                                    <?php else: ?>
                                        <?php echo $index + 1; ?>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="user-info">
                                    <div class="user-avatar">
                                        <img src="<?php echo $referrer['avatar'] ?: 'assets/images/default-avatar.png'; ?>" 
                                             alt="Avatar">
                                    </div>
                                    <div class="user-details">
                                        <div class="user-name">
                                            <?php echo htmlspecialchars($referrer['first_name'] . ' ' . substr($referrer['last_name'], 0, 1) . '.'); ?>
                                        </div>
                                        <div class="user-country">
                                            <?php if ($referrer['country']): ?>
                                                <i class="fas fa-flag"></i> <?php echo htmlspecialchars($referrer['country']); ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="user-stats">
                                    <div class="stat-value"><?php echo $referrer['referral_count']; ?></div>
                                    <div class="stat-label">Рефералов</div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Последние достижения -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-star text-warning"></i> Последние достижения
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_achievements)): ?>
                        <div class="empty-state">
                            <i class="fas fa-medal fa-3x text-muted mb-3"></i>
                            <h5>Достижений пока нет</h5>
                            <p class="text-muted">Станьте первым, кто получит достижение!</p>
                        </div>
                    <?php else: ?>
                        <div class="achievements-timeline">
                            <?php foreach ($recent_achievements as $achievement): ?>
                                <div class="achievement-item">
                                    <div class="achievement-avatar">
                                        <img src="<?php echo $achievement['avatar'] ?: 'assets/images/default-avatar.png'; ?>" 
                                             alt="Avatar">
                                    </div>
                                    
                                    <div class="achievement-content">
                                        <div class="achievement-text">
                                            <strong><?php echo htmlspecialchars($achievement['first_name'] . ' ' . substr($achievement['last_name'], 0, 1) . '.'); ?></strong>
                                            получил достижение
                                            <span class="achievement-name"><?php echo htmlspecialchars($achievement['achievement_name']); ?></span>
                                        </div>
                                        <div class="achievement-time">
                                            <?php echo timeAgo($achievement['earned_at']); ?>
                                        </div>
                                    </div>
                                    
                                    <div class="achievement-badge">
                                        <i class="fas fa-trophy"></i>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.user-position-card {
    background: var(--primary-gradient);
    color: white;
    border-radius: 1rem;
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.position-stats {
    display: flex;
    gap: 2rem;
    margin-top: 1rem;
}

.position-item {
    display: flex;
    flex-direction: column;
    text-align: center;
}

.position-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.position-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-top: 0.25rem;
}

.leaderboard-card {
    height: 100%;
}

.leaderboard-list {
    max-height: 500px;
    overflow-y: auto;
}

.leaderboard-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.leaderboard-item:hover {
    background: #f8f9fa;
    border-radius: 0.5rem;
    margin: 0 -0.5rem;
    padding: 1rem 0.5rem;
}

.leaderboard-item:last-child {
    border-bottom: none;
}

.leaderboard-item.top-three {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05));
    border-radius: 0.5rem;
    margin: 0 -0.5rem;
    padding: 1rem 0.5rem;
}

.rank-badge {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-right: 1rem;
    flex-shrink: 0;
}

.rank-badge.rank-1 {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: white;
    font-size: 1.2rem;
}

.rank-badge.rank-2 {
    background: linear-gradient(135deg, #C0C0C0, #A0A0A0);
    color: white;
    font-size: 1.1rem;
}

.rank-badge.rank-3 {
    background: linear-gradient(135deg, #CD7F32, #B8860B);
    color: white;
    font-size: 1rem;
}

.rank-badge:not(.rank-1):not(.rank-2):not(.rank-3) {
    background: #f8f9fa;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.user-info {
    display: flex;
    align-items: center;
    flex: 1;
    margin-right: 1rem;
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 1rem;
    flex-shrink: 0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.user-country {
    font-size: 0.8rem;
    color: #6c757d;
}

.user-stats {
    text-align: right;
    flex-shrink: 0;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.achievements-timeline {
    max-height: 400px;
    overflow-y: auto;
}

.achievement-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.achievement-item:last-child {
    border-bottom: none;
}

.achievement-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 1rem;
    flex-shrink: 0;
}

.achievement-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.achievement-content {
    flex: 1;
}

.achievement-text {
    margin-bottom: 0.25rem;
}

.achievement-name {
    color: var(--warning-color);
    font-weight: 600;
}

.achievement-time {
    font-size: 0.8rem;
    color: #6c757d;
}

.achievement-badge {
    color: var(--warning-color);
    font-size: 1.2rem;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

@media (max-width: 768px) {
    .user-position-card {
        flex-direction: column;
        text-align: center;
    }
    
    .position-stats {
        justify-content: center;
        margin-top: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .position-action {
        margin-top: 1rem;
    }
    
    .leaderboard-item {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem 0;
    }
    
    .user-info {
        flex-direction: column;
        margin: 1rem 0;
    }
    
    .user-avatar {
        margin: 0 0 0.5rem 0;
    }
    
    .achievement-item {
        flex-direction: column;
        text-align: center;
    }
    
    .achievement-content {
        margin: 0.5rem 0;
    }
}
</style>

<?php
/**
 * Получение топ инвесторов
 */
function getTopInvestors() {
    global $conn;
    
    $stmt = $conn->query("
        SELECT 
            u.id,
            u.first_name,
            u.last_name,
            u.avatar,
            u.country,
            u.total_invested
        FROM users u
        WHERE u.status = 'active' AND u.total_invested > 0
        ORDER BY u.total_invested DESC
        LIMIT 10
    ");
    
    return $stmt->fetchAll();
}

/**
 * Получение топ по прибыли
 */
function getTopEarners() {
    global $conn;
    
    $stmt = $conn->query("
        SELECT 
            u.id,
            u.first_name,
            u.last_name,
            u.avatar,
            u.country,
            u.total_profit
        FROM users u
        WHERE u.status = 'active' AND u.total_profit > 0
        ORDER BY u.total_profit DESC
        LIMIT 10
    ");
    
    return $stmt->fetchAll();
}

/**
 * Получение топ рефереров
 */
function getTopReferrers() {
    global $conn;
    
    $stmt = $conn->query("
        SELECT 
            u.id,
            u.first_name,
            u.last_name,
            u.avatar,
            u.country,
            COUNT(r.referred_id) as referral_count
        FROM users u
        JOIN referrals r ON u.id = r.referrer_id
        WHERE u.status = 'active' AND r.is_active = 1
        GROUP BY u.id
        ORDER BY referral_count DESC
        LIMIT 10
    ");
    
    return $stmt->fetchAll();
}

/**
 * Получение последних достижений
 */
function getRecentAchievements() {
    global $conn;
    
    $stmt = $conn->query("
        SELECT 
            u.first_name,
            u.last_name,
            u.avatar,
            a.name as achievement_name,
            ua.earned_at
        FROM user_achievements ua
        JOIN users u ON ua.user_id = u.id
        JOIN achievements a ON ua.achievement_id = a.id
        WHERE u.status = 'active'
        ORDER BY ua.earned_at DESC
        LIMIT 20
    ");
    
    return $stmt->fetchAll();
}

/**
 * Получение позиции пользователя
 */
function getUserPosition($user_id) {
    global $conn;
    
    // Позиция по инвестициям
    $stmt = $conn->prepare("
        SELECT COUNT(*) + 1 as investment_rank
        FROM users 
        WHERE total_invested > (SELECT total_invested FROM users WHERE id = ?)
        AND status = 'active'
    ");
    $stmt->execute([$user_id]);
    $investment_rank = $stmt->fetch()['investment_rank'];
    
    // Позиция по прибыли
    $stmt = $conn->prepare("
        SELECT COUNT(*) + 1 as profit_rank
        FROM users 
        WHERE total_profit > (SELECT total_profit FROM users WHERE id = ?)
        AND status = 'active'
    ");
    $stmt->execute([$user_id]);
    $profit_rank = $stmt->fetch()['profit_rank'];
    
    // Позиция по рефералам
    $stmt = $conn->prepare("
        SELECT COUNT(*) + 1 as referral_rank
        FROM (
            SELECT referrer_id, COUNT(*) as ref_count
            FROM referrals 
            WHERE is_active = 1
            GROUP BY referrer_id
            HAVING ref_count > (
                SELECT COUNT(*) 
                FROM referrals 
                WHERE referrer_id = ? AND is_active = 1
            )
        ) as ref_counts
    ");
    $stmt->execute([$user_id]);
    $referral_rank = $stmt->fetch()['referral_rank'];
    
    return [
        'investment_rank' => $investment_rank,
        'profit_rank' => $profit_rank,
        'referral_rank' => $referral_rank
    ];
}

/**
 * Форматирование времени "назад"
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'только что';
    if ($time < 3600) return floor($time/60) . ' мин назад';
    if ($time < 86400) return floor($time/3600) . ' ч назад';
    if ($time < 2592000) return floor($time/86400) . ' дн назад';
    
    return date('d.m.Y', strtotime($datetime));
}
?>
