<?php
$page_title = "Восстановление пароля";

// Если пользователь уже авторизован, перенаправляем в личный кабинет
if (isLoggedIn()) {
    redirect('index.php?page=dashboard');
}

$step = $_GET['step'] ?? 'request';
$token = $_GET['token'] ?? '';

// Обработка формы
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step === 'request') {
        $result = processForgotPasswordRequest();
    } elseif ($step === 'reset') {
        $result = processPasswordReset();
    }
    
    if ($result['success']) {
        if ($step === 'request') {
            $success_message = $result['message'];
        } else {
            redirect('index.php?page=login&reset=1', $result['message'], 'success');
        }
    } else {
        $error_message = $result['message'];
    }
}

// Проверка токена для сброса пароля
if ($step === 'reset' && $token) {
    $token_valid = validateResetToken($token);
    if (!$token_valid) {
        redirect('index.php?page=forgot-password', 'Недействительная или истекшая ссылка для сброса пароля', 'error');
    }
}
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-5 col-md-7">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient text-white text-center">
                    <h3 class="mb-0">
                        <i class="fas fa-key"></i> 
                        <?php echo $step === 'reset' ? 'Новый пароль' : 'Восстановление пароля'; ?>
                    </h3>
                    <p class="mb-0 mt-2">
                        <?php echo $step === 'reset' ? 'Введите новый пароль' : 'Мы поможем восстановить доступ'; ?>
                    </p>
                </div>
                
                <div class="card-body p-4">
                    <?php if (isset($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($success_message)): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($step === 'request'): ?>
                        <!-- Форма запроса восстановления -->
                        <form method="POST" class="needs-validation" novalidate>
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email адрес</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                           required>
                                </div>
                                <div class="form-text">
                                    Введите email, указанный при регистрации
                                </div>
                                <div class="invalid-feedback">
                                    Введите корректный email адрес
                                </div>
                            </div>
                            
                            <!-- Капча -->
                            <?php
                            $num1 = rand(1, 10);
                            $num2 = rand(1, 10);
                            $captcha_answer = $num1 + $num2;
                            $_SESSION['captcha_answer'] = $captcha_answer;
                            ?>
                            <div class="mb-3">
                                <label for="captcha" class="form-label">
                                    Решите пример: <?php echo $num1; ?> + <?php echo $num2; ?> = ?
                                </label>
                                <input type="number" class="form-control" id="captcha" name="captcha" 
                                       required min="0" max="20">
                                <div class="invalid-feedback">
                                    Решите математический пример
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane"></i> Отправить ссылку
                                </button>
                            </div>
                        </form>
                        
                    <?php elseif ($step === 'reset' && $token): ?>
                        <!-- Форма сброса пароля -->
                        <form method="POST" class="needs-validation" novalidate>
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="token" value="<?php echo htmlspecialchars($token); ?>">
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Новый пароль</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           required minlength="8">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    Минимум 8 символов, должен содержать заглавные и строчные буквы, цифры
                                </div>
                                <div class="invalid-feedback">
                                    Пароль должен содержать минимум 8 символов
                                </div>
                                <div class="password-strength mt-2">
                                    <div class="progress" style="height: 5px;">
                                        <div class="progress-bar" id="passwordStrength" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted" id="passwordStrengthText"></small>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password_confirm" class="form-label">Подтверждение пароля</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password_confirm" name="password_confirm" 
                                           required>
                                </div>
                                <div class="invalid-feedback">
                                    Пароли не совпадают
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-check"></i> Изменить пароль
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <a href="index.php?page=login" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Вернуться к входу
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Информация о безопасности -->
            <div class="card mt-4 border-0 bg-light">
                <div class="card-body">
                    <h6 class="card-title text-center">
                        <i class="fas fa-info-circle text-info"></i> Как это работает?
                    </h6>
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="step-icon">1</div>
                            <small>Введите email</small>
                        </div>
                        <div class="col-4">
                            <div class="step-icon">2</div>
                            <small>Проверьте почту</small>
                        </div>
                        <div class="col-4">
                            <div class="step-icon">3</div>
                            <small>Создайте новый пароль</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.step-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    font-weight: bold;
    font-size: 0.9rem;
}

.password-strength .progress-bar {
    transition: all 0.3s ease;
}

.password-strength .progress-bar.weak {
    background-color: #dc3545;
}

.password-strength .progress-bar.medium {
    background-color: #ffc107;
}

.password-strength .progress-bar.strong {
    background-color: #28a745;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Переключение видимости пароля
    const togglePassword = document.getElementById('togglePassword');
    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    }
    
    // Проверка силы пароля
    const passwordInput = document.getElementById('password');
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('passwordStrength');
            const strengthText = document.getElementById('passwordStrengthText');
            
            let strength = 0;
            let text = '';
            
            if (password.length >= 8) strength += 25;
            if (/[a-z]/.test(password)) strength += 25;
            if (/[A-Z]/.test(password)) strength += 25;
            if (/[0-9]/.test(password)) strength += 25;
            
            strengthBar.style.width = strength + '%';
            
            if (strength < 50) {
                strengthBar.className = 'progress-bar weak';
                text = 'Слабый пароль';
            } else if (strength < 100) {
                strengthBar.className = 'progress-bar medium';
                text = 'Средний пароль';
            } else {
                strengthBar.className = 'progress-bar strong';
                text = 'Сильный пароль';
            }
            
            strengthText.textContent = text;
        });
    }
    
    // Проверка совпадения паролей
    const passwordConfirm = document.getElementById('password_confirm');
    if (passwordConfirm) {
        passwordConfirm.addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('Пароли не совпадают');
            } else {
                this.setCustomValidity('');
            }
        });
    }
});
</script>

<?php
/**
 * Обработка запроса на восстановление пароля
 */
function processForgotPasswordRequest() {
    global $conn;
    
    // Проверка CSRF токена
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        return ['success' => false, 'message' => 'Ошибка безопасности. Попробуйте еще раз.'];
    }
    
    $email = sanitizeInput($_POST['email']);
    $captcha = intval($_POST['captcha']);
    
    // Валидация
    if (!validateEmail($email)) {
        return ['success' => false, 'message' => 'Некорректный email адрес'];
    }
    
    // Проверка капчи
    if (!isset($_SESSION['captcha_answer']) || $captcha !== $_SESSION['captcha_answer']) {
        return ['success' => false, 'message' => 'Неверно решен пример'];
    }
    
    // Очищаем капчу
    unset($_SESSION['captcha_answer']);
    
    try {
        // Проверка лимита запросов (не более 3 в час)
        $stmt = $conn->prepare("
            SELECT COUNT(*) as request_count 
            FROM users 
            WHERE email = ? 
            AND password_reset_token IS NOT NULL 
            AND password_reset_expires > DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ");
        $stmt->execute([$email]);
        $request_count = $stmt->fetch()['request_count'];
        
        if ($request_count >= 3) {
            return ['success' => false, 'message' => 'Превышен лимит запросов. Попробуйте через час.'];
        }
        
        // Поиск пользователя
        $stmt = $conn->prepare("SELECT id, first_name, email FROM users WHERE email = ? AND status = 'active'");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if (!$user) {
            // Не раскрываем информацию о существовании email
            return ['success' => true, 'message' => 'Если указанный email зарегистрирован, на него будет отправлена ссылка для восстановления пароля.'];
        }
        
        // Генерация токена
        $reset_token = generateToken();
        $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
        
        // Сохранение токена
        $stmt = $conn->prepare("
            UPDATE users 
            SET password_reset_token = ?, password_reset_expires = ? 
            WHERE id = ?
        ");
        $stmt->execute([$reset_token, $expires, $user['id']]);
        
        // Отправка email
        sendPasswordResetEmail($user['email'], $user['first_name'], $reset_token);
        
        // Логирование
        logAction('password_reset_requested', "Email: {$email}");
        
        return ['success' => true, 'message' => 'Ссылка для восстановления пароля отправлена на ваш email.'];
        
    } catch (Exception $e) {
        error_log("Password reset request error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при обработке запроса. Попробуйте позже.'];
    }
}

/**
 * Обработка сброса пароля
 */
function processPasswordReset() {
    global $conn;
    
    // Проверка CSRF токена
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        return ['success' => false, 'message' => 'Ошибка безопасности. Попробуйте еще раз.'];
    }
    
    $token = sanitizeInput($_POST['token']);
    $password = $_POST['password'];
    $password_confirm = $_POST['password_confirm'];
    
    // Валидация
    if (!validatePassword($password)) {
        return ['success' => false, 'message' => 'Пароль должен содержать минимум 8 символов, включая заглавные и строчные буквы, цифры'];
    }
    
    if ($password !== $password_confirm) {
        return ['success' => false, 'message' => 'Пароли не совпадают'];
    }
    
    try {
        // Проверка токена
        $stmt = $conn->prepare("
            SELECT id, email 
            FROM users 
            WHERE password_reset_token = ? 
            AND password_reset_expires > NOW()
            AND status = 'active'
        ");
        $stmt->execute([$token]);
        $user = $stmt->fetch();
        
        if (!$user) {
            return ['success' => false, 'message' => 'Недействительная или истекшая ссылка для сброса пароля'];
        }
        
        // Хеширование нового пароля
        $password_hash = hashPassword($password);
        
        // Обновление пароля и очистка токена
        $stmt = $conn->prepare("
            UPDATE users 
            SET password_hash = ?, password_reset_token = NULL, password_reset_expires = NULL 
            WHERE id = ?
        ");
        $stmt->execute([$password_hash, $user['id']]);
        
        // Логирование
        logAction('password_reset_completed', "User ID: {$user['id']}, Email: {$user['email']}");
        
        return ['success' => true, 'message' => 'Пароль успешно изменен!'];
        
    } catch (Exception $e) {
        error_log("Password reset error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при изменении пароля. Попробуйте позже.'];
    }
}

/**
 * Валидация токена сброса пароля
 */
function validateResetToken($token) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            SELECT id 
            FROM users 
            WHERE password_reset_token = ? 
            AND password_reset_expires > NOW()
        ");
        $stmt->execute([$token]);
        
        return $stmt->fetch() !== false;
        
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Отправка email для сброса пароля
 */
function sendPasswordResetEmail($email, $name, $token) {
    $subject = 'Восстановление пароля - GreenChain EcoFund';
    $reset_link = SITE_URL . "/index.php?page=forgot-password&step=reset&token=" . $token;
    
    $message = "
    <h2>Восстановление пароля</h2>
    <p>Здравствуйте, {$name}!</p>
    <p>Вы запросили восстановление пароля для вашего аккаунта в GreenChain EcoFund.</p>
    <p>Для создания нового пароля перейдите по ссылке:</p>
    <p><a href='{$reset_link}' style='background: #2E8B57; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Восстановить пароль</a></p>
    <p>Если кнопка не работает, скопируйте и вставьте эту ссылку в браузер:</p>
    <p>{$reset_link}</p>
    <p><strong>Важно:</strong> Ссылка действительна в течение 1 часа.</p>
    <p>Если вы не запрашивали восстановление пароля, просто проигнорируйте это письмо.</p>
    <p>С уважением,<br>Команда GreenChain EcoFund</p>
    ";
    
    return sendEmail($email, $subject, $message, true);
}
?>
