<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест кнопок инвестирования - <PERSON><PERSON><PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/luxury-eco-design.css" rel="stylesheet">
    <link href="assets/css/adaptive-contrast.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h2 class="text-center mb-4">🧪 Тест кнопок "Инвестировать"</h2>
                
                <!-- Тест 1: Кнопка без авторизации -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Тест 1: Кнопка для неавторизованного пользователя</h5>
                    </div>
                    <div class="card-body">
                        <p>При клике должен показаться уведомление и переход на страницу входа</p>
                        <button class="btn btn-primary invest-btn" data-package-type="flexible">
                            <i class="fas fa-chart-line"></i> Инвестировать (Гибкий)
                        </button>
                    </div>
                </div>

                <!-- Тест 2: Кнопка с package-id -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Тест 2: Кнопка с конкретным пакетом</h5>
                    </div>
                    <div class="card-body">
                        <p>При клике должен открыться переход на страницу инвестиций с параметрами</p>
                        <button class="btn btn-success invest-btn" data-package-id="1" data-package-type="fixed">
                            <i class="fas fa-chart-line"></i> Инвестировать (Пакет #1)
                        </button>
                    </div>
                </div>

                <!-- Тест 3: Кнопка только с типом -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Тест 3: Кнопка только с типом пакета</h5>
                    </div>
                    <div class="card-body">
                        <p>При клике должен открыться переход на страницу инвестиций с типом пакета</p>
                        <button class="btn btn-warning invest-btn" data-package-type="flexible">
                            <i class="fas fa-chart-line"></i> Инвестировать (Только тип)
                        </button>
                    </div>
                </div>

                <!-- Тест 4: Кнопка без параметров -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Тест 4: Кнопка без параметров</h5>
                    </div>
                    <div class="card-body">
                        <p>При клике должен открыться переход на общую страницу инвестиций</p>
                        <button class="btn btn-info invest-btn">
                            <i class="fas fa-chart-line"></i> Инвестировать (Без параметров)
                        </button>
                    </div>
                </div>

                <!-- Статус авторизации -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Статус авторизации</h5>
                    </div>
                    <div class="card-body">
                        <p>Текущий статус: <span id="auth-status" class="badge bg-secondary">Проверяется...</span></p>
                        <button class="btn btn-sm btn-outline-primary" onclick="toggleAuthStatus()">
                            Переключить статус авторизации
                        </button>
                    </div>
                </div>

                <!-- Лог событий -->
                <div class="card">
                    <div class="card-header">
                        <h5>Лог событий</h5>
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="clearLog()">
                            Очистить лог
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="event-log" style="height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px;">
                            <div class="text-muted">Лог событий будет отображаться здесь...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Симуляция переменной авторизации
        let isLoggedIn = false;
        let currentUser = null;

        // Функция логирования
        function logEvent(message, type = 'info') {
            const log = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : 'text-info';
            
            log.innerHTML += `<div class="${colorClass}"><strong>[${timestamp}]</strong> ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }

        // Обновление статуса авторизации
        function updateAuthStatus() {
            const statusElement = document.getElementById('auth-status');
            if (isLoggedIn) {
                statusElement.textContent = 'Авторизован';
                statusElement.className = 'badge bg-success';
            } else {
                statusElement.textContent = 'Не авторизован';
                statusElement.className = 'badge bg-danger';
            }
        }

        // Переключение статуса авторизации
        function toggleAuthStatus() {
            isLoggedIn = !isLoggedIn;
            if (isLoggedIn) {
                currentUser = { id: 1, name: 'Test User', role: 'user' };
            } else {
                currentUser = null;
            }
            updateAuthStatus();
            logEvent(`Статус авторизации изменен на: ${isLoggedIn ? 'авторизован' : 'не авторизован'}`, 'info');
        }

        // Очистка лога
        function clearLog() {
            document.getElementById('event-log').innerHTML = '<div class="text-muted">Лог очищен...</div>';
        }

        // Симуляция функций из main.js
        function showLoginModal() {
            logEvent('showLoginModal() вызвана - показ уведомления о необходимости входа', 'error');
            alert('Для выполнения этого действия необходимо войти в систему.');
            // Симуляция перехода
            logEvent('Переход на: index.php?page=login', 'info');
        }

        function showInvestModal(packageId, packageType) {
            logEvent(`showInvestModal() вызвана с параметрами: packageId=${packageId}, packageType=${packageType}`, 'success');
            
            // Симуляция логики из функции
            if (packageId) {
                logEvent(`Попытка загрузки данных пакета с ID: ${packageId}`, 'info');
                // Симуляция успешной загрузки
                setTimeout(() => {
                    logEvent(`Переход на: index.php?page=invest&package_id=${packageId}`, 'success');
                }, 500);
            } else {
                let url = 'index.php?page=invest';
                if (packageType) {
                    url += `&package_type=${packageType}`;
                }
                logEvent(`Переход на: ${url}`, 'success');
            }
        }

        // Обработчик кликов по кнопкам инвестирования
        function handleInvestClick(e) {
            e.preventDefault();
            const packageId = e.target.dataset.packageId;
            const packageType = e.target.dataset.packageType;

            logEvent(`Клик по кнопке инвестирования: packageId=${packageId || 'не указан'}, packageType=${packageType || 'не указан'}`, 'info');
            logEvent(`Статус авторизации: isLoggedIn=${isLoggedIn}, currentUser=${JSON.stringify(currentUser)}`, 'info');

            if (!isLoggedIn) {
                logEvent('Пользователь не авторизован, показываем модальное окно входа', 'error');
                showLoginModal();
                return;
            }

            logEvent('Пользователь авторизован, вызываем showInvestModal', 'success');
            showInvestModal(packageId, packageType);
        }

        // Инициализация
        document.addEventListener('DOMContentLoaded', function() {
            updateAuthStatus();
            logEvent('Страница загружена, обработчики событий инициализированы', 'success');
            
            // Добавление обработчиков событий
            document.addEventListener('click', function(e) {
                if (e.target.matches('.invest-btn')) {
                    handleInvestClick(e);
                }
            });
        });
    </script>
</body>
</html>
