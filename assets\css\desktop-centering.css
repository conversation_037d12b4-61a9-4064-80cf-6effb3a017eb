/* ===== ЦЕНТРИРОВАНИЕ ДЛЯ БОЛЬШИХ ЭКРАНОВ - GREENCHAIN ECOFUND ===== */

/* 
 * Этот файл применяет центрирование контента только на больших экранах (1200px+)
 * Сохраняет все существующие стили, цвета, анимации и адаптивность
 * Улучшает визуальное восприятие на широких мониторах
 */

/* ===== ЦЕНТРИРОВАНИЕ ТОЛЬКО ДЛЯ БОЛЬШИХ ЭКРАНОВ ===== */
@media (min-width: 1200px) {
    
    /* Переменные для центрирования */
    :root {
        --desktop-max-width: 1200px;
        --desktop-max-width-wide: 1400px;
        --desktop-max-width-ultra: 1600px;
        --desktop-padding: 2rem;
        --desktop-padding-wide: 2.5rem;
        --desktop-padding-ultra: 3rem;
    }

    /* ===== ОСНОВНЫЕ КОНТЕЙНЕРЫ ===== */
    
    /* Все основные контейнеры страниц */
    .container,
    .container-fluid,
    .container-xxl,
    .container-xl,
    .container-lg,
    .container-md,
    .container-sm {
        max-width: var(--desktop-max-width) !important;
        margin-left: auto !important;
        margin-right: auto !important;
        padding-left: var(--desktop-padding) !important;
        padding-right: var(--desktop-padding) !important;
    }

    /* ===== СПЕЦИФИЧНЫЕ КОНТЕЙНЕРЫ СТРАНИЦ ===== */
    
    /* Дашборд */
    .modern-dashboard .container-fluid {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* Главная страница - секции */
    .modern-hero .container,
    .content-section-modern .container,
    .features-section .container,
    .how-it-works-section .container,
    .partners-section .container,
    .cta-section .container {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
        padding-left: var(--desktop-padding) !important;
        padding-right: var(--desktop-padding) !important;
    }

    /* Страницы с основным контентом */
    .page-container,
    .main-content,
    .content-wrapper {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
        padding-left: var(--desktop-padding) !important;
        padding-right: var(--desktop-padding) !important;
    }

    /* ===== СЕТКИ И КАРТОЧКИ ===== */
    
    /* Сетки карточек */
    .row {
        max-width: var(--desktop-max-width) !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }

    /* Специальные сетки */
    .features-grid-modern,
    .stats-grid-modern,
    .investments-grid,
    .packages-grid {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* ===== НАВИГАЦИЯ И ФУТЕР ===== */
    
    /* Навигация остается полной ширины, но контент центрируется */
    .modern-navbar .container,
    .navbar .container,
    .header .container {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* Футер */
    footer .container,
    .footer .container {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* ===== СПЕЦИФИЧНЫЕ ЭЛЕМЕНТЫ СТРАНИЦ ===== */
    
    /* Инвестиционные пакеты */
    .package-card,
    .investment-card,
    .luxury-investment-card {
        /* Карточки остаются как есть, центрируется только контейнер */
    }

    /* Калькулятор */
    .calculator-container,
    .calculator-wrapper {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* Лидерборд */
    .leaderboard-container,
    .leaderboard-wrapper {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* Задания */
    .tasks-container,
    .tasks-wrapper {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* Профиль */
    .profile-container,
    .profile-wrapper {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* Админ панель */
    .admin-container,
    .admin-wrapper {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* ===== МОДАЛЬНЫЕ ОКНА ===== */
    
    /* Модальные окна остаются как есть - не центрируем их дополнительно */
    .modal-dialog {
        /* Сохраняем существующее поведение */
    }

    /* ===== ФОРМЫ ===== */
    
    /* Формы входа/регистрации */
    .auth-container,
    .login-container,
    .register-container {
        max-width: 500px !important;
        margin: 0 auto !important;
    }

    /* ===== ТАБЛИЦЫ ===== */
    
    /* Контейнеры таблиц */
    .table-responsive {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* ===== КАРТЫ И ГРАФИКИ ===== */
    
    /* Контейнеры карт */
    .map-container,
    .chart-container {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }
}

/* ===== РАСШИРЕННОЕ ЦЕНТРИРОВАНИЕ ДЛЯ ШИРОКИХ ЭКРАНОВ ===== */
@media (min-width: 1400px) {
    
    :root {
        --desktop-max-width: var(--desktop-max-width-wide);
        --desktop-padding: var(--desktop-padding-wide);
    }

    /* Все контейнеры получают увеличенную ширину */
    .container,
    .container-fluid,
    .modern-dashboard .container-fluid,
    .modern-hero .container,
    .content-section-modern .container,
    .features-section .container,
    .how-it-works-section .container,
    .partners-section .container,
    .cta-section .container,
    .modern-navbar .container,
    footer .container {
        max-width: var(--desktop-max-width-wide) !important;
        padding-left: var(--desktop-padding-wide) !important;
        padding-right: var(--desktop-padding-wide) !important;
    }

    /* Сетки и специальные контейнеры */
    .row,
    .features-grid-modern,
    .stats-grid-modern,
    .investments-grid,
    .packages-grid {
        max-width: var(--desktop-max-width-wide) !important;
    }
}

/* ===== УЛЬТРА-ШИРОКИЕ ЭКРАНЫ ===== */
@media (min-width: 1600px) {
    
    :root {
        --desktop-max-width: var(--desktop-max-width-ultra);
        --desktop-padding: var(--desktop-padding-ultra);
    }

    /* Максимальное использование пространства на очень широких экранах */
    .container,
    .container-fluid,
    .modern-dashboard .container-fluid,
    .modern-hero .container,
    .content-section-modern .container,
    .features-section .container,
    .how-it-works-section .container,
    .partners-section .container,
    .cta-section .container,
    .modern-navbar .container,
    footer .container {
        max-width: var(--desktop-max-width-ultra) !important;
        padding-left: var(--desktop-padding-ultra) !important;
        padding-right: var(--desktop-padding-ultra) !important;
    }

    /* Сетки для ультра-широких экранов */
    .row,
    .features-grid-modern,
    .stats-grid-modern,
    .investments-grid,
    .packages-grid {
        max-width: var(--desktop-max-width-ultra) !important;
    }
}

/* ===== СОХРАНЕНИЕ СУЩЕСТВУЮЩИХ СТИЛЕЙ ===== */

/* Убеждаемся, что все цвета, шрифты, анимации остаются неизменными */
/* Этот файл ТОЛЬКО центрирует контент, не изменяя визуальный дизайн */

/* Сохраняем все hover эффекты */
.container:hover,
.row:hover {
    /* Наследуем все существующие hover эффекты */
}

/* Сохраняем все анимации */
@keyframes existing-animations {
    /* Все существующие анимации остаются без изменений */
}

/* ===== ДОПОЛНИТЕЛЬНЫЕ СТИЛИ ДЛЯ СПЕЦИФИЧНЫХ СТРАНИЦ ===== */

@media (min-width: 1200px) {

    /* ===== СТРАНИЦА ИНВЕСТИЦИЙ ===== */

    /* Основной контейнер страницы инвестиций */
    .invest-page .container,
    .investment-packages .container {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* Фильтры инвестиций */
    .filter-section .container,
    .filters-container {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* ===== СТРАНИЦА ДАШБОРДА ===== */

    /* Статистические карточки */
    .stats-grid-modern {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
        justify-content: center !important;
    }

    /* Графики и диаграммы */
    .dashboard-charts,
    .profit-chart-container {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* ===== СТРАНИЦА КАЛЬКУЛЯТОРА ===== */

    /* Контейнер калькулятора */
    .calculator-page .container {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* Результаты калькулятора */
    .calculation-results,
    .calculator-output {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* ===== СТРАНИЦА ЛИДЕРБОРДА ===== */

    /* Контейнер лидерборда */
    .leaderboard-page .container {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* Таблицы лидеров */
    .leaderboard-tables,
    .leaders-grid {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* ===== СТРАНИЦА ЗАДАНИЙ ===== */

    /* Контейнер заданий */
    .tasks-page .container {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* Сетка заданий */
    .tasks-grid,
    .achievements-grid {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* ===== СТРАНИЦА ПРОФИЛЯ ===== */

    /* Контейнер профиля */
    .profile-page .container {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* Формы профиля */
    .profile-forms,
    .profile-settings {
        max-width: 800px !important;
        margin: 0 auto !important;
    }

    /* ===== АДМИН ПАНЕЛЬ ===== */

    /* Контейнер админ панели */
    .admin-page .container,
    .admin-dashboard .container {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* Админ карточки */
    .admin-cards,
    .admin-stats {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* ===== СТРАНИЦА ДЕПОЗИТОВ ===== */

    /* Контейнер депозитов */
    .deposit-page .container {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* Формы депозитов */
    .deposit-forms,
    .payment-methods {
        max-width: 800px !important;
        margin: 0 auto !important;
    }

    /* ===== СТРАНИЦА КАРТЫ ===== */

    /* Контейнер карты */
    .map-page .container {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* Карта проектов */
    .projects-map,
    .map-wrapper {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* ===== СТРАНИЦА ОБРАЗОВАНИЯ ===== */

    /* Контейнер образования */
    .education-page .container {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* Статьи и категории */
    .education-content,
    .articles-grid {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* ===== СТРАНИЦА ПОДДЕРЖКИ ===== */

    /* Контейнер поддержки */
    .support-page .container {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* FAQ и тикеты */
    .support-content,
    .faq-section {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }
}

/* ===== ДОПОЛНИТЕЛЬНЫЕ УЛУЧШЕНИЯ ЦЕНТРИРОВАНИЯ ===== */

@media (min-width: 1200px) {

    /* ===== УНИВЕРСАЛЬНЫЕ ЗАГОЛОВКИ СТРАНИЦ ===== */

    .page-header,
    .welcome-header,
    .welcome-header-modern {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
        text-align: center !important;
    }

    /* ===== КАРТОЧКИ И КОМПОНЕНТЫ ===== */

    /* Убеждаемся, что карточки правильно размещаются в центрированных контейнерах */
    .card,
    .stats-card-modern,
    .feature-card-modern,
    .investment-card,
    .task-card,
    .achievement-card {
        /* Карточки наследуют центрирование от родительских контейнеров */
        margin-left: auto !important;
        margin-right: auto !important;
    }

    /* ===== СПЕЦИАЛЬНЫЕ СЕТКИ ===== */

    /* Сетки с фиксированным количеством колонок */
    .col-lg-6,
    .col-lg-4,
    .col-lg-3,
    .col-md-6,
    .col-md-4 {
        /* Колонки остаются как есть, центрируется только родительский row */
    }

    /* ===== ФОРМЫ И ИНПУТЫ ===== */

    /* Центрирование форм */
    .form-container,
    .auth-form,
    .contact-form {
        max-width: 600px !important;
        margin: 0 auto !important;
    }

    /* ===== ТАБЛИЦЫ И СПИСКИ ===== */

    /* Таблицы лидерборда */
    .leaderboard-table,
    .transactions-table,
    .investments-table {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* ===== ГРАФИКИ И ДИАГРАММЫ ===== */

    /* Контейнеры графиков */
    .chart-container,
    .graph-wrapper,
    .statistics-chart {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* ===== МЕДИА КОНТЕНТ ===== */

    /* Изображения и видео */
    .media-container,
    .image-gallery,
    .video-container {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
    }

    /* ===== СПЕЦИАЛЬНЫЕ КОМПОНЕНТЫ ===== */

    /* Алерты и уведомления */
    .alert,
    .notification,
    .message-container {
        max-width: var(--desktop-max-width) !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }

    /* Пагинация */
    .pagination-container,
    .pagination-wrapper {
        max-width: var(--desktop-max-width) !important;
        margin: 0 auto !important;
        text-align: center !important;
    }

    /* ===== ИСКЛЮЧЕНИЯ ===== */

    /* Элементы, которые НЕ должны центрироваться */
    .navbar,
    .navbar-nav,
    .dropdown-menu,
    .modal,
    .modal-dialog,
    .tooltip,
    .popover,
    .offcanvas {
        /* Эти элементы сохраняют свое оригинальное позиционирование */
        max-width: none !important;
        margin: initial !important;
    }

    /* Фиксированные элементы */
    .fixed-top,
    .fixed-bottom,
    .sticky-top {
        /* Фиксированные элементы не центрируются */
        max-width: none !important;
        margin: initial !important;
    }
}

/* ===== ТОНКАЯ НАСТРОЙКА ДЛЯ РАЗНЫХ РАЗРЕШЕНИЙ ===== */

/* Для экранов 1200-1399px */
@media (min-width: 1200px) and (max-width: 1399px) {
    .container,
    .container-fluid {
        max-width: 1200px !important;
        padding-left: 2rem !important;
        padding-right: 2rem !important;
    }
}

/* Для экранов 1400-1599px */
@media (min-width: 1400px) and (max-width: 1599px) {
    .container,
    .container-fluid {
        max-width: 1400px !important;
        padding-left: 2.5rem !important;
        padding-right: 2.5rem !important;
    }
}

/* Для экранов 1600px+ */
@media (min-width: 1600px) {
    .container,
    .container-fluid {
        max-width: 1600px !important;
        padding-left: 3rem !important;
        padding-right: 3rem !important;
    }
}

/* ===== ОТЛАДКА (можно удалить в продакшене) ===== */
/*
.container,
.container-fluid {
    outline: 1px dashed rgba(255, 0, 0, 0.3);
}
*/
