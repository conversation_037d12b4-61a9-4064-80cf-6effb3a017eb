<?php
$page_title = "Вывод средств";

// Проверка авторизации
if (!isLoggedIn()) {
    redirect('index.php?page=login&redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

$user = getCurrentUser();

// Получение настроек платежной системы
$min_withdrawal = getSetting('payment_min_withdrawal', 20);
$max_withdrawal = getSetting('payment_max_withdrawal', 5000);
$withdrawal_fee = getSetting('payment_withdrawal_fee', 2);
$daily_limit = getSetting('payment_daily_withdrawal_limit', 1000);
$system_enabled = getSetting('payment_system_enabled', 1);

// Получение суммы выводов за сегодня
try {
    $stmt = $conn->prepare("
        SELECT COALESCE(SUM(amount), 0) as today_total
        FROM withdrawal_requests
        WHERE user_id = ?
        AND DATE(created_at) = CURDATE()
        AND status IN ('pending', 'approved', 'completed')
    ");
    $stmt->execute([$user['id']]);
    $today_withdrawals = $stmt->fetch()['today_total'];
    $remaining_limit = $daily_limit - $today_withdrawals;
} catch (Exception $e) {
    $today_withdrawals = 0;
    $remaining_limit = $daily_limit;
}

// Проверка активных заявок
try {
    $stmt = $conn->prepare("
        SELECT COUNT(*) as active_count
        FROM withdrawal_requests
        WHERE user_id = ?
        AND status IN ('pending', 'approved')
    ");
    $stmt->execute([$user['id']]);
    $active_requests = $stmt->fetch()['active_count'];
} catch (Exception $e) {
    $active_requests = 0;
}

// Получение последних заявок пользователя
try {
    $stmt = $conn->prepare("
        SELECT * FROM withdrawal_requests
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$user['id']]);
    $recent_requests = $stmt->fetchAll();
} catch (Exception $e) {
    $recent_requests = [];
}
?>

<div class="container-fluid">
    <!-- Заголовок страницы -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="welcome-header animate-fadeInUp">
                <h2 class="welcome-title">
                    <i class="fas fa-minus-circle"></i> Вывод средств
                </h2>
                <p class="welcome-subtitle">Быстрый и безопасный вывод USDT TRC-20</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Основной контент -->
        <div class="col-lg-8">
            <div class="card animate-fadeInLeft">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-minus-circle me-2"></i>
                        Вывод средств USDT
                    </h4>
                </div>
                <div class="card-body">
                    <?php if (!$system_enabled): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Платежная система временно недоступна. Попробуйте позже.
                        </div>
                    <?php elseif ($active_requests > 0): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            У вас уже есть активная заявка на вывод. Дождитесь её обработки перед созданием новой.
                        </div>
                    <?php elseif ($user['balance'] < $min_withdrawal): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Недостаточно средств для вывода. Минимальная сумма: <?= $min_withdrawal ?> USDT
                        </div>
                    <?php else: ?>

                        <!-- Информация о выводе -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="stats-card animate-fadeInUp">
                                    <h6 class="text-primary">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Условия вывода
                                    </h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><strong>Валюта:</strong> USDT</li>
                                        <li><strong>Сеть:</strong> TRC-20 (TRON)</li>
                                        <li><strong>Мин. сумма:</strong> <?= $min_withdrawal ?> USDT</li>
                                        <li><strong>Макс. сумма:</strong> <?= $max_withdrawal ?> USDT</li>
                                        <li><strong>Комиссия:</strong> <?= $withdrawal_fee ?> USDT</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="stats-card animate-fadeInUp">
                                    <h6 class="text-warning">
                                        <i class="fas fa-clock me-2"></i>
                                        Лимиты и ограничения
                                    </h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><strong>Дневной лимит:</strong> <?= $daily_limit ?> USDT</li>
                                        <li><strong>Использовано сегодня:</strong> <?= number_format($today_withdrawals, 2) ?> USDT</li>
                                        <li><strong>Доступно:</strong> <?= number_format($remaining_limit, 2) ?> USDT</li>
                                        <li><strong>Время обработки:</strong> 1-24 часа</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Форма создания заявки -->
                        <form id="withdrawalForm" class="needs-validation" novalidate>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="amount" class="form-label">
                                            <i class="fas fa-dollar-sign me-1"></i>
                                            Сумма вывода (USDT)
                                        </label>
                                        <input type="number"
                                               class="form-control"
                                               id="amount"
                                               name="amount"
                                               min="<?= $min_withdrawal ?>"
                                               max="<?= min($max_withdrawal, $user['balance'], $remaining_limit) ?>"
                                               step="0.01"
                                               required>
                                        <div class="invalid-feedback">
                                            Введите сумму от <?= $min_withdrawal ?> до <?= min($max_withdrawal, $user['balance'], $remaining_limit) ?> USDT
                                        </div>
                                        <small class="text-muted">
                                            Комиссия <?= $withdrawal_fee ?> USDT будет вычтена из суммы
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="finalAmount" class="form-label">
                                            <i class="fas fa-calculator me-1"></i>
                                            К получению
                                        </label>
                                        <input type="text"
                                               class="form-control"
                                               id="finalAmount"
                                               readonly
                                               placeholder="0.00 USDT">
                                        <small class="text-muted">
                                            Сумма после вычета комиссии
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="walletAddress" class="form-label">
                                    <i class="fas fa-wallet me-1"></i>
                                    Адрес кошелька TRC-20
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="walletAddress"
                                       name="wallet_address"
                                       placeholder="Введите адрес USDT TRC-20 кошелька"
                                       pattern="^T[A-Za-z0-9]{33}$"
                                       required>
                                <div class="invalid-feedback">
                                    Введите корректный адрес TRC-20 кошелька (начинается с T, 34 символа)
                                </div>
                                <small class="text-muted">
                                    Убедитесь, что адрес поддерживает USDT в сети TRC-20
                                </small>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                                    <label class="form-check-label" for="agreeTerms">
                                        Я подтверждаю правильность указанного адреса кошелька и понимаю, что
                                        средства, отправленные на неверный адрес, не могут быть возвращены
                                    </label>
                                    <div class="invalid-feedback">
                                        Необходимо подтвердить правильность адреса
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="button" class="btn btn-secondary me-md-2" onclick="clearForm()">
                                    <i class="fas fa-times me-1"></i>
                                    Очистить
                                </button>
                                <button type="submit" class="btn btn-warning" id="submitBtn">
                                    <i class="fas fa-paper-plane me-1"></i>
                                    Создать заявку на вывод
                                </button>
                            </div>
                        </form>

                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Боковая панель -->
        <div class="col-lg-4">
            <!-- Текущий баланс -->
            <div class="card luxury-card mb-4">
                <div class="card-header bg-gradient-blue text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-balance-scale me-2"></i>
                        Доступный баланс
                    </h6>
                </div>
                <div class="card-body text-center">
                    <h3 class="text-success mb-0">
                        $<?= number_format($user['balance'], 2) ?>
                    </h3>
                    <small class="text-muted">USD</small>
                </div>
            </div>

            <!-- Калькулятор комиссии -->
            <div class="card luxury-card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        Калькулятор вывода
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Сумма вывода:</span>
                        <span id="calcAmount">0.00 USDT</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Комиссия:</span>
                        <span class="text-danger">-<?= $withdrawal_fee ?> USDT</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between fw-bold">
                        <span>К получению:</span>
                        <span class="text-success" id="calcFinal">0.00 USDT</span>
                    </div>
                </div>
            </div>

            <!-- Последние заявки -->
            <?php if (!empty($recent_requests)): ?>
            <div class="card luxury-card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Последние заявки
                    </h6>
                </div>
                <div class="card-body">
                    <?php foreach ($recent_requests as $request): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                            <div>
                                <small class="text-muted">
                                    <?= date('d.m.Y H:i', strtotime($request['created_at'])) ?>
                                </small>
                                <div class="fw-bold">
                                    $<?= number_format($request['amount'], 2) ?>
                                </div>
                                <small class="text-muted">
                                    К получению: $<?= number_format($request['final_amount'], 2) ?>
                                </small>
                            </div>
                            <div>
                                <?php
                                $status_class = [
                                    'pending' => 'warning',
                                    'approved' => 'info',
                                    'completed' => 'success',
                                    'rejected' => 'danger',
                                    'cancelled' => 'secondary'
                                ];
                                $status_text = [
                                    'pending' => 'Ожидает',
                                    'approved' => 'Одобрено',
                                    'completed' => 'Завершено',
                                    'rejected' => 'Отклонено',
                                    'cancelled' => 'Отменено'
                                ];
                                ?>
                                <span class="badge bg-<?= $status_class[$request['status']] ?>">
                                    <?= $status_text[$request['status']] ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    <div class="text-center mt-3">
                        <a href="index.php?page=payment-history" class="btn btn-sm btn-outline-primary">
                            Вся история
                        </a>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Модальное окно успеха -->
<div class="modal fade" id="successModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>
                    Заявка на вывод создана
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="mb-2">Ваша заявка на вывод успешно создана и отправлена на рассмотрение администратору.</p>
                <p class="mb-1"><strong>ID заявки:</strong> <span id="requestId"></span></p>
                <p class="mb-1"><strong>Сумма:</strong> <span id="requestAmount"></span> USDT</p>
                <p class="mb-0"><strong>К получению:</strong> <span id="requestFinal"></span> USDT</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-warning" data-bs-dismiss="modal">Понятно</button>
            </div>
        </div>
    </div>
</div>

<script>
const withdrawalFee = <?= $withdrawal_fee ?>;

// Функция расчета итоговой суммы
function calculateFinalAmount() {
    const amount = parseFloat(document.getElementById('amount').value) || 0;
    const finalAmount = Math.max(0, amount - withdrawalFee);

    document.getElementById('finalAmount').value = finalAmount.toFixed(2) + ' USDT';
    document.getElementById('calcAmount').textContent = amount.toFixed(2) + ' USDT';
    document.getElementById('calcFinal').textContent = finalAmount.toFixed(2) + ' USDT';
}

// Обновление расчета при изменении суммы
document.getElementById('amount').addEventListener('input', calculateFinalAmount);

// Функция очистки формы
function clearForm() {
    document.getElementById('withdrawalForm').reset();
    document.getElementById('withdrawalForm').classList.remove('was-validated');
    calculateFinalAmount();
}

// Обработка отправки формы
document.getElementById('withdrawalForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    if (!this.checkValidity()) {
        e.stopPropagation();
        this.classList.add('was-validated');
        return;
    }

    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;

    try {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Создание заявки...';

        const formData = new FormData(this);
        const data = Object.fromEntries(formData.entries());

        const response = await fetch('/api/withdrawal-request.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            document.getElementById('requestId').textContent = result.request_id;
            document.getElementById('requestAmount').textContent = result.amount;
            document.getElementById('requestFinal').textContent = result.final_amount;
            new bootstrap.Modal(document.getElementById('successModal')).show();
            clearForm();

            // Обновление страницы через 3 секунды для отображения новой заявки
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        } else {
            showNotification(result.message || 'Ошибка создания заявки', 'error');
        }

    } catch (error) {
        console.error('Error:', error);
        showNotification('Ошибка соединения с сервером', 'error');
    } finally {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
});

// Функция показа уведомлений
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Инициализация расчета при загрузке страницы
calculateFinalAmount();
</script>


