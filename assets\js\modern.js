/**
 * <PERSON><PERSON><PERSON><PERSON> - Современный JavaScript
 * Улучшенная функциональность для премиум дизайна
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // ===== ИНИЦИАЛИЗАЦИЯ =====
    initPreloader();
    initNavigation();
    initAnimations();
    initForms();
    initTooltips();
    initCounters();
    
    // ===== ПРЕЛОАДЕР =====
    function initPreloader() {
        const preloader = document.getElementById('preloader');
        if (preloader) {
            // Скрываем прелоадер при загрузке DOM
            function hidePreloader() {
                setTimeout(function() {
                    preloader.classList.add('hidden');
                    setTimeout(function() {
                        preloader.style.display = 'none';
                    }, 500);
                }, 1000);
            }

            // Скрываем при загрузке страницы
            if (document.readyState === 'complete') {
                hidePreloader();
            } else {
                window.addEventListener('load', hidePreloader);
            }

            // Принудительно скрываем через 3 секунды
            setTimeout(function() {
                if (preloader && preloader.style.display !== 'none') {
                    preloader.style.display = 'none';
                }
            }, 3000);
        }
    }
    
    // ===== НАВИГАЦИЯ =====
    function initNavigation() {
        // Мобильное меню
        const navbarToggler = document.querySelector('.navbar-toggler');
        const navbarCollapse = document.querySelector('.navbar-collapse');
        
        if (navbarToggler && navbarCollapse) {
            navbarToggler.addEventListener('click', function() {
                navbarCollapse.classList.toggle('show');
            });
            
            // Закрытие меню при клике вне его
            document.addEventListener('click', function(e) {
                if (!navbarToggler.contains(e.target) && !navbarCollapse.contains(e.target)) {
                    navbarCollapse.classList.remove('show');
                }
            });
        }
        
        // Плавная прокрутка для якорных ссылок
        const anchorLinks = document.querySelectorAll('a[href^="#"]');
        anchorLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }
    
    // ===== АНИМАЦИИ =====
    function initAnimations() {
        // Intersection Observer для анимаций при скролле
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fadeInUp');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);
        
        // Наблюдение за элементами
        const animatedElements = document.querySelectorAll('.card, .stat-item, .package-card');
        animatedElements.forEach(el => {
            observer.observe(el);
        });
    }
    
    // ===== ФОРМЫ =====
    function initForms() {
        // Валидация форм
        const forms = document.querySelectorAll('.needs-validation');
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                } else {
                    // Показываем прелоадер при отправке формы
                    const preloader = document.getElementById('preloader');
                    if (preloader) {
                        preloader.style.display = 'flex';
                        preloader.classList.remove('hidden');
                    }

                    // Отключаем кнопку отправки
                    const submitBtn = form.querySelector('[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        const originalText = submitBtn.textContent;
                        submitBtn.textContent = 'Обработка...';

                        // Восстанавливаем кнопку через 10 секунд на случай ошибки
                        setTimeout(() => {
                            submitBtn.disabled = false;
                            submitBtn.textContent = originalText;
                        }, 10000);
                    }
                }
                form.classList.add('was-validated');
            });
        });
        
        // Улучшенные input поля
        const formControls = document.querySelectorAll('.form-control');
        formControls.forEach(input => {
            // Автоматическое форматирование для числовых полей
            if (input.type === 'number' || input.classList.contains('currency')) {
                input.addEventListener('input', function() {
                    formatCurrency(this);
                });
            }
        });
    }
    
    // ===== ТУЛТИПЫ =====
    function initTooltips() {
        // Инициализация Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    // ===== СЧЕТЧИКИ =====
    function initCounters() {
        const counters = document.querySelectorAll('.stat-number, .counter');
        
        const counterObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounter(entry.target);
                    counterObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });
        
        counters.forEach(counter => {
            counterObserver.observe(counter);
        });
    }
    
    // ===== УТИЛИТЫ =====
    
    // Форматирование валюты
    function formatCurrency(input) {
        let value = input.value.replace(/[^\d]/g, '');
        if (value) {
            value = parseInt(value).toLocaleString('ru-RU');
            input.value = value;
        }
    }
    
    // Анимация счетчика
    function animateCounter(element) {
        const target = parseInt(element.dataset.target || element.textContent.replace(/[^\d]/g, ''));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(function() {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            if (element.dataset.currency) {
                element.textContent = '$' + Math.floor(current).toLocaleString();
            } else if (element.dataset.percentage) {
                element.textContent = Math.floor(current) + '%';
            } else {
                element.textContent = Math.floor(current).toLocaleString();
            }
        }, 16);
    }
    
    // ===== УВЕДОМЛЕНИЯ =====
    window.showNotification = function(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('animate-fadeInRight');
        }, 10);
        
        setTimeout(() => {
            notification.remove();
        }, duration);
    };
    
    // ===== ГЛОБАЛЬНЫЕ ФУНКЦИИ =====
    
    // Копирование в буфер обмена
    window.copyToClipboard = function(text) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('Скопировано в буфер обмена!', 'success', 2000);
        });
    };
    
    // Форматирование чисел
    window.formatNumber = function(num) {
        return new Intl.NumberFormat('ru-RU').format(num);
    };
    
    // Форматирование валюты
    window.formatCurrencyDisplay = function(amount, currency = 'USD') {
        return new Intl.NumberFormat('ru-RU', {
            style: 'currency',
            currency: currency
        }).format(amount);
    };
    
});

// ===== СТИЛИ ДЛЯ КАСТОМНЫХ КОМПОНЕНТОВ =====
const customStyles = `
    .custom-tooltip {
        position: absolute;
        background: var(--gray-900);
        color: var(--white);
        padding: 0.5rem 0.75rem;
        border-radius: var(--radius-lg);
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        z-index: 9999;
        opacity: 0;
        transform: translateY(5px);
        transition: var(--transition);
        pointer-events: none;
        white-space: nowrap;
        box-shadow: var(--shadow-lg);
    }
    
    .custom-tooltip.show {
        opacity: 1;
        transform: translateY(0);
    }
    
    .custom-tooltip::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 5px solid transparent;
        border-top-color: var(--gray-900);
    }
    
    .animate-fadeInRight {
        animation: fadeInRight 0.5s ease-out;
    }
    
    @keyframes fadeInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
`;

// Добавление стилей в head
const styleSheet = document.createElement('style');
styleSheet.textContent = customStyles;
document.head.appendChild(styleSheet);
