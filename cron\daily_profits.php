<?php
/**
 * Скрипт для автоматического начисления ежедневной прибыли
 * Должен запускаться через cron каждый день в 00:01
 * 
 * Cron команда: 1 0 * * * /usr/bin/php /path/to/project/cron/daily_profits.php
 */

// Проверяем, что скрипт запущен из командной строки
if (php_sapi_name() !== 'cli') {
    die('This script can only be run from command line');
}

require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/includes/functions.php';

// Логирование
$log_file = dirname(__DIR__) . '/logs/daily_profits.log';
$start_time = microtime(true);

log_message("=== Daily Profits Calculation Started ===");
log_message("Start time: " . date('Y-m-d H:i:s'));

try {
    // Вызываем хранимую процедуру для расчета прибыли
    $stmt = $conn->prepare("CALL CalculateDailyProfits()");
    $stmt->execute();
    
    log_message("Daily profits calculation completed successfully");
    
    // Получаем статистику обработанных инвестиций
    $stats = getDailyProfitStats();
    log_message("Processed investments: " . $stats['processed_count']);
    log_message("Total profit distributed: $" . number_format($stats['total_profit'], 2));
    log_message("Affected users: " . $stats['affected_users']);
    
    // Обновляем статистику завершенных инвестиций
    updateCompletedInvestments();
    
    // Обновляем прогресс заданий
    updateTaskProgress();
    
    // Отправляем уведомления о прибыли (если включено)
    if (getSetting('send_profit_notifications', false)) {
        sendProfitNotifications();
    }
    
    // Очищаем старые логи
    cleanupOldLogs();
    
    $execution_time = round(microtime(true) - $start_time, 2);
    log_message("Execution time: {$execution_time} seconds");
    log_message("=== Daily Profits Calculation Completed ===\n");
    
    echo "Daily profits calculation completed successfully\n";
    echo "Processed: {$stats['processed_count']} investments\n";
    echo "Total profit: $" . number_format($stats['total_profit'], 2) . "\n";
    echo "Execution time: {$execution_time} seconds\n";
    
} catch (Exception $e) {
    $error_message = "Error in daily profits calculation: " . $e->getMessage();
    log_message("ERROR: " . $error_message);
    
    // Отправляем уведомление администраторам об ошибке
    notifyAdminsError($error_message);
    
    echo "Error: " . $error_message . "\n";
    exit(1);
}

/**
 * Получение статистики обработанных инвестиций
 */
function getDailyProfitStats() {
    global $conn;
    
    // Статистика за сегодня
    $stmt = $conn->query("
        SELECT 
            COUNT(*) as processed_count,
            SUM(amount) as total_profit,
            COUNT(DISTINCT user_id) as affected_users
        FROM transactions 
        WHERE type = 'profit' 
        AND DATE(created_at) = CURDATE()
    ");
    
    return $stmt->fetch();
}

/**
 * Обновление завершенных инвестиций
 */
function updateCompletedInvestments() {
    global $conn;
    
    try {
        // Помечаем как завершенные инвестиции, у которых истек срок
        $stmt = $conn->prepare("
            UPDATE user_investments 
            SET status = 'completed' 
            WHERE status = 'active' 
            AND end_date IS NOT NULL 
            AND end_date < CURDATE()
        ");
        $stmt->execute();
        
        $completed_count = $stmt->rowCount();
        if ($completed_count > 0) {
            log_message("Marked {$completed_count} investments as completed");
        }
        
    } catch (Exception $e) {
        log_message("Error updating completed investments: " . $e->getMessage());
    }
}

/**
 * Обновление прогресса заданий
 */
function updateTaskProgress() {
    global $conn;
    
    try {
        // Обновляем прогресс ежедневных заданий
        $stmt = $conn->query("
            SELECT DISTINCT user_id 
            FROM transactions 
            WHERE type = 'profit' 
            AND DATE(created_at) = CURDATE()
        ");
        
        $users_with_profit = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($users_with_profit as $user_id) {
            // Проверяем задание на ежедневный вход
            updateUserTaskProgress($user_id, 'daily_login', 1);
        }
        
        log_message("Updated task progress for " . count($users_with_profit) . " users");
        
    } catch (Exception $e) {
        log_message("Error updating task progress: " . $e->getMessage());
    }
}

/**
 * Обновление прогресса конкретного задания пользователя
 */
function updateUserTaskProgress($user_id, $task_type, $increment = 1) {
    global $conn;
    
    try {
        // Получаем активные задания данного типа
        $stmt = $conn->prepare("
            SELECT id, target_value, reward_amount 
            FROM tasks 
            WHERE type = ? AND is_active = 1
        ");
        $stmt->execute([$task_type]);
        $tasks = $stmt->fetchAll();
        
        foreach ($tasks as $task) {
            // Проверяем текущий прогресс
            $stmt = $conn->prepare("
                SELECT id, current_value, is_completed 
                FROM user_task_progress 
                WHERE user_id = ? AND task_id = ?
            ");
            $stmt->execute([$user_id, $task['id']]);
            $progress = $stmt->fetch();
            
            if (!$progress) {
                // Создаем новую запись прогресса
                $stmt = $conn->prepare("
                    INSERT INTO user_task_progress (user_id, task_id, current_value) 
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$user_id, $task['id'], $increment]);
                $new_value = $increment;
            } else if (!$progress['is_completed']) {
                // Обновляем существующий прогресс
                $new_value = $progress['current_value'] + $increment;
                $stmt = $conn->prepare("
                    UPDATE user_task_progress 
                    SET current_value = ? 
                    WHERE id = ?
                ");
                $stmt->execute([$new_value, $progress['id']]);
            } else {
                continue; // Задание уже выполнено
            }
            
            // Проверяем, выполнено ли задание
            if ($new_value >= $task['target_value']) {
                completeTask($user_id, $task['id'], $task['reward_amount']);
            }
        }
        
    } catch (Exception $e) {
        log_message("Error updating user task progress: " . $e->getMessage());
    }
}

/**
 * Завершение задания и выдача награды
 */
function completeTask($user_id, $task_id, $reward_amount) {
    global $conn;
    
    try {
        $conn->beginTransaction();
        
        // Помечаем задание как выполненное
        $stmt = $conn->prepare("
            UPDATE user_task_progress 
            SET is_completed = 1, completed_at = NOW() 
            WHERE user_id = ? AND task_id = ?
        ");
        $stmt->execute([$user_id, $task_id]);
        
        // Получаем текущий баланс
        $stmt = $conn->prepare("SELECT balance FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $current_balance = $stmt->fetch()['balance'];
        
        // Обновляем баланс
        $new_balance = $current_balance + $reward_amount;
        $stmt = $conn->prepare("UPDATE users SET balance = ? WHERE id = ?");
        $stmt->execute([$new_balance, $user_id]);
        
        // Создаем транзакцию
        $stmt = $conn->prepare("
            INSERT INTO transactions (user_id, type, amount, balance_before, balance_after, description, reference_id, reference_type) 
            VALUES (?, 'task_reward', ?, ?, ?, ?, ?, 'task')
        ");
        $stmt->execute([
            $user_id, 
            $reward_amount, 
            $current_balance, 
            $new_balance, 
            'Награда за выполнение задания', 
            $task_id
        ]);
        
        // Создаем уведомление
        $stmt = $conn->prepare("
            INSERT INTO notifications (user_id, title, message, type) 
            VALUES (?, ?, ?, 'success')
        ");
        $stmt->execute([
            $user_id,
            'Задание выполнено!',
            'Вы получили награду: $' . number_format($reward_amount, 2)
        ]);
        
        $conn->commit();
        log_message("Task completed for user {$user_id}, reward: $" . number_format($reward_amount, 2));
        
    } catch (Exception $e) {
        $conn->rollBack();
        log_message("Error completing task: " . $e->getMessage());
    }
}

/**
 * Отправка уведомлений о прибыли
 */
function sendProfitNotifications() {
    global $conn;
    
    try {
        // Получаем пользователей, которые получили прибыль сегодня
        $stmt = $conn->query("
            SELECT 
                t.user_id,
                u.email,
                u.first_name,
                SUM(t.amount) as daily_profit
            FROM transactions t
            JOIN users u ON t.user_id = u.id
            WHERE t.type = 'profit' 
            AND DATE(t.created_at) = CURDATE()
            AND u.status = 'active'
            GROUP BY t.user_id
        ");
        
        $users = $stmt->fetchAll();
        $sent_count = 0;
        
        foreach ($users as $user) {
            if (sendProfitEmail($user)) {
                $sent_count++;
            }
        }
        
        log_message("Sent profit notifications to {$sent_count} users");
        
    } catch (Exception $e) {
        log_message("Error sending profit notifications: " . $e->getMessage());
    }
}

/**
 * Отправка email о прибыли
 */
function sendProfitEmail($user) {
    // Здесь должна быть реализация отправки email
    // Пока просто логируем
    log_message("Would send profit email to {$user['email']}: $" . number_format($user['daily_profit'], 2));
    return true;
}

/**
 * Уведомление администраторов об ошибке
 */
function notifyAdminsError($error_message) {
    global $conn;
    
    try {
        $stmt = $conn->query("SELECT id FROM users WHERE role = 'admin' AND status = 'active'");
        $admins = $stmt->fetchAll();
        
        foreach ($admins as $admin) {
            $stmt = $conn->prepare("
                INSERT INTO notifications (user_id, title, message, type) 
                VALUES (?, ?, ?, 'error')
            ");
            $stmt->execute([
                $admin['id'],
                'Ошибка в начислении прибыли',
                $error_message
            ]);
        }
        
    } catch (Exception $e) {
        log_message("Error notifying admins: " . $e->getMessage());
    }
}

/**
 * Очистка старых логов
 */
function cleanupOldLogs() {
    $log_dir = dirname(__DIR__) . '/logs/';
    $files = glob($log_dir . '*.log');
    
    foreach ($files as $file) {
        if (filemtime($file) < strtotime('-30 days')) {
            unlink($file);
        }
    }
}

/**
 * Получение настройки
 */
function getSetting($key, $default = null) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();
        
        return $result ? $result['setting_value'] : $default;
        
    } catch (Exception $e) {
        return $default;
    }
}

/**
 * Логирование сообщений
 */
function log_message($message) {
    global $log_file;
    
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] {$message}\n";
    
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    
    // Также выводим в консоль при запуске из CLI
    if (php_sapi_name() === 'cli') {
        echo $log_entry;
    }
}
?>
