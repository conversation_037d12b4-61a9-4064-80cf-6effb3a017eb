<?php
require_once '../config/config.php';
require_once '../config/session_config.php';
session_start();
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Проверка авторизации
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Необходима авторизация']);
    exit;
}

// Проверка метода запроса
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Метод не разрешен']);
    exit;
}

// Получение ID пакета
$package_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($package_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Некорректный ID пакета']);
    exit;
}

try {
    // Получение данных пакета
    $stmt = $conn->prepare("
        SELECT id, name, daily_rate, min_amount, max_amount, type, duration_days, description
        FROM investment_packages 
        WHERE id = ? AND is_active = 1
    ");
    $stmt->execute([$package_id]);
    $package = $stmt->fetch();

    if (!$package) {
        echo json_encode(['success' => false, 'message' => 'Пакет не найден']);
        exit;
    }

    // Возвращаем данные пакета
    echo json_encode([
        'success' => true,
        'package' => [
            'id' => $package['id'],
            'name' => $package['name'],
            'daily_rate' => $package['daily_rate'],
            'min_amount' => $package['min_amount'],
            'max_amount' => $package['max_amount'],
            'type' => $package['type'],
            'duration_days' => $package['duration_days'],
            'description' => $package['description']
        ]
    ]);

} catch (Exception $e) {
    error_log("Error in get-package.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Произошла ошибка при получении данных пакета']);
}
?>
