<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест расширенных контейнеров - Green<PERSON>hain EcoFund</title>
    
    <!-- CSS в том же порядке, что и на сайте -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/eco-achievements-preserved.css">
    <link rel="stylesheet" href="assets/css/light-sections-contrast.css">
    <link rel="stylesheet" href="assets/css/unified-sections.css">
    <link rel="stylesheet" href="assets/css/investment-cards-styling.css">
    <link rel="stylesheet" href="assets/css/text-alignment-fixes.css">
    <link rel="stylesheet" href="assets/css/modern-navigation.css">
    <link rel="stylesheet" href="assets/css/section-separation.css">
    <link rel="stylesheet" href="assets/css/layout-alignment-improvements.css">
    
    <style>
        /* Визуальные индикаторы для проверки расширенных контейнеров */
        .container-test {
            position: relative;
        }
        
        .container-test::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 2px dashed rgba(255, 215, 0, 0.7);
            pointer-events: none;
            z-index: 1000;
        }
        
        .width-indicator {
            position: fixed;
            top: 60px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            z-index: 9999;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .container-info {
            position: fixed;
            top: 60px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            z-index: 9999;
            max-width: 350px;
            font-size: 0.9rem;
        }
        
        .success {
            color: #22c55e;
        }
        
        .info {
            color: #3b82f6;
        }
        
        .warning {
            color: #f59e0b;
        }
        
        /* Демонстрация использования пространства */
        .space-demo {
            background: linear-gradient(90deg, 
                rgba(255, 255, 255, 0.1) 0%, 
                rgba(255, 255, 255, 0.05) 50%, 
                rgba(255, 255, 255, 0.1) 100%);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <!-- SVG Icons -->
    <svg style="display: none;">
        <defs>
            <symbol id="icon-eco-leaf" viewBox="0 0 24 24">
                <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </symbol>
            <symbol id="icon-energy" viewBox="0 0 24 24">
                <path fill="currentColor" d="M13 2.05v3.03c3.39.49 6 3.39 6 6.92 0 .9-.18 1.75-.48 2.54l2.6 1.53c.56-1.24.88-2.62.88-4.07 0-5.18-3.95-9.45-9-9.95zM12 19c-3.87 0-7-3.13-7-7 0-3.53 2.61-6.43 6-6.92V2.05c-5.06.5-9 4.76-9 9.95 0 5.52 4.47 10 9.99 10 3.31 0 6.24-1.61 8.06-4.09l-2.6-1.53C16.17 17.98 14.21 19 12 19z"/>
            </symbol>
            <symbol id="icon-dashboard" viewBox="0 0 24 24">
                <path fill="currentColor" d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
            </symbol>
        </defs>
    </svg>

    <!-- Width Indicator -->
    <div class="width-indicator">
        <div>Ширина экрана: <span id="screen-width">0</span>px</div>
        <div>Ширина контейнера: <span id="container-width">0</span>px</div>
        <div>Использование: <span id="usage-percent">0</span>%</div>
    </div>

    <!-- Container Info -->
    <div class="container-info">
        <h5 style="margin-bottom: 1rem; color: #22c55e;">📏 Расширенные контейнеры:</h5>
        <ul style="margin: 0; padding-left: 1rem; list-style: none;">
            <li class="success">✅ >1600px: Контейнер 1600px</li>
            <li class="success">✅ 1400-1599px: Контейнер 1400px</li>
            <li class="info">ℹ️ 1200-1399px: Контейнер 1200px</li>
            <li class="warning">⚠️ <1200px: Адаптивная ширина</li>
        </ul>
        <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #374151;">
            <small style="color: #9ca3af;">
                Желтые рамки показывают границы контейнеров.<br>
                Измените размер окна для тестирования.
            </small>
        </div>
    </div>

    <!-- Header -->
    <header class="modern-header">
        <nav class="modern-navbar">
            <div class="container container-test">
                <div class="d-flex align-items-center justify-content-between w-100">
                    <a class="modern-brand" href="#">
                        <div class="brand-logo-modern">🌱</div>
                        <span class="brand-text-modern text-brand-gold">GreenChain EcoFund</span>
                    </a>
                    <div class="d-flex align-items-center gap-2">
                        <a href="#" class="nav-button">Расширенные контейнеры</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="main-content">
        <!-- Hero Section Test -->
        <section class="modern-hero">
            <div class="container container-test">
                <div class="space-demo">
                    <h4 style="color: white; margin-bottom: 1rem;">🚀 Демонстрация использования пространства</h4>
                    <p style="color: #e2e8f0; margin-bottom: 0;">
                        Этот блок показывает, как расширенные контейнеры используют доступное пространство экрана.
                        На широких экранах (>1400px) контейнеры автоматически расширяются для лучшего использования пространства.
                    </p>
                </div>
                
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="hero-content-modern">
                            <div class="hero-badge-modern">
                                📏 Тест расширенных контейнеров 🚀
                            </div>
                            <h1 class="hero-title-modern">
                                Расширенные <br>
                                <span class="hero-title-gradient">контейнеры</span><br>
                                <span class="text-brand-gold">GreenChain EcoFund</span>
                            </h1>
                            <p class="hero-subtitle-modern">
                                Новые расширенные контейнеры обеспечивают лучшее использование пространства экрана 
                                на широких мониторах, сохраняя при этом идеальное выравнивание и центрирование.
                            </p>

                            <div class="hero-stats-modern">
                                <div class="stat-card-modern">
                                    <div class="stat-number-modern">1600px</div>
                                    <div class="stat-label-modern">Макс. ширина</div>
                                </div>
                                <div class="stat-card-modern">
                                    <div class="stat-number-modern">1400px</div>
                                    <div class="stat-label-modern">Стандарт</div>
                                </div>
                                <div class="stat-card-modern">
                                    <div class="stat-number-modern">100%</div>
                                    <div class="stat-label-modern">Адаптивность</div>
                                </div>
                            </div>

                            <div class="hero-buttons-modern">
                                <a href="#" class="btn-hero-primary">
                                    <svg class="nav-icon me-2"><use href="#icon-eco-leaf"></use></svg>
                                    Тест широких экранов
                                </a>
                                <a href="#" class="btn-hero-secondary">
                                    <svg class="nav-icon me-2"><use href="#icon-dashboard"></use></svg>
                                    Проверить адаптивность
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="hero-image-modern">
                            <div class="feature-card-modern">
                                <div class="feature-icon-modern">
                                    <svg class="nav-icon"><use href="#icon-eco-leaf"></use></svg>
                                </div>
                                <h3 class="feature-title-modern">📏 Адаптивные размеры</h3>
                                <p class="feature-description-modern">
                                    Контейнеры автоматически адаптируются к размеру экрана для оптимального использования пространства.
                                </p>
                                <div class="space-demo">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <span style="color: white; font-weight: bold;">Эффективность:</span>
                                        <span style="color: #22c55e; font-weight: bold; font-size: 1.1rem;">Максимальная</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section Test -->
        <section class="content-section-modern section-green-bg">
            <div class="container container-test">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="section-title-modern section-title-eco">
                            🌍 Тест расширенных сеток
                        </h2>
                        <p class="section-subtitle-modern">
                            Демонстрация того, как расширенные контейнеры улучшают распределение карточек в сетке
                        </p>
                    </div>
                </div>

                <div class="features-grid-modern">
                    <div class="feature-card-modern">
                        <div class="feature-icon-modern">
                            <svg class="nav-icon"><use href="#icon-eco-leaf"></use></svg>
                        </div>
                        <h3 class="feature-title-modern">Больше пространства</h3>
                        <p class="feature-description-modern">
                            На широких экранах карточки получают больше пространства для контента.
                        </p>
                    </div>

                    <div class="feature-card-modern">
                        <div class="feature-icon-modern">
                            <svg class="nav-icon"><use href="#icon-energy"></use></svg>
                        </div>
                        <h3 class="feature-title-modern">Лучшее выравнивание</h3>
                        <p class="feature-description-modern">
                            Расширенные контейнеры обеспечивают более равномерное распределение элементов.
                        </p>
                    </div>

                    <div class="feature-card-modern">
                        <div class="feature-icon-modern">
                            <svg class="nav-icon"><use href="#icon-dashboard"></use></svg>
                        </div>
                        <h3 class="feature-title-modern">Адаптивность</h3>
                        <p class="feature-description-modern">
                            Автоматическая адаптация к размеру экрана без потери функциональности.
                        </p>
                    </div>

                    <div class="feature-card-modern">
                        <div class="feature-icon-modern">
                            <svg class="nav-icon"><use href="#icon-eco-leaf"></use></svg>
                        </div>
                        <h3 class="feature-title-modern">Четвертая карточка</h3>
                        <p class="feature-description-modern">
                            На очень широких экранах может поместиться больше карточек в одном ряду.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Investment Packages Test -->
        <section class="content-section-modern">
            <div class="container container-test">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="section-title-modern">
                            💎 Расширенные инвестиционные пакеты
                        </h2>
                        <p class="section-subtitle-modern">
                            Демонстрация улучшенного использования пространства для больших карточек
                        </p>
                    </div>
                </div>

                <div class="row package-grid">
                    <div class="col-lg-6 mb-4">
                        <div class="luxury-investment-card h-100">
                            <div class="text-center mb-3">
                                <div class="feature-icon-modern mb-3">
                                    <svg class="nav-icon"><use href="#icon-eco-leaf"></use></svg>
                                </div>
                                <h4 class="luxury-investment-title">Расширенный пакет 1</h4>
                                <div class="luxury-investment-rate">1.2%</div>
                                <div class="luxury-investment-period">в день</div>
                            </div>
                            <div class="card-body">
                                <div class="space-demo">
                                    <p style="margin: 0; color: white;">
                                        Больше пространства для описания инвестиционных возможностей 
                                        и детальной информации о пакете.
                                    </p>
                                </div>
                            </div>
                            <div class="text-center mt-4">
                                <button class="btn-luxury-primary">
                                    <i class="fas fa-chart-line"></i> Тест расширенного пакета
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6 mb-4">
                        <div class="luxury-investment-card h-100">
                            <div class="text-center mb-3">
                                <div class="feature-icon-modern mb-3">
                                    <svg class="nav-icon"><use href="#icon-energy"></use></svg>
                                </div>
                                <h4 class="luxury-investment-title">Расширенный пакет 2</h4>
                                <div class="luxury-investment-rate">1.8%</div>
                                <div class="luxury-investment-period">в день</div>
                            </div>
                            <div class="card-body">
                                <div class="space-demo">
                                    <p style="margin: 0; color: white;">
                                        Улучшенное использование пространства позволяет разместить 
                                        больше информации без ущерба для дизайна.
                                    </p>
                                </div>
                            </div>
                            <div class="text-center mt-4">
                                <button class="btn-luxury-primary">
                                    <i class="fas fa-chart-line"></i> Тест расширенного пакета
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        // Отслеживание размеров экрана и контейнера
        function updateSizeInfo() {
            const screenWidth = window.innerWidth;
            const container = document.querySelector('.container');
            const containerWidth = container ? container.offsetWidth : 0;
            const usagePercent = screenWidth > 0 ? Math.round((containerWidth / screenWidth) * 100) : 0;
            
            document.getElementById('screen-width').textContent = screenWidth;
            document.getElementById('container-width').textContent = containerWidth;
            document.getElementById('usage-percent').textContent = usagePercent;
        }
        
        // Обновление при загрузке и изменении размера
        window.addEventListener('load', updateSizeInfo);
        window.addEventListener('resize', updateSizeInfo);
        
        // Обновление каждую секунду для точности
        setInterval(updateSizeInfo, 1000);
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
