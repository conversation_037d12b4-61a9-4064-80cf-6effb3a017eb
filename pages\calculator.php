<?php
$page_title = "Калькулятор доходности";

// Получение доступных пакетов
$packages = getInvestmentPackages();
?>

<div class="container">
    <!-- Заголовок страницы -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="welcome-header animate-fadeInUp">
                <h2 class="welcome-title">
                    <i class="fas fa-calculator"></i> Калькулятор доходности
                </h2>
                <p class="welcome-subtitle">Рассчитайте потенциальную прибыль от ваших инвестиций</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Калькулятор -->
        <div class="col-lg-6">
            <div class="card animate-fadeInLeft">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line"></i> Расчет прибыли
                    </h5>
                </div>
                <div class="card-body">
                    <form id="calculatorForm">
                        <div class="form-group">
                            <label for="package_select" class="form-label">
                                <i class="fas fa-box me-2"></i>Инвестиционный пакет
                            </label>
                            <select class="form-control form-select" id="package_select" onchange="updateCalculation()">
                                <option value="">Выберите пакет</option>
                                <?php foreach ($packages as $package): ?>
                                    <option value="<?php echo $package['id']; ?>"
                                            data-rate="<?php echo $package['daily_rate']; ?>"
                                            data-type="<?php echo $package['type']; ?>"
                                            data-duration="<?php echo $package['duration_days']; ?>"
                                            data-min="<?php echo $package['min_amount']; ?>"
                                            data-max="<?php echo $package['max_amount']; ?>">
                                        <?php echo htmlspecialchars($package['name']); ?>
                                        (<?php echo formatPercent($package['daily_rate']; ?>/день)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="investment_amount" class="form-label">Сумма инвестиции</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control" id="investment_amount" 
                                       min="10" step="1" value="1000" onchange="updateCalculation()">
                            </div>
                            <div class="form-text" id="amount_limits"></div>
                        </div>
                        
                        <!-- Быстрые суммы -->
                        <div class="quick-amounts mb-3">
                            <label class="form-label">Быстрый выбор:</label>
                            <div class="amount-buttons">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setAmount(100)">$100</button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setAmount(500)">$500</button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setAmount(1000)">$1,000</button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setAmount(5000)">$5,000</button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setAmount(10000)">$10,000</button>
                            </div>
                        </div>
                        
                        <div class="mb-3" id="duration_section" style="display: none;">
                            <label for="investment_duration" class="form-label">Период инвестирования (дней)</label>
                            <input type="number" class="form-control" id="investment_duration" 
                                   min="1" max="365" value="30" onchange="updateCalculation()">
                            <div class="form-text">Для гибких пакетов вы можете выбрать любой период</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="compound_interest" onchange="updateCalculation()">
                                <label class="form-check-label" for="compound_interest">
                                    Реинвестирование прибыли (сложный процент)
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Результаты -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar"></i> Результаты расчета
                    </h5>
                </div>
                <div class="card-body">
                    <div id="calculation_results">
                        <div class="result-placeholder">
                            <i class="fas fa-calculator fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Выберите пакет и сумму для расчета</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- График доходности -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-area"></i> График доходности
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="profitChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Сравнение пакетов -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-balance-scale"></i> Сравнение пакетов
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Пакет</th>
                                    <th>Доходность</th>
                                    <th>Тип</th>
                                    <th>Минимум</th>
                                    <th>Максимум</th>
                                    <th>Прибыль за 30 дней*</th>
                                    <th>Прибыль за год*</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($packages as $package): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($package['name']); ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-success"><?php echo formatPercent($package['daily_rate']); ?>/день</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $package['type'] === 'fixed' ? 'primary' : 'secondary'; ?>">
                                                <?php echo $package['type'] === 'fixed' ? 'Фиксированный' : 'Гибкий'; ?>
                                            </span>
                                        </td>
                                        <td><?php echo formatMoney($package['min_amount']); ?></td>
                                        <td><?php echo $package['max_amount'] ? formatMoney($package['max_amount']) : 'Без лимита'; ?></td>
                                        <td class="text-success">
                                            <?php echo formatMoney(1000 * ($package['daily_rate'] / 100) * 30); ?>
                                        </td>
                                        <td class="text-success">
                                            <?php echo formatMoney(1000 * ($package['daily_rate'] / 100) * 365); ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        <small class="text-muted">* Расчет для инвестиции $1,000 без реинвестирования</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* ===== КАЛЬКУЛЯТОР - ПРЕМИАЛЬНАЯ ТЕМНАЯ СХЕМА ===== */

.amount-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.result-placeholder {
    text-align: center;
    padding: 2rem;
}

.result-placeholder .text-muted {
    color: var(--text-light-gray) !important;
}

.calculation-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.calc-item {
    background: linear-gradient(145deg, var(--dark-green-medium), var(--dark-green)) !important;
    border: 1px solid var(--gold-primary) !important;
    padding: 1rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.calc-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.2);
}

.calc-label {
    font-size: 0.9rem;
    color: var(--text-light-gray) !important;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.calc-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gold-primary) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.calc-value.profit {
    color: var(--gold-bright) !important;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3), 0 0 10px rgba(255, 215, 0, 0.3); }
    to { text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 215, 0, 0.6); }
}

.calc-summary {
    background: linear-gradient(135deg, var(--dark-green), var(--dark-navy)) !important;
    border: 2px solid var(--gold-primary) !important;
    color: var(--text-white) !important;
    padding: 1.5rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4), 0 0 20px rgba(212, 175, 55, 0.2);
    position: relative;
    overflow: hidden;
}

.calc-summary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--gold-primary), var(--gold-bright), var(--gold-primary));
}

.summary-title {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: var(--text-light-gray) !important;
    font-weight: 600;
}

.summary-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gold-bright) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.roi-badge {
    background: rgba(212, 175, 55, 0.2) !important;
    border: 1px solid var(--gold-primary) !important;
    color: var(--gold-bright) !important;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.9rem;
    margin-top: 0.5rem;
    display: inline-block;
    font-weight: 600;
}

/* Исправления для текста в результатах */
#calculation_results .text-muted {
    color: var(--text-light-gray) !important;
}

#calculation_results .text-success {
    color: var(--gold-bright) !important;
    font-weight: 600;
}

#calculation_results strong {
    color: var(--gold-primary) !important;
}

#calculation_results small {
    color: var(--text-medium-gray) !important;
}

/* Стили для таблицы сравнения */
.table {
    color: var(--text-white) !important;
}

.table th {
    color: var(--gold-primary) !important;
    font-weight: 600;
    border-bottom: 2px solid var(--gold-primary) !important;
}

.table td {
    color: var(--text-light-gray) !important;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2) !important;
}

.table td strong {
    color: var(--text-white) !important;
}

.table .badge {
    color: var(--text-white) !important;
}

.table .text-success {
    color: var(--gold-bright) !important;
}

.table-responsive .text-muted {
    color: var(--text-medium-gray) !important;
}

@media (max-width: 768px) {
    .calculation-grid {
        grid-template-columns: 1fr;
    }

    .amount-buttons {
        justify-content: center;
    }

    .calc-item {
        padding: 0.75rem;
    }

    .calc-value {
        font-size: 1.25rem;
    }

    .summary-value {
        font-size: 1.75rem;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
let chart;
let currentPackage = null;

document.addEventListener('DOMContentLoaded', function() {
    // Инициализация с первым пакетом
    if (document.getElementById('package_select').options.length > 1) {
        document.getElementById('package_select').selectedIndex = 1;
        updateCalculation();
    }
});

function setAmount(amount) {
    document.getElementById('investment_amount').value = amount;
    updateCalculation();
}

function updateCalculation() {
    const packageSelect = document.getElementById('package_select');
    const selectedOption = packageSelect.options[packageSelect.selectedIndex];
    
    if (!selectedOption.value) {
        showPlaceholder();
        return;
    }
    
    currentPackage = {
        id: selectedOption.value,
        rate: parseFloat(selectedOption.dataset.rate),
        type: selectedOption.dataset.type,
        duration: parseInt(selectedOption.dataset.duration) || null,
        min: parseFloat(selectedOption.dataset.min),
        max: parseFloat(selectedOption.dataset.max) || null
    };
    
    const amount = parseFloat(document.getElementById('investment_amount').value) || 0;
    const compound = document.getElementById('compound_interest').checked;
    
    // Обновляем лимиты
    updateAmountLimits();
    
    // Показываем/скрываем поле длительности
    updateDurationSection();
    
    if (amount > 0) {
        const results = calculateProfit(amount, compound);
        displayResults(results);
        updateChart(results);
    } else {
        showPlaceholder();
    }
}

function updateAmountLimits() {
    const limitsText = document.getElementById('amount_limits');
    if (currentPackage) {
        let text = `Минимум: $${currentPackage.min.toLocaleString()}`;
        if (currentPackage.max) {
            text += `, максимум: $${currentPackage.max.toLocaleString()}`;
        }
        limitsText.textContent = text;
    }
}

function updateDurationSection() {
    const durationSection = document.getElementById('duration_section');
    const durationInput = document.getElementById('investment_duration');
    
    if (currentPackage.type === 'flexible') {
        durationSection.style.display = 'block';
    } else {
        durationSection.style.display = 'none';
        if (currentPackage.duration) {
            durationInput.value = currentPackage.duration;
        }
    }
}

function calculateProfit(amount, compound) {
    const dailyRate = currentPackage.rate / 100;
    const duration = currentPackage.type === 'flexible' 
        ? parseInt(document.getElementById('investment_duration').value) 
        : (currentPackage.duration || 30);
    
    let results = {
        amount: amount,
        dailyRate: currentPackage.rate,
        duration: duration,
        compound: compound,
        daily: [],
        totals: {}
    };
    
    let currentAmount = amount;
    let totalProfit = 0;
    
    for (let day = 1; day <= duration; day++) {
        const dailyProfit = currentAmount * dailyRate;
        totalProfit += dailyProfit;
        
        if (compound) {
            currentAmount += dailyProfit;
        }
        
        results.daily.push({
            day: day,
            profit: dailyProfit,
            totalProfit: totalProfit,
            balance: compound ? currentAmount : amount + totalProfit
        });
    }
    
    // Расчет итогов
    results.totals = {
        totalProfit: totalProfit,
        finalBalance: compound ? currentAmount : amount + totalProfit,
        roi: (totalProfit / amount) * 100,
        dailyAverage: totalProfit / duration,
        weeklyProfit: (totalProfit / duration) * 7,
        monthlyProfit: (totalProfit / duration) * 30,
        yearlyProfit: (totalProfit / duration) * 365
    };
    
    return results;
}

function displayResults(results) {
    const container = document.getElementById('calculation_results');

    container.innerHTML = `
        <div class="calculation-grid">
            <div class="calc-item">
                <div class="calc-label">Начальная сумма</div>
                <div class="calc-value">$${results.amount.toLocaleString()}</div>
            </div>
            <div class="calc-item">
                <div class="calc-label">Период</div>
                <div class="calc-value">${results.duration} дней</div>
            </div>
            <div class="calc-item">
                <div class="calc-label">Ежедневная прибыль</div>
                <div class="calc-value profit">$${results.totals.dailyAverage.toFixed(2)}</div>
            </div>
            <div class="calc-item">
                <div class="calc-label">Общая прибыль</div>
                <div class="calc-value profit">$${results.totals.totalProfit.toFixed(2)}</div>
            </div>
        </div>

        <div class="calc-summary">
            <div class="summary-title">Итоговая сумма</div>
            <div class="summary-value">$${results.totals.finalBalance.toLocaleString()}</div>
            <div class="roi-badge">ROI: ${results.totals.roi.toFixed(1)}%</div>
        </div>

        <div class="mt-3">
            <h6 style="color: var(--gold-primary) !important; font-weight: 600;">Прогнозируемая прибыль:</h6>
            <div class="row">
                <div class="col-6">
                    <small style="color: var(--text-medium-gray) !important;">За неделю:</small><br>
                    <strong style="color: var(--gold-bright) !important; font-weight: 600;">$${results.totals.weeklyProfit.toFixed(2)}</strong>
                </div>
                <div class="col-6">
                    <small style="color: var(--text-medium-gray) !important;">За месяц:</small><br>
                    <strong style="color: var(--gold-bright) !important; font-weight: 600;">$${results.totals.monthlyProfit.toFixed(2)}</strong>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-12">
                    <small style="color: var(--text-medium-gray) !important;">За год:</small><br>
                    <strong style="color: var(--gold-bright) !important; font-weight: 600;">$${results.totals.yearlyProfit.toLocaleString()}</strong>
                </div>
            </div>
        </div>

        ${results.compound ? '<div class="alert alert-info mt-3" style="background: linear-gradient(135deg, var(--dark-navy), var(--dark-green)) !important; border: 1px solid var(--gold-primary) !important; color: var(--text-white) !important;"><i class="fas fa-info-circle" style="color: var(--gold-primary) !important;"></i> Расчет с реинвестированием прибыли (сложный процент)</div>' : ''}
    `;
}

function updateChart(results) {
    const ctx = document.getElementById('profitChart').getContext('2d');
    
    if (chart) {
        chart.destroy();
    }
    
    const labels = results.daily.map(d => `День ${d.day}`);
    const balanceData = results.daily.map(d => d.balance);
    const profitData = results.daily.map(d => d.totalProfit);
    
    chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Общий баланс',
                    data: balanceData,
                    borderColor: '#d4af37',
                    backgroundColor: 'rgba(212, 175, 55, 0.1)',
                    fill: true,
                    tension: 0.4,
                    borderWidth: 3
                },
                {
                    label: 'Накопленная прибыль',
                    data: profitData,
                    borderColor: '#ffd700',
                    backgroundColor: 'rgba(255, 215, 0, 0.1)',
                    fill: true,
                    tension: 0.4,
                    borderWidth: 3
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        color: '#ffffff',
                        font: {
                            weight: 600
                        }
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: '#e2e8f0'
                    },
                    grid: {
                        color: 'rgba(212, 175, 55, 0.2)'
                    }
                },
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: '#e2e8f0',
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    },
                    grid: {
                        color: 'rgba(212, 175, 55, 0.2)'
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

function showPlaceholder() {
    document.getElementById('calculation_results').innerHTML = `
        <div class="result-placeholder">
            <i class="fas fa-calculator fa-3x mb-3" style="color: var(--text-medium-gray) !important;"></i>
            <p style="color: var(--text-light-gray) !important; font-weight: 500;">Выберите пакет и сумму для расчета</p>
        </div>
    `;

    if (chart) {
        chart.destroy();
        chart = null;
    }
}
</script>


