<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Комплексное тестирование сайта</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-console {
            background: #000;
            color: #0f0;
            font-family: monospace;
            padding: 15px;
            height: 500px;
            overflow-y: auto;
            border-radius: 5px;
            font-size: 12px;
        }
        .error { color: #f00; }
        .warn { color: #ff0; }
        .success { color: #0f0; }
        .info { color: #00f; }
        .test-item {
            margin: 5px 0;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .test-passed { background: #d4edda; border-color: #c3e6cb; }
        .test-failed { background: #f8d7da; border-color: #f5c6cb; }
        .test-pending { background: #fff3cd; border-color: #ffeaa7; }
    </style>
</head>
<body>
    <div class="container-fluid mt-3">
        <h2>🧪 Комплексное тестирование GreenChain EcoFund</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>🎯 Результаты тестов</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-results"></div>
                        <button onclick="runAllTests()" class="btn btn-primary mt-3">🚀 Запустить все тесты</button>
                        <button onclick="clearResults()" class="btn btn-secondary mt-3">🧹 Очистить</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>📊 Консоль тестирования</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-console" class="test-console"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const testResults = document.getElementById('test-results');
        const testConsole = document.getElementById('test-console');
        let testCount = 0;
        let passedTests = 0;
        let failedTests = 0;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'warn' ? 'warn' : type === 'success' ? 'success' : 'info';
            testConsole.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            testConsole.scrollTop = testConsole.scrollHeight;
            console.log(message);
        }

        function addTestResult(testName, status, details = '') {
            testCount++;
            const div = document.createElement('div');
            div.className = `test-item test-${status}`;
            
            const icon = status === 'passed' ? '✅' : status === 'failed' ? '❌' : '⏳';
            const statusText = status === 'passed' ? 'ПРОЙДЕН' : status === 'failed' ? 'ПРОВАЛЕН' : 'ОЖИДАНИЕ';
            
            div.innerHTML = `
                <strong>${icon} ${testName}</strong><br>
                <small>Статус: ${statusText}</small>
                ${details ? `<br><small class="text-muted">${details}</small>` : ''}
            `;
            
            testResults.appendChild(div);
            
            if (status === 'passed') passedTests++;
            if (status === 'failed') failedTests++;
        }

        function clearResults() {
            testResults.innerHTML = '';
            testConsole.innerHTML = '';
            testCount = 0;
            passedTests = 0;
            failedTests = 0;
        }

        async function testPageLoad(pageName, url) {
            log(`🔍 Тестируем загрузку страницы: ${pageName}`, 'info');
            
            try {
                const response = await fetch(url);
                if (response.ok) {
                    const content = await response.text();
                    
                    // Проверяем базовые элементы
                    const hasTitle = content.includes('<title>');
                    const hasBody = content.includes('<body');
                    const hasNavigation = content.includes('navbar') || content.includes('nav');
                    const hasFooter = content.includes('footer') || content.includes('</footer>');
                    
                    if (hasTitle && hasBody && hasNavigation && hasFooter) {
                        addTestResult(`Загрузка ${pageName}`, 'passed', `HTTP ${response.status}, все элементы найдены`);
                        log(`✅ ${pageName} загружена успешно`, 'success');
                        return true;
                    } else {
                        addTestResult(`Загрузка ${pageName}`, 'failed', `Отсутствуют базовые элементы`);
                        log(`❌ ${pageName} загружена с ошибками`, 'error');
                        return false;
                    }
                } else {
                    addTestResult(`Загрузка ${pageName}`, 'failed', `HTTP ${response.status}`);
                    log(`❌ ${pageName} ошибка ${response.status}`, 'error');
                    return false;
                }
            } catch (error) {
                addTestResult(`Загрузка ${pageName}`, 'failed', `Ошибка: ${error.message}`);
                log(`❌ ${pageName} ошибка: ${error.message}`, 'error');
                return false;
            }
        }

        async function testJavaScriptFiles() {
            log('🔍 Тестируем JavaScript файлы', 'info');
            
            const jsFiles = [
                'assets/js/main.js',
                'assets/js/modern.js',
                'assets/js/adaptive-contrast.js',
                'assets/js/realtime.js'
            ];
            
            for (const file of jsFiles) {
                try {
                    const response = await fetch(file);
                    if (response.ok) {
                        addTestResult(`JS файл: ${file}`, 'passed', `Размер: ${response.headers.get('content-length') || 'неизвестно'}`);
                        log(`✅ ${file} доступен`, 'success');
                    } else {
                        addTestResult(`JS файл: ${file}`, 'failed', `HTTP ${response.status}`);
                        log(`❌ ${file} недоступен`, 'error');
                    }
                } catch (error) {
                    addTestResult(`JS файл: ${file}`, 'failed', `Ошибка: ${error.message}`);
                    log(`❌ ${file} ошибка: ${error.message}`, 'error');
                }
            }
        }

        async function testCSSFiles() {
            log('🔍 Тестируем CSS файлы', 'info');
            
            const cssFiles = [
                'assets/css/style.css',
                'assets/css/luxury-eco.css',
                'assets/css/adaptive-contrast.css'
            ];
            
            for (const file of cssFiles) {
                try {
                    const response = await fetch(file);
                    if (response.ok) {
                        addTestResult(`CSS файл: ${file}`, 'passed', `Размер: ${response.headers.get('content-length') || 'неизвестно'}`);
                        log(`✅ ${file} доступен`, 'success');
                    } else {
                        addTestResult(`CSS файл: ${file}`, 'failed', `HTTP ${response.status}`);
                        log(`❌ ${file} недоступен`, 'error');
                    }
                } catch (error) {
                    addTestResult(`CSS файл: ${file}`, 'failed', `Ошибка: ${error.message}`);
                    log(`❌ ${file} ошибка: ${error.message}`, 'error');
                }
            }
        }

        async function runAllTests() {
            clearResults();
            log('🚀 Начинаем комплексное тестирование сайта...', 'info');
            
            // Тест основных страниц
            const pages = [
                ['Главная', '/'],
                ['Главная (index)', '/index.php'],
                ['Домашняя', '/index.php?page=home'],
                ['О нас', '/index.php?page=about'],
                ['Инвестиции', '/index.php?page=invest'],
                ['Вход', '/index.php?page=login'],
                ['Регистрация', '/index.php?page=register'],
                ['Контакты', '/index.php?page=contact']
            ];
            
            for (const [name, url] of pages) {
                await testPageLoad(name, url);
                await new Promise(resolve => setTimeout(resolve, 200)); // Пауза между запросами
            }
            
            // Тест файлов
            await testJavaScriptFiles();
            await testCSSFiles();
            
            // Итоговая статистика
            log(`📊 Тестирование завершено!`, 'info');
            log(`✅ Пройдено: ${passedTests}`, 'success');
            log(`❌ Провалено: ${failedTests}`, 'error');
            log(`📈 Общий результат: ${testCount} тестов`, 'info');
            
            const successRate = Math.round((passedTests / testCount) * 100);
            if (successRate >= 90) {
                log(`🎉 ОТЛИЧНО! Успешность: ${successRate}%`, 'success');
            } else if (successRate >= 70) {
                log(`⚠️ ХОРОШО! Успешность: ${successRate}%`, 'warn');
            } else {
                log(`🚨 ТРЕБУЕТ ВНИМАНИЯ! Успешность: ${successRate}%`, 'error');
            }
        }

        // Автозапуск при загрузке
        window.addEventListener('load', function() {
            log('🔧 Система тестирования готова', 'success');
            log('👆 Нажмите "Запустить все тесты" для начала', 'info');
        });
    </script>
</body>
</html>
