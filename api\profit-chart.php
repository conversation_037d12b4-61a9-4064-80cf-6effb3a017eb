<?php
// API для получения данных графика прибыли
require_once '../config/config.php';
require_once '../config/session_config.php';
session_start();
require_once '../includes/functions.php';

// Установка заголовков
header('Content-Type: application/json');

// Проверка авторизации
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    $chartData = getProfitChartData($user_id);
    echo json_encode([
        'success' => true,
        'chartData' => $chartData
    ]);
} catch (Exception $e) {
    error_log("Profit chart API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

/**
 * Получение данных для графика прибыли за последние 7 дней
 */
function getProfitChartData($user_id) {
    global $conn;
    
    $labels = [];
    $profits = [];
    
    // Генерируем даты за последние 7 дней
    for ($i = 6; $i >= 0; $i--) {
        $date = date('Y-m-d', strtotime("-$i days"));
        $labels[] = date('d.m', strtotime($date));
        
        // Получаем прибыль за конкретный день
        $stmt = $conn->prepare("
            SELECT COALESCE(SUM(amount), 0) as daily_profit
            FROM transactions 
            WHERE user_id = ? 
            AND type = 'profit' 
            AND DATE(created_at) = ?
        ");
        $stmt->execute([$user_id, $date]);
        $result = $stmt->fetch();
        
        $profits[] = floatval($result['daily_profit']);
    }
    
    return [
        'labels' => $labels,
        'profits' => $profits
    ];
}
?>
