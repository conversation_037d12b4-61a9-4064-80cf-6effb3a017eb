/* ===== ИСПРАВЛЕНИЯ ВЫРАВНИВАНИЯ ТЕКСТА ===== */

/* ===== ГЛОБАЛЬНЫЕ ПРАВИЛА ВЫРАВНИВАНИЯ ===== */

/* Заголовки секций - всегда по центру */
h1, h2, 
.hero-title,
.hero-title-modern,
.section-title,
.section-title-modern,
.section-title-eco {
    text-align: center !important;
    width: 100% !important;
    display: block !important;
    margin-left: auto !important;
    margin-right: auto !important;
    /* Правильные межстрочные интервалы */
    line-height: 1.2 !important;
    margin-bottom: 1.5rem !important;
}

/* Подзаголовки секций - по центру */
.hero-subtitle,
.hero-subtitle-modern,
.section-subtitle,
.section-subtitle-modern {
    text-align: center !important;
    width: 100% !important;
    display: block !important;
    margin-left: auto !important;
    margin-right: auto !important;
    max-width: 700px !important;
    /* Правильные межстрочные интервалы */
    line-height: 1.6 !important;
    margin-bottom: 2rem !important;
}

/* Заголовки в карточках - по левому краю */
h3, h4, h5, h6,
.feature-title,
.feature-title-modern,
.investment-title,
.luxury-investment-title,
.step-title,
.card-title {
    text-align: left !important;
    width: 100% !important;
    /* Правильные межстрочные интервалы */
    line-height: 1.3 !important;
    margin-bottom: 1rem !important;
}

/* Обычный текст в карточках - по левому краю */
p,
.feature-description,
.feature-description-modern,
.investment-description,
.step-description,
.card-text {
    text-align: left !important;
    width: 100% !important;
    /* Правильные межстрочные интервалы */
    line-height: 1.6 !important;
    margin-bottom: 1rem !important;
}

/* Списки - по левому краю */
ul, ol, li {
    text-align: left !important;
    line-height: 1.6 !important;
}

/* ===== СПЕЦИАЛЬНЫЕ СЛУЧАИ ===== */

/* Центрированный текст в статистике */
.stat-number,
.stat-number-modern,
.stat-label,
.stat-label-modern,
.hero-stats-modern .stat-card-modern {
    text-align: center !important;
}

/* Центрированные кнопки */
.hero-buttons,
.hero-buttons-modern,
.text-center .btn,
.btn-container {
    text-align: center !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 1rem !important;
    flex-wrap: wrap !important;
}

/* ===== ИСПРАВЛЕНИЯ ДЛЯ КОНКРЕТНЫХ ЭЛЕМЕНТОВ ===== */

/* Hero секция */
.modern-hero .hero-content-modern {
    text-align: left !important;
}

.modern-hero .hero-title-modern {
    text-align: left !important;
    margin-bottom: 1.5rem !important;
}

.modern-hero .hero-subtitle-modern {
    text-align: left !important;
    margin-bottom: 2rem !important;
}

/* Статистика в hero */
.hero-stats-modern {
    display: flex !important;
    justify-content: flex-start !important;
    gap: 2rem !important;
    margin: 2rem 0 !important;
    flex-wrap: wrap !important;
}

.hero-stats-modern .stat-card-modern {
    text-align: center !important;
    min-width: 120px !important;
}

/* Кнопки в hero */
.hero-buttons-modern {
    display: flex !important;
    justify-content: flex-start !important;
    gap: 1rem !important;
    margin-top: 2rem !important;
    flex-wrap: wrap !important;
}

/* ===== КАРТОЧКИ И СЕТКИ ===== */

/* Контейнеры карточек */
.features-grid-modern,
.row {
    display: grid !important;
    align-items: stretch !important;
    text-align: left !important;
}

/* Внутреннее содержимое карточек */
.feature-card-modern,
.investment-card,
.luxury-investment-card,
.feature-card,
.step-card {
    text-align: left !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
    align-items: stretch !important;
}

/* Иконки в карточках - по центру */
.feature-icon-modern,
.feature-icon,
.step-icon {
    text-align: center !important;
    margin: 0 auto 1.5rem !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

/* ===== НАВИГАЦИЯ ===== */

/* Элементы навигации */
.nav-button,
.navbar-nav .nav-link {
    text-align: center !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

/* Информация о пользователе */
.user-name,
.user-balance {
    text-align: left !important;
    line-height: 1.2 !important;
}

/* ===== ФУТЕР ===== */

/* Колонки футера */
.footer-column h5 {
    text-align: left !important;
    margin-bottom: 1rem !important;
}

.footer-column ul,
.footer-column p {
    text-align: left !important;
}

/* ===== АДАПТИВНЫЕ ИСПРАВЛЕНИЯ ===== */

@media (max-width: 768px) {
    /* На мобильных заголовки hero тоже по центру */
    .modern-hero .hero-title-modern,
    .modern-hero .hero-subtitle-modern {
        text-align: center !important;
    }
    
    /* Статистика и кнопки по центру на мобильных */
    .hero-stats-modern,
    .hero-buttons-modern {
        justify-content: center !important;
    }
    
    /* Уменьшенные отступы */
    h1, h2 {
        margin-bottom: 1rem !important;
    }
    
    .section-subtitle,
    .section-subtitle-modern {
        margin-bottom: 1.5rem !important;
    }
}

@media (max-width: 576px) {
    /* Еще более компактные отступы */
    h1, h2 {
        margin-bottom: 0.75rem !important;
    }
    
    .hero-stats-modern {
        gap: 1rem !important;
    }
    
    .hero-buttons-modern {
        gap: 0.75rem !important;
    }
}

/* ===== ПРИНУДИТЕЛЬНЫЕ ИСПРАВЛЕНИЯ ===== */

/* Восстанавливаем правильное выравнивание */
.text-center,
.text-center * {
    text-align: center !important;
}

.text-left,
.text-left * {
    text-align: left !important;
}

.text-right,
.text-right * {
    text-align: right !important;
}

/* ===== ДОПОЛНИТЕЛЬНЫЕ УЛУЧШЕНИЯ ВЫРАВНИВАНИЯ ===== */

/* Обеспечиваем правильное выравнивание для всех Bootstrap колонок */
[class*="col-"] {
    display: flex !important;
    flex-direction: column !important;
    align-items: stretch !important;
}

/* Центрированные колонки */
.col-12.text-center,
.col-lg-12.text-center,
.col-md-12.text-center {
    align-items: center !important;
    text-align: center !important;
}

/* Улучшенное выравнивание для flex контейнеров */
.d-flex {
    display: flex !important;
}

.justify-content-center {
    justify-content: center !important;
}

.align-items-center {
    align-items: center !important;
}

.flex-wrap {
    flex-wrap: wrap !important;
}

/* Обеспечиваем правильные отступы */
.mb-4 {
    margin-bottom: 2rem !important;
}

.mb-5 {
    margin-bottom: 3rem !important;
}

.mt-4 {
    margin-top: 2rem !important;
}

.gap-2 {
    gap: 0.5rem !important;
}

.gap-3 {
    gap: 1rem !important;
}

.gap-4 {
    gap: 1.5rem !important;
}
