<?php
/**
 * Скрипт для выполнения критических исправлений БД
 * GreenChain EcoFund Platform
 */

require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Исправление критических проблем БД</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; }
        h2 { color: #34495e; border-bottom: 2px solid #ecf0f1; padding-bottom: 5px; }
        .success { color: #27ae60; background: #d5f4e6; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .error { color: #e74c3c; background: #fdf2f2; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .info { color: #3498db; background: #ebf3fd; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .warning { color: #f39c12; background: #fef9e7; padding: 10px; border-radius: 4px; margin: 5px 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .progress { background: #ecf0f1; height: 20px; border-radius: 10px; margin: 10px 0; }
        .progress-bar { background: #3498db; height: 100%; border-radius: 10px; transition: width 0.3s; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔧 Исправление критических проблем БД</h1>";
echo "<div class='info'>Начинаем выполнение исправлений...</div>";

// Проверка подключения к БД
if (!$conn) {
    echo "<div class='error'>❌ Ошибка подключения к базе данных!</div>";
    exit;
}

echo "<div class='success'>✅ Подключение к базе данных установлено</div>";

// Читаем SQL файл
$sql_file = 'database/fix_critical_issues.sql';
if (!file_exists($sql_file)) {
    echo "<div class='error'>❌ SQL файл не найден: $sql_file</div>";
    exit;
}

$sql_content = file_get_contents($sql_file);
echo "<div class='success'>✅ SQL файл загружен</div>";

// Разбиваем на отдельные запросы
$queries = array_filter(array_map('trim', explode(';', $sql_content)));

echo "<h2>📊 Выполнение SQL запросов</h2>";
echo "<div class='info'>Всего запросов к выполнению: " . count($queries) . "</div>";

$executed = 0;
$errors = 0;
$total = count($queries);

foreach ($queries as $index => $query) {
    if (empty($query) || strpos($query, '--') === 0) {
        continue;
    }
    
    $progress = round(($executed / $total) * 100);
    echo "<div class='progress'><div class='progress-bar' style='width: {$progress}%'></div></div>";
    
    try {
        $conn->exec($query);
        $executed++;
        
        // Показываем только важные запросы
        if (stripos($query, 'ALTER TABLE') !== false || 
            stripos($query, 'CREATE TABLE') !== false || 
            stripos($query, 'INSERT') !== false) {
            $short_query = substr($query, 0, 100) . (strlen($query) > 100 ? '...' : '');
            echo "<div class='success'>✅ Выполнено: " . htmlspecialchars($short_query) . "</div>";
        }
        
    } catch (Exception $e) {
        $errors++;
        $short_query = substr($query, 0, 100) . (strlen($query) > 100 ? '...' : '');
        echo "<div class='error'>❌ Ошибка в запросе: " . htmlspecialchars($short_query) . "<br>Ошибка: " . $e->getMessage() . "</div>";
    }
}

echo "<h2>📈 Результаты выполнения</h2>";
echo "<div class='info'>Успешно выполнено: $executed запросов</div>";
if ($errors > 0) {
    echo "<div class='warning'>Ошибок: $errors</div>";
} else {
    echo "<div class='success'>✅ Все запросы выполнены без ошибок!</div>";
}

// Проверяем результаты
echo "<h2>🔍 Проверка результатов</h2>";

// Проверяем колонку remember_token
try {
    $stmt = $conn->query("SHOW COLUMNS FROM users LIKE 'remember_token'");
    if ($stmt->rowCount() > 0) {
        echo "<div class='success'>✅ Колонка remember_token добавлена в таблицу users</div>";
    } else {
        echo "<div class='error'>❌ Колонка remember_token не найдена в таблице users</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Ошибка проверки колонки remember_token: " . $e->getMessage() . "</div>";
}

// Проверяем таблицу settings
try {
    $stmt = $conn->query("SELECT COUNT(*) as count FROM settings");
    $result = $stmt->fetch();
    if ($result['count'] > 0) {
        echo "<div class='success'>✅ Таблица settings создана и содержит " . $result['count'] . " настроек</div>";
    } else {
        echo "<div class='warning'>⚠️ Таблица settings создана, но пуста</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Ошибка проверки таблицы settings: " . $e->getMessage() . "</div>";
}

// Проверяем функцию getSetting
try {
    $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
    $stmt->execute(['min_withdrawal']);
    $result = $stmt->fetch();
    if ($result) {
        echo "<div class='success'>✅ Функция getSetting() должна работать. Тестовое значение min_withdrawal: " . $result['setting_value'] . "</div>";
    } else {
        echo "<div class='warning'>⚠️ Настройка min_withdrawal не найдена</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Ошибка тестирования getSetting(): " . $e->getMessage() . "</div>";
}

// Показываем все настройки
echo "<h2>⚙️ Текущие настройки системы</h2>";
try {
    $stmt = $conn->query("SELECT setting_key, setting_value, description FROM settings ORDER BY setting_key");
    $settings = $stmt->fetchAll();
    
    if (count($settings) > 0) {
        echo "<table style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'><th style='padding: 8px; border: 1px solid #ddd;'>Ключ</th><th style='padding: 8px; border: 1px solid #ddd;'>Значение</th><th style='padding: 8px; border: 1px solid #ddd;'>Описание</th></tr>";
        
        foreach ($settings as $setting) {
            echo "<tr>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($setting['setting_key']) . "</td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($setting['setting_value']) . "</td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($setting['description']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='warning'>⚠️ Настройки не найдены</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Ошибка получения настроек: " . $e->getMessage() . "</div>";
}

echo "<h2>🎯 Следующие шаги</h2>";
echo "<div class='info'>
1. ✅ Проблемы с заголовками HTTP исправлены<br>
2. ✅ Отсутствующие файлы проверены<br>
3. ✅ Функция getSetting() должна работать<br>
4. ✅ Колонка remember_token добавлена<br>
5. 🔄 Теперь нужно протестировать все API endpoints<br>
6. 🔄 Проверить формы авторизации<br>
7. 🔄 Убедиться в работе админ панели
</div>";

echo "<div style='text-align: center; margin: 20px 0;'>
<a href='index.php' style='background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>← Вернуться на главную</a>
<a href='api-test.html' style='background: #27ae60; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-left: 10px;'>Тестировать API →</a>
</div>";

echo "</div></body></html>";
?>
