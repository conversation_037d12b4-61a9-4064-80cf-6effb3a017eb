/**
 * Адаптивная контрастность и умный скролл для GreenChain EcoFund
 * Автоматически адаптирует цвета текста в зависимости от фона секций
 */

class AdaptiveContrast {
    constructor() {
        this.sections = [];
        this.currentSection = null;
        this.isScrolling = false;
        this.scrollTimeout = null;
        
        this.init();
    }
    
    init() {
        this.detectSections();
        this.setupScrollListener();
        this.setupResizeListener();
        this.checkInitialSection();
        
        // Добавляем классы для адаптивности
        this.addAdaptiveClasses();
        
        console.log('Adaptive Contrast System initialized');
    }
    
    /**
     * Определяет все секции на странице и их типы фонов
     */
    detectSections() {
        const sectionSelectors = [
            'section',
            '.hero-section',
            '.luxury-hero',
            '.bg-luxury-hero',
            '.bg-luxury-investment',
            '.bg-light',
            '.bg-dark',
            '.bg-white',
            'header',
            'footer',
            '.luxury-navbar'
        ];
        
        sectionSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                const sectionData = this.analyzeSectionBackground(element);
                if (sectionData) {
                    this.sections.push(sectionData);
                }
            });
        });
        
        // Сортируем секции по позиции на странице
        this.sections.sort((a, b) => a.top - b.top);
    }
    
    /**
     * Анализирует фон секции и определяет нужный тип контрастности
     */
    analyzeSectionBackground(element) {
        const rect = element.getBoundingClientRect();
        const computedStyle = window.getComputedStyle(element);
        const classList = Array.from(element.classList);
        
        // Определяем тип фона на основе классов
        let backgroundType = 'light'; // по умолчанию
        
        const darkClasses = [
            'luxury-hero',
            'bg-luxury-hero', 
            'bg-dark',
            'luxury-navbar',
            'dark-section'
        ];
        
        const lightClasses = [
            'bg-light',
            'bg-white',
            'luxury-card',
            'bg-luxury-investment',
            'light-section'
        ];
        
        // Проверяем классы
        if (darkClasses.some(cls => classList.includes(cls))) {
            backgroundType = 'dark';
        } else if (lightClasses.some(cls => classList.includes(cls))) {
            backgroundType = 'light';
        } else {
            // Анализируем CSS свойства
            const bgColor = computedStyle.backgroundColor;
            const bgImage = computedStyle.backgroundImage;
            
            if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)') {
                backgroundType = this.getBackgroundTypeFromColor(bgColor);
            } else if (bgImage && bgImage !== 'none') {
                // Для градиентов и изображений используем эвристику
                if (bgImage.includes('#1a3d2e') || bgImage.includes('#0f2419')) {
                    backgroundType = 'dark';
                }
            }
        }
        
        return {
            element: element,
            top: rect.top + window.scrollY,
            bottom: rect.bottom + window.scrollY,
            height: rect.height,
            backgroundType: backgroundType,
            selector: this.getElementSelector(element)
        };
    }
    
    /**
     * Определяет тип фона на основе цвета
     */
    getBackgroundTypeFromColor(color) {
        // Конвертируем цвет в RGB
        const rgb = this.parseColor(color);
        if (!rgb) return 'light';
        
        // Вычисляем относительную яркость
        const luminance = this.calculateLuminance(rgb.r, rgb.g, rgb.b);
        
        // Если яркость меньше 0.5, считаем фон темным
        return luminance < 0.5 ? 'dark' : 'light';
    }
    
    /**
     * Парсит CSS цвет в RGB
     */
    parseColor(color) {
        const div = document.createElement('div');
        div.style.color = color;
        document.body.appendChild(div);
        const computedColor = window.getComputedStyle(div).color;
        document.body.removeChild(div);
        
        const match = computedColor.match(/rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)/);
        if (match) {
            return {
                r: parseInt(match[1]),
                g: parseInt(match[2]),
                b: parseInt(match[3])
            };
        }
        return null;
    }
    
    /**
     * Вычисляет относительную яркость цвета
     */
    calculateLuminance(r, g, b) {
        const [rs, gs, bs] = [r, g, b].map(c => {
            c = c / 255;
            return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
        });
        return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
    }
    
    /**
     * Получает CSS селектор для элемента
     */
    getElementSelector(element) {
        if (element.id) return `#${element.id}`;
        if (element.className) {
            const classes = element.className.split(' ').filter(c => c.length > 0);
            if (classes.length > 0) return `.${classes[0]}`;
        }
        return element.tagName.toLowerCase();
    }
    
    /**
     * Настраивает слушатель скролла
     */
    setupScrollListener() {
        let ticking = false;
        
        const handleScroll = () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    this.onScroll();
                    ticking = false;
                });
                ticking = true;
            }
        };
        
        window.addEventListener('scroll', handleScroll, { passive: true });
    }
    
    /**
     * Настраивает слушатель изменения размера окна
     */
    setupResizeListener() {
        let resizeTimeout;
        
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.sections = [];
                this.detectSections();
                this.onScroll();
            }, 250);
        });
    }
    
    /**
     * Обработчик скролла
     */
    onScroll() {
        const scrollY = window.scrollY;
        const windowHeight = window.innerHeight;
        const viewportCenter = scrollY + windowHeight / 2;
        
        // Находим текущую секцию
        const currentSection = this.sections.find(section => 
            viewportCenter >= section.top && viewportCenter <= section.bottom
        );
        
        if (currentSection && currentSection !== this.currentSection) {
            this.currentSection = currentSection;
            this.applyContrastForSection(currentSection);
        }
        
        // Обновляем навигацию
        this.updateNavigation(scrollY);
    }
    
    /**
     * Применяет контрастность для текущей секции
     */
    applyContrastForSection(section) {
        const body = document.body;
        
        // Удаляем предыдущие классы
        body.classList.remove('current-section-dark', 'current-section-light');
        
        // Добавляем новый класс
        body.classList.add(`current-section-${section.backgroundType}`);
        
        // Применяем адаптивные стили к элементам в viewport
        this.applyAdaptiveStyles(section);
        
        // Обновляем CSS переменные
        this.updateCSSVariables(section.backgroundType);
    }
    
    /**
     * Применяет адаптивные стили к элементам
     */
    applyAdaptiveStyles(section) {
        const adaptiveElements = document.querySelectorAll('.adaptive-text, .adaptive-icon');
        
        adaptiveElements.forEach(element => {
            const elementRect = element.getBoundingClientRect();
            const elementCenter = elementRect.top + elementRect.height / 2;
            
            // Проверяем, находится ли элемент в viewport
            if (elementCenter >= 0 && elementCenter <= window.innerHeight) {
                element.classList.remove('on-dark-bg', 'on-light-bg');
                element.classList.add(`on-${section.backgroundType}-bg`);
            }
        });
    }
    
    /**
     * Обновляет CSS переменные для адаптивной контрастности
     */
    updateCSSVariables(backgroundType) {
        const root = document.documentElement;
        
        if (backgroundType === 'dark') {
            root.style.setProperty('--adaptive-text-primary', 'var(--dark-section-text)');
            root.style.setProperty('--adaptive-text-secondary', 'var(--dark-section-text-secondary)');
            root.style.setProperty('--adaptive-text-muted', 'var(--dark-section-text-muted)');
        } else {
            root.style.setProperty('--adaptive-text-primary', 'var(--light-section-text)');
            root.style.setProperty('--adaptive-text-secondary', 'var(--light-section-text-secondary)');
            root.style.setProperty('--adaptive-text-muted', 'var(--light-section-text-muted)');
        }
    }
    
    /**
     * Обновляет стили навигации в зависимости от скролла
     */
    updateNavigation(scrollY) {
        const navbar = document.querySelector('.luxury-navbar');
        if (!navbar) return;
        
        if (scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }
    
    /**
     * Проверяет начальную секцию при загрузке
     */
    checkInitialSection() {
        this.onScroll();
    }
    
    /**
     * Добавляет классы для адаптивности к элементам
     */
    addAdaptiveClasses() {
        // Добавляем классы к заголовкам
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        headings.forEach(heading => {
            if (!heading.classList.contains('adaptive-text')) {
                heading.classList.add('adaptive-text');
            }
        });
        
        // Добавляем классы к параграфам
        const paragraphs = document.querySelectorAll('p, .luxury-subtitle');
        paragraphs.forEach(p => {
            if (!p.classList.contains('adaptive-text')) {
                p.classList.add('adaptive-text');
            }
        });
        
        // Добавляем классы к иконкам
        const icons = document.querySelectorAll('i[class*="fa"], .luxury-icon');
        icons.forEach(icon => {
            if (!icon.classList.contains('adaptive-icon')) {
                icon.classList.add('adaptive-icon');
            }
        });
    }
    
    /**
     * Принудительно обновляет контрастность
     */
    forceUpdate() {
        this.sections = [];
        this.detectSections();
        this.onScroll();
    }
}

// Инициализация при загрузке DOM
document.addEventListener('DOMContentLoaded', () => {
    window.adaptiveContrast = new AdaptiveContrast();
});

// Экспорт для использования в других модулях
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdaptiveContrast;
}
