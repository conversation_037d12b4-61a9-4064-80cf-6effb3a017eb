# Исправление ошибки установки базы данных

## Проблема
При импорте файла `database/schema.sql` в phpMyAdmin возникает ошибка:
```
SQL запрос: Копировать
#1304 - PROCEDURE CalculateDailyProfit already exists
```

## Решение

### Вариант 1: Использование упрощенной схемы (Рекомендуется)

1. **Удалите существующую базу данных** (если она была создана):
   ```sql
   DROP DATABASE IF EXISTS greenchain_ecofund;
   ```

2. **Создайте новую базу данных**:
   ```sql
   CREATE DATABASE greenchain_ecofund CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **Импортируйте упрощенную схему**:
   - Используйте файл `database/schema_simple.sql` вместо `database/schema.sql`
   - Этот файл не содержит процедур и должен импортироваться без ошибок

4. **Импортируйте начальные данные**:
   ```sql
   -- Выполните в phpMyAdmin или MySQL консоли
   USE greenchain_ecofund;
   SOURCE database/initial_data.sql;
   ```

### Вариант 2: Исправление основной схемы

Если вы хотите использовать полную схему с процедурами:

1. **Очистите базу данных**:
   ```sql
   DROP DATABASE IF EXISTS greenchain_ecofund;
   CREATE DATABASE greenchain_ecofund CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   USE greenchain_ecofund;
   ```

2. **Импортируйте исправленную схему**:
   - Файл `database/schema.sql` был обновлен с добавлением `DROP PROCEDURE IF EXISTS`
   - Теперь он должен импортироваться без ошибок

### Вариант 3: Ручное исправление в phpMyAdmin

Если ошибка все еще возникает:

1. **Удалите существующие процедуры**:
   ```sql
   DROP PROCEDURE IF EXISTS CalculateDailyProfits;
   DROP PROCEDURE IF EXISTS ProcessReferralBonus;
   ```

2. **Импортируйте схему заново**

## Проверка установки

После успешного импорта проверьте, что все таблицы созданы:

```sql
SHOW TABLES;
```

Вы должны увидеть следующие таблицы:
- users
- investment_packages
- user_investments
- transactions
- referrals
- notifications
- user_notification_settings
- support_tickets
- support_messages
- map_projects
- tasks
- user_task_progress
- achievements
- user_achievements
- education_categories
- education_articles
- faq
- settings
- activity_logs

## Создание администратора

После установки базы данных создайте администратора:

```sql
INSERT INTO users (
    email, 
    password_hash, 
    first_name, 
    last_name, 
    referral_code, 
    role, 
    email_verified
) VALUES (
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'Admin',
    'User',
    'ADMIN001',
    'admin',
    1
);
```

## Настройка конфигурации

1. **Скопируйте файл конфигурации**:
   ```bash
   cp config/config.example.php config/config.php
   ```

2. **Отредактируйте настройки подключения к БД** в `config/config.php`:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'greenchain_ecofund');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   ```

## Тестирование

1. Откройте сайт в браузере
2. Попробуйте зарегистрироваться
3. Войдите как администратор: <EMAIL> / password
4. Проверьте административную панель

## Дополнительные настройки

### Настройка cron задач (опционально)

Для автоматического начисления прибыли создайте cron задачи:

```bash
# Ежедневное начисление прибыли в 00:01
1 0 * * * /usr/bin/php /path/to/site/cron/daily_profit.php

# Обновление статистики каждый час
0 * * * * /usr/bin/php /path/to/site/cron/update_stats.php
```

### Настройка прав доступа

```bash
chmod 755 uploads/
chmod 755 uploads/avatars/
chmod 644 config/config.php
```

## Поддержка

Если проблемы продолжаются:

1. Проверьте версию MySQL (требуется 5.7+)
2. Убедитесь, что у пользователя БД есть права на создание процедур
3. Проверьте логи ошибок MySQL
4. Обратитесь к документации в README.md

## Альтернативное решение

Если ничего не помогает, можете:

1. Использовать только `schema_simple.sql` (без процедур)
2. Реализовать логику начисления прибыли через PHP cron скрипты
3. Это не повлияет на основную функциональность платформы
