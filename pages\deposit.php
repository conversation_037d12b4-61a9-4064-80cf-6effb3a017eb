<?php
// Страница пополнения баланса
// GreenChain EcoFund - Deposit Page

require_once '../includes/functions.php';

// Проверка авторизации
if (!isLoggedIn()) {
    redirect('/index.php?page=login');
    exit;
}

$user = getCurrentUser();
$page_title = "Пополнение баланса";

// Получение настроек платежной системы
$min_deposit = getSetting('payment_min_deposit', 10);
$max_deposit = getSetting('payment_max_deposit', 10000);
$usdt_wallet = getSetting('payment_usdt_wallet', 'TYourUSDTWalletAddressHere');
$system_enabled = getSetting('payment_system_enabled', 1);

// Получение последних заявок пользователя
try {
    $stmt = $conn->prepare("
        SELECT * FROM payment_requests 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$user['id']]);
    $recent_requests = $stmt->fetchAll();
} catch (Exception $e) {
    $recent_requests = [];
}
?>

<div class="container-fluid">
    <div class="row">
        <!-- Основной контент -->
        <div class="col-lg-8">
            <div class="card luxury-card">
                <div class="card-header bg-gradient-green text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-plus-circle me-2"></i>
                        Пополнение баланса USDT
                    </h4>
                </div>
                <div class="card-body">
                    <?php if (!$system_enabled): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Платежная система временно недоступна. Попробуйте позже.
                        </div>
                    <?php else: ?>
                        
                        <!-- Информация о пополнении -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="info-card bg-light p-3 rounded">
                                    <h6 class="text-primary">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Информация о пополнении
                                    </h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><strong>Валюта:</strong> USDT</li>
                                        <li><strong>Сеть:</strong> TRC-20 (TRON)</li>
                                        <li><strong>Мин. сумма:</strong> <?= $min_deposit ?> USDT</li>
                                        <li><strong>Макс. сумма:</strong> <?= $max_deposit ?> USDT</li>
                                        <li><strong>Комиссия:</strong> Бесплатно</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-card bg-light p-3 rounded">
                                    <h6 class="text-success">
                                        <i class="fas fa-wallet me-2"></i>
                                        Адрес для пополнения
                                    </h6>
                                    <div class="wallet-address">
                                        <code id="walletAddress" class="d-block p-2 bg-white rounded border">
                                            <?= htmlspecialchars($usdt_wallet) ?>
                                        </code>
                                        <button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="copyWalletAddress()">
                                            <i class="fas fa-copy me-1"></i>
                                            Копировать адрес
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Форма создания заявки -->
                        <form id="depositForm" class="needs-validation" novalidate>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="amount" class="form-label">
                                            <i class="fas fa-dollar-sign me-1"></i>
                                            Сумма пополнения (USDT)
                                        </label>
                                        <input type="number" 
                                               class="form-control" 
                                               id="amount" 
                                               name="amount" 
                                               min="<?= $min_deposit ?>" 
                                               max="<?= $max_deposit ?>" 
                                               step="0.01" 
                                               required>
                                        <div class="invalid-feedback">
                                            Введите сумму от <?= $min_deposit ?> до <?= $max_deposit ?> USDT
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="transactionHash" class="form-label">
                                            <i class="fas fa-hashtag me-1"></i>
                                            Хеш транзакции
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="transactionHash" 
                                               name="transaction_hash" 
                                               placeholder="Введите хеш транзакции USDT TRC-20"
                                               required>
                                        <div class="invalid-feedback">
                                            Введите хеш транзакции
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="walletAddressFrom" class="form-label">
                                    <i class="fas fa-wallet me-1"></i>
                                    Адрес отправителя (необязательно)
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="walletAddressFrom" 
                                       name="wallet_address" 
                                       placeholder="Адрес кошелька, с которого была отправлена транзакция">
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                                    <label class="form-check-label" for="agreeTerms">
                                        Я подтверждаю, что отправил указанную сумму USDT на адрес выше и согласен с 
                                        <a href="#" class="text-primary">условиями пополнения</a>
                                    </label>
                                    <div class="invalid-feedback">
                                        Необходимо согласиться с условиями
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="button" class="btn btn-secondary me-md-2" onclick="clearForm()">
                                    <i class="fas fa-times me-1"></i>
                                    Очистить
                                </button>
                                <button type="submit" class="btn btn-success" id="submitBtn">
                                    <i class="fas fa-paper-plane me-1"></i>
                                    Создать заявку
                                </button>
                            </div>
                        </form>

                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Боковая панель -->
        <div class="col-lg-4">
            <!-- Текущий баланс -->
            <div class="card luxury-card mb-4">
                <div class="card-header bg-gradient-blue text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-balance-scale me-2"></i>
                        Текущий баланс
                    </h6>
                </div>
                <div class="card-body text-center">
                    <h3 class="text-success mb-0">
                        $<?= number_format($user['balance'], 2) ?>
                    </h3>
                    <small class="text-muted">USD</small>
                </div>
            </div>

            <!-- Инструкция -->
            <div class="card luxury-card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        Как пополнить баланс?
                    </h6>
                </div>
                <div class="card-body">
                    <ol class="list-unstyled">
                        <li class="mb-2">
                            <span class="badge bg-primary rounded-pill me-2">1</span>
                            Скопируйте адрес кошелька выше
                        </li>
                        <li class="mb-2">
                            <span class="badge bg-primary rounded-pill me-2">2</span>
                            Отправьте USDT через сеть TRC-20
                        </li>
                        <li class="mb-2">
                            <span class="badge bg-primary rounded-pill me-2">3</span>
                            Скопируйте хеш транзакции
                        </li>
                        <li class="mb-2">
                            <span class="badge bg-primary rounded-pill me-2">4</span>
                            Заполните форму и создайте заявку
                        </li>
                        <li class="mb-0">
                            <span class="badge bg-success rounded-pill me-2">5</span>
                            Ожидайте подтверждения администратора
                        </li>
                    </ol>
                </div>
            </div>

            <!-- Последние заявки -->
            <?php if (!empty($recent_requests)): ?>
            <div class="card luxury-card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Последние заявки
                    </h6>
                </div>
                <div class="card-body">
                    <?php foreach ($recent_requests as $request): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                            <div>
                                <small class="text-muted">
                                    <?= date('d.m.Y H:i', strtotime($request['created_at'])) ?>
                                </small>
                                <div class="fw-bold">
                                    $<?= number_format($request['amount'], 2) ?>
                                </div>
                            </div>
                            <div>
                                <?php
                                $status_class = [
                                    'pending' => 'warning',
                                    'approved' => 'success',
                                    'rejected' => 'danger',
                                    'cancelled' => 'secondary'
                                ];
                                $status_text = [
                                    'pending' => 'Ожидает',
                                    'approved' => 'Одобрено',
                                    'rejected' => 'Отклонено',
                                    'cancelled' => 'Отменено'
                                ];
                                ?>
                                <span class="badge bg-<?= $status_class[$request['status']] ?>">
                                    <?= $status_text[$request['status']] ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    <div class="text-center mt-3">
                        <a href="index.php?page=payment-history" class="btn btn-sm btn-outline-primary">
                            Вся история
                        </a>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Модальное окно успеха -->
<div class="modal fade" id="successModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>
                    Заявка создана
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="mb-0">Ваша заявка на пополнение успешно создана и отправлена на рассмотрение администратору.</p>
                <p class="mb-0 mt-2"><strong>ID заявки:</strong> <span id="requestId"></span></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" data-bs-dismiss="modal">Понятно</button>
            </div>
        </div>
    </div>
</div>

<script>
// Функция копирования адреса кошелька
function copyWalletAddress() {
    const walletAddress = document.getElementById('walletAddress').textContent.trim();
    navigator.clipboard.writeText(walletAddress).then(() => {
        showNotification('Адрес кошелька скопирован!', 'success');
    }).catch(() => {
        // Fallback для старых браузеров
        const textArea = document.createElement('textarea');
        textArea.value = walletAddress;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('Адрес кошелька скопирован!', 'success');
    });
}

// Функция очистки формы
function clearForm() {
    document.getElementById('depositForm').reset();
    document.getElementById('depositForm').classList.remove('was-validated');
}

// Обработка отправки формы
document.getElementById('depositForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (!this.checkValidity()) {
        e.stopPropagation();
        this.classList.add('was-validated');
        return;
    }

    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    
    try {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Создание заявки...';

        const formData = new FormData(this);
        const data = Object.fromEntries(formData.entries());

        const response = await fetch('/api/payment-request.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            document.getElementById('requestId').textContent = result.request_id;
            new bootstrap.Modal(document.getElementById('successModal')).show();
            clearForm();
        } else {
            showNotification(result.message || 'Ошибка создания заявки', 'error');
        }

    } catch (error) {
        console.error('Error:', error);
        showNotification('Ошибка соединения с сервером', 'error');
    } finally {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
});

// Функция показа уведомлений
function showNotification(message, type = 'info') {
    // Создаем элемент уведомления
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Автоматическое удаление через 5 секунд
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>
