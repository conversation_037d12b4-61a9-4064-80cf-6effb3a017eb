# Быстрое руководство: Черный текст в колонках

## ✅ Реализовано

В систему адаптивной контрастности GreenChain EcoFund добавлена поддержка черного цвета текста (#000000) для колонок.

## 🚀 Как использовать

### 1. Для любой колонки
```html
<div class="col-lg-6 black-text">
    Весь текст в этой колонке будет черным
</div>
```

### 2. Для статистических карточек
```html
<div class="stats-card black-text">
    <div class="stats-value">$125,000</div>
    <div class="stats-label">Баланс</div>
</div>
```

### 3. Для таблиц (конкретные колонки)
```html
<table class="table">
    <thead>
        <tr>
            <th class="black-column">Тип операции</th>
            <th class="col-black">Статус</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td class="black-column">Инвестиция</td>
            <td class="col-black">Активна</td>
        </tr>
    </tbody>
</table>
```

### 4. Принудительный черный (всегда)
```html
<div class="black-text-force">
    Всегда черный текст
</div>
```

### 5. Умный черный (адаптируется к фону)
```html
<div class="black-text-smart">
    Черный на светлом, белый на темном
</div>
```

## 📋 Доступные классы

| Класс | Применение |
|-------|------------|
| `.black-text` | Для колонок и карточек |
| `.black-text-column` | Альтернатив для колонок |
| `.col-black-text` | Еще один вариант для колонок |
| `.black-column` | Для колонок таблиц |
| `.col-black` | Для ячеек таблиц |
| `.table-black-text` | Для всей таблицы |
| `.black-text-force` | Принудительный черный |
| `.black-text-smart` | Адаптивный черный/белый |

## 🎯 Примеры применения

### Dashboard статистика
```html
<div class="col-lg-3 col-md-6 mb-3">
    <div class="stats-card black-text">
        <div class="stats-value">$125,000</div>
        <div class="stats-label">Доступный баланс</div>
    </div>
</div>
```

### Таблица транзакций
```html
<table class="table">
    <tr>
        <td>15.06.2024</td>
        <td class="black-column">Инвестиция</td>
        <td class="col-black">Активна</td>
    </tr>
</table>
```

### Bootstrap колонки
```html
<div class="row">
    <div class="col-lg-4 black-text">Колонка 1</div>
    <div class="col-lg-4">Обычная колонка</div>
    <div class="col-lg-4 black-text">Колонка 3</div>
</div>
```

## 🔧 Тестирование

Откройте для просмотра всех примеров:
```
http://127.0.0.1:8081/test-contrast.html
```

Найдите раздел "Тест: Черный текст в колонках" для демонстрации всех возможностей.

## ⚠️ Важные замечания

1. **Контрастность**: Убедитесь, что черный текст читаем на вашем фоне
2. **Темные фоны**: Используйте `.black-text-smart` для автоматической адаптации
3. **Специфичность**: Все классы используют `!important` для гарантированного применения

## 📁 Измененные файлы

- `assets/css/adaptive-contrast.css` - добавлены новые CSS классы
- `test-contrast.html` - добавлены примеры использования
- `BLACK_TEXT_COLUMNS_GUIDE.md` - полная документация

## ✨ Готово к использованию!

Система черного текста полностью интегрирована в адаптивную контрастность и готова к использованию на всех страницах GreenChain EcoFund.
