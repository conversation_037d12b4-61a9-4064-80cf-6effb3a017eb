<?php
$page_title = "Административная панель";

// Проверка прав администратора
if (!isAdmin()) {
    redirect('index.php?page=dashboard', 'Доступ запрещен', 'error');
}

// Получение статистики
$admin_stats = getAdminStats();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header mb-4">
                <h2 class="page-title">
                    <i class="fas fa-cog text-primary"></i> Административная панель
                </h2>
                <p class="page-subtitle">Управление платформой GreenChain EcoFund</p>
            </div>
        </div>
    </div>
    
    <!-- Статистика -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-primary">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo number_format($admin_stats['total_users']); ?></div>
                    <div class="stats-label">Всего пользователей</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-success">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo formatMoney($admin_stats['total_investments']); ?></div>
                    <div class="stats-label">Общие инвестиции</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-warning">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo formatMoney($admin_stats['total_profit']); ?></div>
                    <div class="stats-label">Общая прибыль</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-info">
                    <i class="fas fa-handshake"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo number_format($admin_stats['active_investments']); ?></div>
                    <div class="stats-label">Активные инвестиции</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Быстрые действия -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools"></i> Быстрые действия
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-4 col-md-6 mb-3">
                            <a href="#" class="admin-action-card">
                                <div class="action-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="action-content">
                                    <h6>Управление пользователями</h6>
                                    <p>Просмотр и редактирование пользователей</p>
                                </div>
                            </a>
                        </div>
                        
                        <div class="col-lg-4 col-md-6 mb-3">
                            <a href="#" class="admin-action-card">
                                <div class="action-icon">
                                    <i class="fas fa-box"></i>
                                </div>
                                <div class="action-content">
                                    <h6>Инвестиционные пакеты</h6>
                                    <p>Настройка пакетов и тарифов</p>
                                </div>
                            </a>
                        </div>
                        
                        <div class="col-lg-4 col-md-6 mb-3">
                            <a href="#" class="admin-action-card">
                                <div class="action-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="action-content">
                                    <h6>Настройки системы</h6>
                                    <p>Общие настройки платформы</p>
                                </div>
                            </a>
                        </div>
                        
                        <div class="col-lg-4 col-md-6 mb-3">
                            <a href="#" class="admin-action-card">
                                <div class="action-icon">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <div class="action-content">
                                    <h6>Отчеты и аналитика</h6>
                                    <p>Детальная статистика</p>
                                </div>
                            </a>
                        </div>
                        
                        <div class="col-lg-4 col-md-6 mb-3">
                            <a href="#" class="admin-action-card">
                                <div class="action-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="action-content">
                                    <h6>Уведомления</h6>
                                    <p>Массовые рассылки</p>
                                </div>
                            </a>
                        </div>
                        
                        <div class="col-lg-4 col-md-6 mb-3">
                            <a href="#" class="admin-action-card">
                                <div class="action-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="action-content">
                                    <h6>Безопасность</h6>
                                    <p>Логи и мониторинг</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stats-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
    font-size: 1.5rem;
}

.stats-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
}

.stats-label {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.admin-action-card {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    text-decoration: none;
    color: var(--text-dark);
    transition: all 0.3s ease;
    height: 100%;
}

.admin-action-card:hover {
    background: #f8f9fa;
    border-color: var(--primary-green);
    color: var(--text-dark);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.action-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.action-content h6 {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.action-content p {
    margin-bottom: 0;
    color: var(--text-muted);
    font-size: 0.875rem;
}
</style>

<?php
/**
 * Получение статистики для админ панели
 */
function getAdminStats() {
    global $conn;
    
    try {
        // Общее количество пользователей
        $stmt = $conn->query("SELECT COUNT(*) as total_users FROM users WHERE status != 'banned'");
        $total_users = $stmt->fetch()['total_users'];
        
        // Общие инвестиции
        $stmt = $conn->query("SELECT COALESCE(SUM(amount), 0) as total_investments FROM user_investments");
        $total_investments = $stmt->fetch()['total_investments'];
        
        // Общая прибыль
        $stmt = $conn->query("SELECT COALESCE(SUM(total_profit), 0) as total_profit FROM user_investments");
        $total_profit = $stmt->fetch()['total_profit'];
        
        // Активные инвестиции
        $stmt = $conn->query("SELECT COUNT(*) as active_investments FROM user_investments WHERE status = 'active'");
        $active_investments = $stmt->fetch()['active_investments'];
        
        return [
            'total_users' => $total_users,
            'total_investments' => $total_investments,
            'total_profit' => $total_profit,
            'active_investments' => $active_investments
        ];
        
    } catch (Exception $e) {
        error_log("Admin stats error: " . $e->getMessage());
        return [
            'total_users' => 0,
            'total_investments' => 0,
            'total_profit' => 0,
            'active_investments' => 0
        ];
    }
}
?>
