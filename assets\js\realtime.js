// <PERSON><PERSON><PERSON><PERSON>coFund - Real-time Updates

// Переменные для управления обновлениями
let updateInterval = null;
let isUpdating = false;
let updateFrequency = 30000; // 30 секунд
let retryCount = 0;
let maxRetries = 3;

// Запуск обновлений в реальном времени
function startRealTimeUpdates() {
    // Проверяем, авторизован ли пользователь
    if (!isLoggedIn) {
        return;
    }
    
    // Первое обновление сразу
    updateUserData();
    
    // Запускаем периодические обновления
    updateInterval = setInterval(() => {
        if (!isUpdating) {
            updateUserData();
        }
    }, updateFrequency);
    
    // Обновляем общую статистику реже
    setInterval(updateGlobalStats, 60000); // каждую минуту
    
    // Обновляем курсы криптовалют
    setInterval(updateCryptoRates, 120000); // каждые 2 минуты
    
    console.log('Real-time updates started');
}

// Остановка обновлений
function stopRealTimeUpdates() {
    if (updateInterval) {
        clearInterval(updateInterval);
        updateInterval = null;
        console.log('Real-time updates stopped');
    }
}

// Обновление данных пользователя
async function updateUserData() {
    if (isUpdating) return;
    
    isUpdating = true;
    
    try {
        const response = await fetch('api/realtime.php?action=user_data', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
            // Обновляем баланс
            updateBalance(data.balance);
            
            // Обновляем дневную прибыль
            updateDailyProfit(data.daily_profit);
            
            // Обновляем общую прибыль
            updateTotalProfit(data.total_profit);
            
            // Обновляем активные инвестиции
            updateActiveInvestments(data.active_investments);
            
            // Обновляем статус рефералов
            updateReferralStats(data.referral_stats);
            
            // Обновляем прогресс заданий
            updateTaskProgress(data.task_progress);
            
            // Сбрасываем счетчик ошибок
            retryCount = 0;
            
        } else {
            console.error('Error updating user data:', data.message);
        }
        
    } catch (error) {
        console.error('Error fetching user data:', error);
        handleUpdateError();
    } finally {
        isUpdating = false;
    }
}

// Обновление глобальной статистики
async function updateGlobalStats() {
    try {
        const response = await fetch('api/realtime.php?action=global_stats');
        const data = await response.json();
        
        if (data.success) {
            // Обновляем общее количество пользователей
            const totalUsersElement = document.getElementById('total-users');
            if (totalUsersElement && data.total_users) {
                animateNumberUpdate(totalUsersElement, data.total_users);
            }
            
            // Обновляем общую сумму инвестиций
            const totalInvestedElement = document.getElementById('total-invested');
            if (totalInvestedElement && data.total_invested) {
                animateNumberUpdate(totalInvestedElement, data.total_invested);
            }
            
            // Обновляем статистику проектов на карте
            updateMapStats(data.project_stats);
            
            // Обновляем лидерборд
            updateLeaderboard(data.leaderboard);
        }
        
    } catch (error) {
        console.error('Error updating global stats:', error);
    }
}

// Обновление курсов криптовалют
async function updateCryptoRates() {
    try {
        const response = await fetch('api/realtime.php?action=crypto_rates');
        const data = await response.json();
        
        if (data.success && data.rates) {
            // Обновляем отображение курсов
            const ratesContainer = document.getElementById('crypto-rates');
            if (ratesContainer) {
                updateCryptoDisplay(data.rates);
            }
        }
        
    } catch (error) {
        console.error('Error updating crypto rates:', error);
    }
}

// Обновление баланса пользователя
function updateBalance(newBalance) {
    const balanceElements = document.querySelectorAll('.user-balance, .balance-badge');
    
    balanceElements.forEach(element => {
        const currentBalance = parseFloat(element.textContent.replace(/[^\d.]/g, '')) || 0;
        
        if (Math.abs(currentBalance - newBalance) > 0.01) {
            // Анимация изменения баланса
            element.classList.add('animate-pulse');
            
            setTimeout(() => {
                element.textContent = formatMoney(newBalance);
                element.classList.remove('animate-pulse');
                
                // Показываем уведомление о изменении баланса
                if (newBalance > currentBalance) {
                    showBalanceNotification(newBalance - currentBalance, 'increase');
                }
            }, 300);
        }
    });
}

// Обновление дневной прибыли
function updateDailyProfit(dailyProfit) {
    const dailyProfitElement = document.getElementById('daily-profit');
    if (dailyProfitElement) {
        const currentProfit = parseFloat(dailyProfitElement.textContent.replace(/[^\d.]/g, '')) || 0;
        
        if (Math.abs(currentProfit - dailyProfit) > 0.01) {
            dailyProfitElement.classList.add('animate-pulse');
            setTimeout(() => {
                dailyProfitElement.textContent = formatMoney(dailyProfit);
                dailyProfitElement.classList.remove('animate-pulse');
            }, 300);
        }
    }
}

// Обновление общей прибыли
function updateTotalProfit(totalProfit) {
    const totalProfitElement = document.getElementById('total-profit');
    if (totalProfitElement) {
        const currentProfit = parseFloat(totalProfitElement.textContent.replace(/[^\d.]/g, '')) || 0;
        
        if (Math.abs(currentProfit - totalProfit) > 0.01) {
            animateNumberUpdate(totalProfitElement, totalProfit, '$');
        }
    }
}

// Обновление активных инвестиций
function updateActiveInvestments(investments) {
    const investmentsContainer = document.getElementById('active-investments');
    if (!investmentsContainer || !investments) return;
    
    // Обновляем каждую инвестицию
    investments.forEach(investment => {
        const investmentElement = document.querySelector(`[data-investment-id="${investment.id}"]`);
        if (investmentElement) {
            // Обновляем прогресс
            const progressBar = investmentElement.querySelector('.progress-bar');
            if (progressBar) {
                progressBar.style.width = investment.progress + '%';
            }
            
            // Обновляем текущую прибыль
            const currentProfitElement = investmentElement.querySelector('.current-profit');
            if (currentProfitElement) {
                currentProfitElement.textContent = formatMoney(investment.current_profit);
            }
            
            // Обновляем оставшееся время
            const timeLeftElement = investmentElement.querySelector('.time-left');
            if (timeLeftElement && investment.time_left) {
                timeLeftElement.textContent = formatTimeLeft(investment.time_left);
            }
        }
    });
}

// Обновление статистики рефералов
function updateReferralStats(referralStats) {
    if (!referralStats) return;
    
    // Обновляем количество рефералов
    const referralCountElement = document.getElementById('referral-count');
    if (referralCountElement) {
        animateNumberUpdate(referralCountElement, referralStats.total_referrals);
    }
    
    // Обновляем заработок с рефералов
    const referralEarningsElement = document.getElementById('referral-earnings');
    if (referralEarningsElement) {
        animateNumberUpdate(referralEarningsElement, referralStats.total_earnings, '$');
    }
}

// Обновление прогресса заданий
function updateTaskProgress(taskProgress) {
    if (!taskProgress) return;
    
    taskProgress.forEach(task => {
        const taskElement = document.querySelector(`[data-task-id="${task.id}"]`);
        if (taskElement) {
            const progressBar = taskElement.querySelector('.progress-bar');
            const progressText = taskElement.querySelector('.progress-text');
            
            if (progressBar) {
                progressBar.style.width = task.progress + '%';
            }
            
            if (progressText) {
                progressText.textContent = `${task.current}/${task.target}`;
            }
            
            // Если задание выполнено, показываем уведомление
            if (task.completed && !taskElement.classList.contains('completed')) {
                taskElement.classList.add('completed');
                showNotification(`Задание "${task.title}" выполнено! Получена награда: ${formatMoney(task.reward)}`, 'success');
            }
        }
    });
}

// Обновление статистики проектов на карте
function updateMapStats(projectStats) {
    if (!projectStats || typeof updateMapMarkers !== 'function') return;
    
    // Обновляем маркеры на карте (функция определена в map.js)
    updateMapMarkers(projectStats);
}

// Обновление лидерборда
function updateLeaderboard(leaderboard) {
    const leaderboardContainer = document.getElementById('leaderboard-list');
    if (!leaderboardContainer || !leaderboard) return;
    
    // Обновляем позиции в лидерборде
    leaderboard.forEach((user, index) => {
        const userElement = leaderboardContainer.querySelector(`[data-user-id="${user.id}"]`);
        if (userElement) {
            const positionElement = userElement.querySelector('.position');
            const amountElement = userElement.querySelector('.amount');
            
            if (positionElement) {
                positionElement.textContent = index + 1;
            }
            
            if (amountElement) {
                amountElement.textContent = formatMoney(user.total_invested);
            }
        }
    });
}

// Обновление отображения криптовалют
function updateCryptoDisplay(rates) {
    Object.keys(rates).forEach(currency => {
        const rateElement = document.getElementById(`rate-${currency.toLowerCase()}`);
        if (rateElement) {
            const currentRate = parseFloat(rateElement.textContent.replace(/[^\d.]/g, '')) || 0;
            const newRate = rates[currency];
            
            if (Math.abs(currentRate - newRate) > 0.01) {
                rateElement.textContent = formatMoney(newRate);
                
                // Добавляем класс для анимации изменения
                if (newRate > currentRate) {
                    rateElement.classList.add('rate-up');
                } else {
                    rateElement.classList.add('rate-down');
                }
                
                setTimeout(() => {
                    rateElement.classList.remove('rate-up', 'rate-down');
                }, 2000);
            }
        }
    });
}

// Анимированное обновление числа
function animateNumberUpdate(element, targetValue, prefix = '') {
    const currentValue = parseFloat(element.textContent.replace(/[^\d.]/g, '')) || 0;
    
    if (Math.abs(currentValue - targetValue) < 0.01) return;
    
    const duration = 1000;
    const startTime = Date.now();
    const startValue = currentValue;
    
    function updateNumber() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentNumber = startValue + (targetValue - startValue) * easeOutQuart;
        
        element.textContent = prefix + currentNumber.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    updateNumber();
}

// Показ уведомления об изменении баланса
function showBalanceNotification(amount, type) {
    const message = type === 'increase' 
        ? `Баланс пополнен на ${formatMoney(amount)}` 
        : `Списано с баланса ${formatMoney(amount)}`;
    
    const notificationType = type === 'increase' ? 'success' : 'info';
    
    showNotification(message, notificationType);
}

// Форматирование оставшегося времени
function formatTimeLeft(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
        return `${days}д ${hours}ч`;
    } else if (hours > 0) {
        return `${hours}ч ${minutes}м`;
    } else {
        return `${minutes}м`;
    }
}

// Обработка ошибок обновления
function handleUpdateError() {
    retryCount++;
    
    if (retryCount >= maxRetries) {
        console.warn('Max retries reached, stopping real-time updates');
        stopRealTimeUpdates();
        
        // Показываем уведомление пользователю
        showNotification('Потеряно соединение с сервером. Обновите страницу.', 'warning');
        
        // Пытаемся восстановить соединение через 5 минут
        setTimeout(() => {
            retryCount = 0;
            startRealTimeUpdates();
        }, 300000);
    } else {
        console.warn(`Update failed, retry ${retryCount}/${maxRetries}`);
        
        // Увеличиваем интервал обновления при ошибках
        updateFrequency = Math.min(updateFrequency * 1.5, 120000); // максимум 2 минуты
    }
}

// Восстановление нормального интервала обновления
function resetUpdateFrequency() {
    updateFrequency = 30000; // возвращаем к 30 секундам
    retryCount = 0;
}

// Обработка видимости страницы
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // Страница скрыта, замедляем обновления
        updateFrequency = 120000; // 2 минуты
    } else {
        // Страница видима, возвращаем нормальную частоту
        resetUpdateFrequency();
        
        // Немедленное обновление при возвращении на страницу
        if (isLoggedIn && !isUpdating) {
            updateUserData();
        }
    }
});

// Экспорт функций
window.startRealTimeUpdates = startRealTimeUpdates;
window.stopRealTimeUpdates = stopRealTimeUpdates;
window.updateUserData = updateUserData;
