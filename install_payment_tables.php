<?php
// Скрипт для установки таблиц платежной системы
// GreenChain EcoFund - Payment System Installation

require_once 'config/config.php';

try {
    echo "<h2>🚀 Установка таблиц платежной системы GreenChain EcoFund</h2>\n";
    echo "<pre>\n";

    // Создание таблицы заявок на пополнение
    echo "📋 Создание таблицы payment_requests...\n";
    $sql = "CREATE TABLE IF NOT EXISTS payment_requests (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        currency VARCHAR(10) DEFAULT 'USDT',
        network VARCHAR(20) DEFAULT 'TRC-20',
        transaction_hash VARCHAR(100),
        wallet_address VARCHAR(100),
        status ENUM('pending', 'approved', 'rejected', 'cancelled') DEFAULT 'pending',
        admin_comment TEXT,
        admin_id INT,
        processed_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_payment_user (user_id),
        INDEX idx_payment_status (status),
        INDEX idx_payment_date (created_at),
        INDEX idx_payment_hash (transaction_hash)
    )";
    
    if ($conn->exec($sql) !== false) {
        echo "✅ Таблица payment_requests создана успешно\n";
    } else {
        echo "❌ Ошибка создания payment_requests: " . implode(", ", $conn->errorInfo()) . "\n";
    }

    // Создание таблицы заявок на вывод
    echo "\n📋 Создание таблицы withdrawal_requests...\n";
    $sql = "CREATE TABLE IF NOT EXISTS withdrawal_requests (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        currency VARCHAR(10) DEFAULT 'USDT',
        network VARCHAR(20) DEFAULT 'TRC-20',
        wallet_address VARCHAR(100) NOT NULL,
        transaction_hash VARCHAR(100),
        fee_amount DECIMAL(15,2) DEFAULT 0.00,
        final_amount DECIMAL(15,2) NOT NULL,
        status ENUM('pending', 'approved', 'rejected', 'completed', 'cancelled') DEFAULT 'pending',
        admin_comment TEXT,
        admin_id INT,
        processed_at TIMESTAMP NULL,
        completed_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_withdrawal_user (user_id),
        INDEX idx_withdrawal_status (status),
        INDEX idx_withdrawal_date (created_at),
        INDEX idx_withdrawal_wallet (wallet_address),
        INDEX idx_withdrawal_hash (transaction_hash)
    )";
    
    if ($conn->exec($sql) !== false) {
        echo "✅ Таблица withdrawal_requests создана успешно\n";
    } else {
        echo "❌ Ошибка создания withdrawal_requests: " . implode(", ", $conn->errorInfo()) . "\n";
    }

    // Создание таблицы настроек если не существует
    echo "\n📋 Проверка таблицы settings...\n";
    $sql = "CREATE TABLE IF NOT EXISTS settings (
        id INT PRIMARY KEY AUTO_INCREMENT,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_setting_key (setting_key)
    )";
    
    if ($conn->exec($sql) !== false) {
        echo "✅ Таблица settings проверена/создана\n";
    } else {
        echo "❌ Ошибка с таблицей settings: " . implode(", ", $conn->errorInfo()) . "\n";
    }

    // Добавление настроек платежной системы
    echo "\n⚙️ Добавление настроек платежной системы...\n";
    $settings = [
        ['payment_usdt_wallet', 'TYourUSDTWalletAddressHere', 'USDT TRC-20 кошелек для пополнений'],
        ['payment_min_deposit', '10.00', 'Минимальная сумма пополнения в USDT'],
        ['payment_max_deposit', '10000.00', 'Максимальная сумма пополнения в USDT'],
        ['payment_min_withdrawal', '20.00', 'Минимальная сумма вывода в USDT'],
        ['payment_max_withdrawal', '5000.00', 'Максимальная сумма вывода в USDT'],
        ['payment_withdrawal_fee', '2.00', 'Комиссия за вывод в USDT'],
        ['payment_auto_approve_limit', '100.00', 'Лимит для автоматического одобрения пополнений'],
        ['payment_daily_withdrawal_limit', '1000.00', 'Дневной лимит вывода на пользователя'],
        ['payment_system_enabled', '1', 'Включена ли платежная система (1 - да, 0 - нет)']
    ];

    $stmt = $conn->prepare("INSERT IGNORE INTO settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
    
    foreach ($settings as $setting) {
        if ($stmt->execute($setting)) {
            echo "✅ Настройка {$setting[0]} добавлена\n";
        } else {
            echo "⚠️ Настройка {$setting[0]} уже существует или ошибка\n";
        }
    }

    echo "\n🎉 Установка завершена успешно!\n";
    echo "\n📊 Проверка созданных таблиц:\n";
    
    // Проверка таблиц
    $tables = ['payment_requests', 'withdrawal_requests', 'settings'];
    foreach ($tables as $table) {
        $stmt = $conn->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Таблица $table существует\n";
            
            // Подсчет записей
            $count_stmt = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $count_stmt->fetch()['count'];
            echo "   📊 Записей в таблице: $count\n";
        } else {
            echo "❌ Таблица $table НЕ найдена\n";
        }
    }

    echo "\n</pre>";
    echo "<p><strong>✅ Платежная система готова к использованию!</strong></p>";
    echo "<p><a href='index.php' class='btn btn-primary'>Вернуться на сайт</a></p>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h3>❌ Ошибка установки:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
