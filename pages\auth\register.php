<?php
$page_title = "Регистрация";

// Если пользователь уже авторизован, перенаправляем в личный кабинет
if (isLoggedIn()) {
    redirect('index.php?page=dashboard');
}

// Обработка формы регистрации
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $result = processRegistration();
    if ($result['success']) {
        redirect('index.php?page=login', $result['message'], 'success');
    } else {
        $error_message = $result['message'];
    }
}

// Получение реферального кода из URL
$referral_code = $_GET['ref'] ?? '';
?>

<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="auth-card animate-fadeInUp">
                    <div class="auth-header">
                        <h3 class="mb-0">
                            <i class="fas fa-user-plus"></i> Регистрация
                        </h3>
                        <p class="mb-0 mt-2">Присоединяйтесь к GreenChain EcoFund</p>
                    </div>

                    <div class="auth-body">
                    <?php if (isset($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="first_name" class="form-label">
                                        <i class="fas fa-user me-2"></i>Имя *
                                    </label>
                                    <input type="text" class="form-control" id="first_name" name="first_name"
                                           value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>"
                                           placeholder="Введите имя"
                                           required minlength="2" maxlength="50">
                                    <div class="invalid-feedback">
                                        Введите ваше имя (минимум 2 символа)
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="last_name" class="form-label">
                                        <i class="fas fa-user me-2"></i>Фамилия *
                                    </label>
                                    <input type="text" class="form-control" id="last_name" name="last_name"
                                           value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>"
                                           placeholder="Введите фамилию"
                                           required minlength="2" maxlength="50">
                                    <div class="invalid-feedback">
                                        Введите вашу фамилию (минимум 2 символа)
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="username" class="form-label">
                                <i class="fas fa-at me-2"></i>Имя пользователя *
                            </label>
                            <input type="text" class="form-control" id="username" name="username"
                                   value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                   placeholder="Введите имя пользователя"
                                   required minlength="3" maxlength="50" pattern="[a-zA-Z0-9_]+">
                            <small class="form-text text-muted">Только латинские буквы, цифры и подчеркивание</small>
                            <div class="invalid-feedback">
                                Имя пользователя должно содержать 3-50 символов (только латинские буквы, цифры и _)
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>Email *
                            </label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                   placeholder="Введите email адрес"
                                   required>
                            <div class="invalid-feedback">
                                Введите корректный email адрес
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone me-2"></i>Телефон
                            </label>
                            <input type="tel" class="form-control phone-input" id="phone" name="phone"
                                   value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                                   placeholder="+7 (999) 123-45-67">
                            <small class="form-text text-muted">Необязательно, но рекомендуется для безопасности</small>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="country" class="form-label">Страна</label>
                                <select class="form-select" id="country" name="country">
                                    <option value="">Выберите страну</option>
                                    <option value="RU" <?php echo ($_POST['country'] ?? '') === 'RU' ? 'selected' : ''; ?>>Россия</option>
                                    <option value="UA" <?php echo ($_POST['country'] ?? '') === 'UA' ? 'selected' : ''; ?>>Украина</option>
                                    <option value="BY" <?php echo ($_POST['country'] ?? '') === 'BY' ? 'selected' : ''; ?>>Беларусь</option>
                                    <option value="KZ" <?php echo ($_POST['country'] ?? '') === 'KZ' ? 'selected' : ''; ?>>Казахстан</option>
                                    <option value="US" <?php echo ($_POST['country'] ?? '') === 'US' ? 'selected' : ''; ?>>США</option>
                                    <option value="DE" <?php echo ($_POST['country'] ?? '') === 'DE' ? 'selected' : ''; ?>>Германия</option>
                                    <option value="OTHER" <?php echo ($_POST['country'] ?? '') === 'OTHER' ? 'selected' : ''; ?>>Другая</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="city" class="form-label">Город</label>
                                <input type="text" class="form-control" id="city" name="city" 
                                       value="<?php echo htmlspecialchars($_POST['city'] ?? ''); ?>" 
                                       maxlength="100">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Пароль *</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" 
                                       required minlength="8">
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                Минимум 8 символов, должен содержать заглавные и строчные буквы, цифры
                            </div>
                            <div class="invalid-feedback">
                                Пароль должен содержать минимум 8 символов, включая заглавные и строчные буквы, цифры
                            </div>
                            <div class="password-strength mt-2">
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar" id="passwordStrength" style="width: 0%"></div>
                                </div>
                                <small class="text-muted" id="passwordStrengthText"></small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password_confirm" class="form-label">Подтверждение пароля *</label>
                            <input type="password" class="form-control" id="password_confirm" name="password_confirm" 
                                   required>
                            <div class="invalid-feedback">
                                Пароли не совпадают
                            </div>
                        </div>
                        
                        <?php if ($referral_code): ?>
                            <div class="mb-3">
                                <label for="referral_code" class="form-label">Реферальный код</label>
                                <input type="text" class="form-control" id="referral_code" name="referral_code" 
                                       value="<?php echo htmlspecialchars($referral_code); ?>" readonly>
                                <div class="form-text text-success">
                                    <i class="fas fa-gift"></i> Вы получите бонус за регистрацию по реферальной ссылке!
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="agree_terms" name="agree_terms" required>
                            <label class="form-check-label" for="agree_terms">
                                Я согласен с <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">условиями использования</a> 
                                и <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">политикой конфиденциальности</a> *
                            </label>
                            <div class="invalid-feedback">
                                Необходимо согласиться с условиями использования
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="subscribe_news" name="subscribe_news" checked>
                            <label class="form-check-label" for="subscribe_news">
                                Получать новости и обновления на email
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus"></i> Зарегистрироваться
                            </button>
                        </div>
                    </form>
                    
                    </div>

                    <div class="auth-footer">
                        <p class="mb-2">Уже есть аккаунт?</p>
                        <a href="index.php?page=login" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt"></i> Войти
                        </a>

                        <div class="mt-3">
                            <div class="row text-center">
                                <div class="col-4">
                                    <i class="fas fa-gift text-success"></i>
                                    <small class="d-block">Бонус $5</small>
                                </div>
                                <div class="col-4">
                                    <i class="fas fa-chart-line text-primary"></i>
                                    <small class="d-block">До 1.2%/день</small>
                                </div>
                                <div class="col-4">
                                    <i class="fas fa-shield-alt text-info"></i>
                                    <small class="d-block">Безопасность</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.feature-icon-small {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(46, 139, 87, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
}

.password-strength .progress-bar {
    transition: all 0.3s ease;
}

.password-strength .progress-bar.weak {
    background-color: #dc3545;
}

.password-strength .progress-bar.medium {
    background-color: #ffc107;
}

.password-strength .progress-bar.strong {
    background-color: #28a745;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Переключение видимости пароля
    document.getElementById('togglePassword').addEventListener('click', function() {
        const password = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (password.type === 'password') {
            password.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            password.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    
    // Проверка силы пароля
    document.getElementById('password').addEventListener('input', function() {
        const password = this.value;
        const strengthBar = document.getElementById('passwordStrength');
        const strengthText = document.getElementById('passwordStrengthText');
        
        let strength = 0;
        let text = '';
        
        if (password.length >= 8) strength += 25;
        if (/[a-z]/.test(password)) strength += 25;
        if (/[A-Z]/.test(password)) strength += 25;
        if (/[0-9]/.test(password)) strength += 25;
        
        strengthBar.style.width = strength + '%';
        
        if (strength < 50) {
            strengthBar.className = 'progress-bar weak';
            text = 'Слабый пароль';
        } else if (strength < 100) {
            strengthBar.className = 'progress-bar medium';
            text = 'Средний пароль';
        } else {
            strengthBar.className = 'progress-bar strong';
            text = 'Сильный пароль';
        }
        
        strengthText.textContent = text;
    });
    
    // Проверка совпадения паролей
    document.getElementById('password_confirm').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;
        
        if (password !== confirmPassword) {
            this.setCustomValidity('Пароли не совпадают');
        } else {
            this.setCustomValidity('');
        }
    });
});
</script>

<?php
/**
 * Обработка регистрации
 */
function processRegistration() {
    global $conn;
    
    // Проверка CSRF токена
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        return ['success' => false, 'message' => 'Ошибка безопасности. Попробуйте еще раз.'];
    }
    
    // Получение и валидация данных
    $first_name = sanitizeInput($_POST['first_name']);
    $last_name = sanitizeInput($_POST['last_name']);
    $username = sanitizeInput($_POST['username']);
    $email = sanitizeInput($_POST['email']);
    $phone = sanitizeInput($_POST['phone']);
    $country = sanitizeInput($_POST['country']);
    $city = sanitizeInput($_POST['city']);
    $password = $_POST['password'];
    $password_confirm = $_POST['password_confirm'];
    $referral_code = sanitizeInput($_POST['referral_code'] ?? '');
    $agree_terms = isset($_POST['agree_terms']);
    $subscribe_news = isset($_POST['subscribe_news']);
    
    // Валидация
    $validation = validateRegistrationData($first_name, $last_name, $username, $email, $password, $password_confirm, $agree_terms);
    if (!$validation['success']) {
        return $validation;
    }
    
    try {
        $conn->beginTransaction();
        
        // Проверка уникальности username и email
        $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
        $stmt->execute([$username, $email]);
        if ($stmt->fetch()) {
            $conn->rollBack();
            return ['success' => false, 'message' => 'Пользователь с таким именем или email уже существует'];
        }
        
        // Проверка реферального кода
        $referrer_id = null;
        if ($referral_code) {
            $stmt = $conn->prepare("SELECT id FROM users WHERE referral_code = ? AND status = 'active'");
            $stmt->execute([$referral_code]);
            $referrer = $stmt->fetch();
            if ($referrer) {
                $referrer_id = $referrer['id'];
            }
        }
        
        // Хеширование пароля
        $password_hash = hashPassword($password);
        
        // Генерация токена подтверждения email
        $email_verification_token = generateToken();
        
        // Создание пользователя
        $stmt = $conn->prepare("
            INSERT INTO users (username, email, password_hash, first_name, last_name, phone, country, city, 
                              referrer_id, email_verification_token, balance, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $initial_balance = $referrer_id ? 5.00 : 0.00; // Бонус за регистрацию по реферальной ссылке
        
        $stmt->execute([
            $username, $email, $password_hash, $first_name, $last_name, 
            $phone, $country, $city, $referrer_id, $email_verification_token, $initial_balance
        ]);
        
        $user_id = $conn->lastInsertId();
        
        // Создание записи реферала
        if ($referrer_id) {
            $stmt = $conn->prepare("
                INSERT INTO referrals (referrer_id, referred_id, level, bonus_rate) 
                VALUES (?, ?, 1, ?)
            ");
            $stmt->execute([$referrer_id, $user_id, REFERRAL_BONUS_LEVEL_1]);
            
            // Создание транзакции бонуса
            if ($initial_balance > 0) {
                $stmt = $conn->prepare("
                    INSERT INTO transactions (user_id, type, amount, balance_before, balance_after, description) 
                    VALUES (?, 'deposit', ?, 0, ?, 'Бонус за регистрацию по реферальной ссылке')
                ");
                $stmt->execute([$user_id, $initial_balance, $initial_balance]);
            }
        }
        
        // Отправка email подтверждения
        sendVerificationEmail($email, $first_name, $email_verification_token);
        
        // Логирование
        logAction('user_registered', "User ID: $user_id, Email: $email");
        
        $conn->commit();
        
        return [
            'success' => true, 
            'message' => 'Регистрация успешна! Проверьте email для подтверждения аккаунта.'
        ];
        
    } catch (Exception $e) {
        $conn->rollBack();
        error_log("Registration error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при регистрации. Попробуйте позже.'];
    }
}

/**
 * Валидация данных регистрации
 */
function validateRegistrationData($first_name, $last_name, $username, $email, $password, $password_confirm, $agree_terms) {
    if (empty($first_name) || strlen($first_name) < 2) {
        return ['success' => false, 'message' => 'Имя должно содержать минимум 2 символа'];
    }
    
    if (empty($last_name) || strlen($last_name) < 2) {
        return ['success' => false, 'message' => 'Фамилия должна содержать минимум 2 символа'];
    }
    
    if (empty($username) || strlen($username) < 3 || !preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
        return ['success' => false, 'message' => 'Имя пользователя должно содержать 3-50 символов (только латинские буквы, цифры и _)'];
    }
    
    if (!validateEmail($email)) {
        return ['success' => false, 'message' => 'Некорректный email адрес'];
    }
    
    if (!validatePassword($password)) {
        return ['success' => false, 'message' => 'Пароль должен содержать минимум 8 символов, включая заглавные и строчные буквы, цифры'];
    }
    
    if ($password !== $password_confirm) {
        return ['success' => false, 'message' => 'Пароли не совпадают'];
    }
    
    if (!$agree_terms) {
        return ['success' => false, 'message' => 'Необходимо согласиться с условиями использования'];
    }
    
    return ['success' => true];
}

/**
 * Отправка email подтверждения
 */
function sendVerificationEmail($email, $name, $token) {
    $subject = 'Подтверждение регистрации - GreenChain EcoFund';
    $verification_link = SITE_URL . "/index.php?page=verify&token=" . $token;
    
    $message = "
    <h2>Добро пожаловать в GreenChain EcoFund!</h2>
    <p>Здравствуйте, {$name}!</p>
    <p>Спасибо за регистрацию на нашей платформе. Для завершения регистрации, пожалуйста, подтвердите ваш email адрес:</p>
    <p><a href='{$verification_link}' style='background: #2E8B57; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Подтвердить email</a></p>
    <p>Если кнопка не работает, скопируйте и вставьте эту ссылку в браузер:</p>
    <p>{$verification_link}</p>
    <p>С уважением,<br>Команда GreenChain EcoFund</p>
    ";
    
    return sendEmail($email, $subject, $message, true);
}
?>
