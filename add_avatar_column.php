<?php
// Добавление колонки avatar в таблицу users
require_once 'config/config.php';

echo "<h1>Добавление колонки avatar</h1>";

try {
    // Проверяем, есть ли уже колонка avatar
    $stmt = $conn->query("SHOW COLUMNS FROM users LIKE 'avatar'");
    if ($stmt->rowCount() == 0) {
        // Добавляем колонку avatar
        $conn->exec("ALTER TABLE users ADD COLUMN avatar VARCHAR(255) NULL AFTER email");
        echo "✅ Колонка avatar добавлена в таблицу users<br>";
    } else {
        echo "ℹ️ Колонка avatar уже существует<br>";
    }
    
    // Создаем папку для аватаров если её нет
    $upload_dir = 'uploads/avatars/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
        echo "✅ Папка uploads/avatars/ создана<br>";
    } else {
        echo "ℹ️ Папка uploads/avatars/ уже существует<br>";
    }
    
    // Проверяем права на запись
    if (is_writable($upload_dir)) {
        echo "✅ Папка uploads/avatars/ доступна для записи<br>";
    } else {
        echo "❌ Папка uploads/avatars/ недоступна для записи<br>";
        // Пытаемся исправить права
        chmod($upload_dir, 0755);
        if (is_writable($upload_dir)) {
            echo "✅ Права на папку исправлены<br>";
        } else {
            echo "❌ Не удалось исправить права на папку<br>";
        }
    }
    
    // Создаем файл default-avatar.png если его нет
    $default_avatar = 'assets/images/default-avatar.png';
    if (!file_exists($default_avatar)) {
        // Создаем простой SVG аватар
        $svg_content = '<?xml version="1.0" encoding="UTF-8"?>
<svg width="150" height="150" viewBox="0 0 150 150" xmlns="http://www.w3.org/2000/svg">
    <circle cx="75" cy="75" r="75" fill="#e9ecef"/>
    <circle cx="75" cy="60" r="25" fill="#6c757d"/>
    <ellipse cx="75" cy="130" rx="35" ry="25" fill="#6c757d"/>
</svg>';
        
        // Создаем папку assets/images если её нет
        if (!is_dir('assets/images')) {
            mkdir('assets/images', 0755, true);
        }
        
        file_put_contents('assets/images/default-avatar.svg', $svg_content);
        echo "✅ Создан default-avatar.svg<br>";
    } else {
        echo "ℹ️ Файл default-avatar.png уже существует<br>";
    }
    
    echo "<h2>✅ Настройка загрузки аватаров завершена!</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ Критическая ошибка: " . $e->getMessage() . "</h2>";
}

echo "<p><a href='index.php'>← Вернуться на главную</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #2c3e50; }
h2 { color: #34495e; border-bottom: 2px solid #ecf0f1; padding-bottom: 5px; }
</style>
