// Service Worker для <PERSON><PERSON>n EcoFund PWA
const CACHE_NAME = 'greenchain-v1.0.0';
const STATIC_CACHE = 'greenchain-static-v1.0.0';
const DYNAMIC_CACHE = 'greenchain-dynamic-v1.0.0';

// Файлы для кэширования
const STATIC_FILES = [
    '/',
    '/index.php',
    '/assets/css/style.css',
    '/assets/css/mobile.css',
    '/assets/js/main.js',
    '/assets/js/realtime.js',
    '/assets/images/logo.png',
    '/assets/images/default-avatar.png',
    '/assets/images/default-article.jpg',
    '/manifest.json',
    // Bootstrap и другие библиотеки
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    'https://cdn.jsdelivr.net/npm/chart.js',
    // Шрифты
    'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'
];

// Страницы для кэширования
const CACHE_PAGES = [
    '/index.php?page=dashboard',
    '/index.php?page=invest',
    '/index.php?page=calculator',
    '/index.php?page=map',
    '/index.php?page=education',
    '/index.php?page=support',
    '/index.php?page=profile'
];

// API endpoints для кэширования
const CACHE_API = [
    '/api/user-stats.php',
    '/api/profit-chart.php',
    '/api/project-details.php'
];

// Установка Service Worker
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        Promise.all([
            // Кэширование статических файлов
            caches.open(STATIC_CACHE).then(cache => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_FILES);
            }),
            // Предварительное кэширование страниц
            caches.open(DYNAMIC_CACHE).then(cache => {
                console.log('Service Worker: Pre-caching pages');
                return cache.addAll(CACHE_PAGES);
            })
        ]).then(() => {
            console.log('Service Worker: Installation complete');
            return self.skipWaiting();
        })
    );
});

// Активация Service Worker
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    // Удаляем старые кэши
                    if (cacheName !== STATIC_CACHE && 
                        cacheName !== DYNAMIC_CACHE && 
                        cacheName !== CACHE_NAME) {
                        console.log('Service Worker: Deleting old cache', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('Service Worker: Activation complete');
            return self.clients.claim();
        })
    );
});

// Обработка запросов
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Игнорируем запросы к другим доменам (кроме CDN)
    if (url.origin !== location.origin && !isCDNRequest(url)) {
        return;
    }
    
    // Стратегия кэширования в зависимости от типа запроса
    if (isStaticFile(request)) {
        // Cache First для статических файлов
        event.respondWith(cacheFirst(request));
    } else if (isAPIRequest(request)) {
        // Network First для API запросов
        event.respondWith(networkFirst(request));
    } else if (isPageRequest(request)) {
        // Stale While Revalidate для страниц
        event.respondWith(staleWhileRevalidate(request));
    } else {
        // Network First по умолчанию
        event.respondWith(networkFirst(request));
    }
});

// Стратегия Cache First
async function cacheFirst(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.error('Cache First failed:', error);
        return new Response('Offline', { status: 503 });
    }
}

// Стратегия Network First
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.log('Network failed, trying cache:', error);
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Возвращаем офлайн страницу для HTML запросов
        if (request.headers.get('accept').includes('text/html')) {
            return caches.match('/offline.html') || 
                   new Response('Offline', { status: 503 });
        }
        
        return new Response('Offline', { status: 503 });
    }
}

// Стратегия Stale While Revalidate
async function staleWhileRevalidate(request) {
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    const fetchPromise = fetch(request).then(networkResponse => {
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    }).catch(() => cachedResponse);
    
    return cachedResponse || fetchPromise;
}

// Проверка типов запросов
function isStaticFile(request) {
    const url = new URL(request.url);
    return url.pathname.match(/\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf)$/);
}

function isAPIRequest(request) {
    const url = new URL(request.url);
    return url.pathname.startsWith('/api/');
}

function isPageRequest(request) {
    return request.headers.get('accept').includes('text/html');
}

function isCDNRequest(url) {
    const cdnDomains = [
        'cdn.jsdelivr.net',
        'cdnjs.cloudflare.com',
        'fonts.googleapis.com',
        'fonts.gstatic.com'
    ];
    return cdnDomains.some(domain => url.hostname.includes(domain));
}

// Обработка push уведомлений
self.addEventListener('push', event => {
    console.log('Push notification received');
    
    let data = {};
    if (event.data) {
        try {
            data = event.data.json();
        } catch (e) {
            data = { title: 'GreenChain', body: event.data.text() };
        }
    }
    
    const options = {
        title: data.title || 'GreenChain EcoFund',
        body: data.body || 'У вас новое уведомление',
        icon: '/assets/images/icons/icon-192x192.png',
        badge: '/assets/images/icons/badge-72x72.png',
        image: data.image,
        data: data.url || '/',
        actions: [
            {
                action: 'open',
                title: 'Открыть',
                icon: '/assets/images/icons/open-24x24.png'
            },
            {
                action: 'close',
                title: 'Закрыть',
                icon: '/assets/images/icons/close-24x24.png'
            }
        ],
        tag: data.tag || 'general',
        renotify: true,
        requireInteraction: data.requireInteraction || false,
        silent: false,
        vibrate: [200, 100, 200],
        timestamp: Date.now()
    };
    
    event.waitUntil(
        self.registration.showNotification(options.title, options)
    );
});

// Обработка кликов по уведомлениям
self.addEventListener('notificationclick', event => {
    console.log('Notification clicked');
    
    event.notification.close();
    
    if (event.action === 'close') {
        return;
    }
    
    const urlToOpen = event.notification.data || '/';
    
    event.waitUntil(
        clients.matchAll({ type: 'window', includeUncontrolled: true })
            .then(clientList => {
                // Проверяем, есть ли уже открытое окно
                for (const client of clientList) {
                    if (client.url === urlToOpen && 'focus' in client) {
                        return client.focus();
                    }
                }
                
                // Открываем новое окно
                if (clients.openWindow) {
                    return clients.openWindow(urlToOpen);
                }
            })
    );
});

// Обработка фоновой синхронизации
self.addEventListener('sync', event => {
    console.log('Background sync triggered:', event.tag);
    
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

// Фоновая синхронизация
async function doBackgroundSync() {
    try {
        // Синхронизация данных пользователя
        await syncUserData();
        
        // Синхронизация уведомлений
        await syncNotifications();
        
        console.log('Background sync completed');
    } catch (error) {
        console.error('Background sync failed:', error);
    }
}

async function syncUserData() {
    try {
        const response = await fetch('/api/sync-user-data.php', {
            method: 'POST',
            credentials: 'include'
        });
        
        if (response.ok) {
            const data = await response.json();
            // Обновляем кэш с новыми данными
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put('/api/user-stats.php', new Response(JSON.stringify(data)));
        }
    } catch (error) {
        console.error('User data sync failed:', error);
    }
}

async function syncNotifications() {
    try {
        const response = await fetch('/api/notifications.php', {
            credentials: 'include'
        });
        
        if (response.ok) {
            const notifications = await response.json();
            // Показываем новые уведомления
            notifications.forEach(notification => {
                if (notification.show_push) {
                    self.registration.showNotification(notification.title, {
                        body: notification.message,
                        icon: '/assets/images/icons/icon-192x192.png',
                        data: notification.url
                    });
                }
            });
        }
    } catch (error) {
        console.error('Notifications sync failed:', error);
    }
}

// Обработка сообщений от клиента
self.addEventListener('message', event => {
    console.log('Message received:', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({ version: CACHE_NAME });
    }
    
    if (event.data && event.data.type === 'CLEAR_CACHE') {
        clearAllCaches().then(() => {
            event.ports[0].postMessage({ success: true });
        });
    }
});

// Очистка всех кэшей
async function clearAllCaches() {
    const cacheNames = await caches.keys();
    await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
    );
    console.log('All caches cleared');
}

// Периодическая очистка старых кэшей
setInterval(() => {
    clearOldCaches();
}, 24 * 60 * 60 * 1000); // Каждые 24 часа

async function clearOldCaches() {
    try {
        const cacheNames = await caches.keys();
        const oldCaches = cacheNames.filter(name => 
            !name.includes('v1.0.0') && 
            name.startsWith('greenchain-')
        );
        
        await Promise.all(
            oldCaches.map(cacheName => {
                console.log('Deleting old cache:', cacheName);
                return caches.delete(cacheName);
            })
        );
    } catch (error) {
        console.error('Error clearing old caches:', error);
    }
}
