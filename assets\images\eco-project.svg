<svg width="400" height="250" viewBox="0 0 400 250" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E0F6FF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="solarGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="windGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#16844a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#22c55e;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Sky Background -->
  <rect width="400" height="150" fill="url(#skyGradient)"/>
  
  <!-- Mountains/Hills -->
  <path d="M0 120 Q100 80 200 100 T400 90 L400 150 L0 150 Z" fill="#16844a"/>
  <path d="M0 130 Q150 100 300 110 T400 105 L400 150 L0 150 Z" fill="#22c55e" opacity="0.7"/>
  
  <!-- Wind Turbines -->
  <g transform="translate(80, 60)">
    <!-- Tower -->
    <rect x="48" y="40" width="4" height="60" fill="#f0f0f0"/>
    <!-- Blades -->
    <g transform="translate(50, 40)">
      <ellipse cx="0" cy="-15" rx="2" ry="15" fill="#ffffff" transform="rotate(0)"/>
      <ellipse cx="13" cy="7.5" rx="2" ry="15" fill="#ffffff" transform="rotate(120)"/>
      <ellipse cx="-13" cy="7.5" rx="2" ry="15" fill="#ffffff" transform="rotate(240)"/>
      <circle cx="0" cy="0" r="3" fill="#e5e5e5"/>
    </g>
  </g>
  
  <g transform="translate(150, 70)">
    <!-- Tower -->
    <rect x="48" y="30" width="4" height="50" fill="#f0f0f0"/>
    <!-- Blades -->
    <g transform="translate(50, 30)">
      <ellipse cx="0" cy="-12" rx="2" ry="12" fill="#ffffff" transform="rotate(45)"/>
      <ellipse cx="8.5" cy="8.5" rx="2" ry="12" fill="#ffffff" transform="rotate(165)"/>
      <ellipse cx="-8.5" cy="8.5" rx="2" ry="12" fill="#ffffff" transform="rotate(285)"/>
      <circle cx="0" cy="0" r="2.5" fill="#e5e5e5"/>
    </g>
  </g>
  
  <g transform="translate(320, 65)">
    <!-- Tower -->
    <rect x="48" y="35" width="4" height="55" fill="#f0f0f0"/>
    <!-- Blades -->
    <g transform="translate(50, 35)">
      <ellipse cx="0" cy="-13" rx="2" ry="13" fill="#ffffff" transform="rotate(90)"/>
      <ellipse cx="11" cy="6.5" rx="2" ry="13" fill="#ffffff" transform="rotate(210)"/>
      <ellipse cx="-11" cy="6.5" rx="2" ry="13" fill="#ffffff" transform="rotate(330)"/>
      <circle cx="0" cy="0" r="3" fill="#e5e5e5"/>
    </g>
  </g>
  
  <!-- Solar Panels -->
  <g transform="translate(200, 110)">
    <rect x="0" y="0" width="80" height="30" fill="url(#solarGradient)" rx="2"/>
    <rect x="5" y="5" width="70" height="20" fill="#1e40af" opacity="0.8"/>
    <!-- Grid lines -->
    <line x1="20" y1="5" x2="20" y2="25" stroke="#ffffff" stroke-width="0.5" opacity="0.3"/>
    <line x1="40" y1="5" x2="40" y2="25" stroke="#ffffff" stroke-width="0.5" opacity="0.3"/>
    <line x1="60" y1="5" x2="60" y2="25" stroke="#ffffff" stroke-width="0.5" opacity="0.3"/>
    <line x1="5" y1="15" x2="75" y2="15" stroke="#ffffff" stroke-width="0.5" opacity="0.3"/>
  </g>
  
  <g transform="translate(120, 115)">
    <rect x="0" y="0" width="60" height="25" fill="url(#solarGradient)" rx="2"/>
    <rect x="3" y="3" width="54" height="19" fill="#1e40af" opacity="0.8"/>
    <!-- Grid lines -->
    <line x1="15" y1="3" x2="15" y2="22" stroke="#ffffff" stroke-width="0.5" opacity="0.3"/>
    <line x1="30" y1="3" x2="30" y2="22" stroke="#ffffff" stroke-width="0.5" opacity="0.3"/>
    <line x1="45" y1="3" x2="45" y2="22" stroke="#ffffff" stroke-width="0.5" opacity="0.3"/>
    <line x1="3" y1="12" x2="57" y2="12" stroke="#ffffff" stroke-width="0.5" opacity="0.3"/>
  </g>
  
  <!-- Trees -->
  <g transform="translate(30, 120)">
    <rect x="18" y="20" width="4" height="20" fill="#8B4513"/>
    <circle cx="20" cy="15" r="12" fill="#22c55e"/>
    <circle cx="20" cy="12" r="8" fill="#16844a"/>
  </g>
  
  <g transform="translate(350, 125)">
    <rect x="18" y="15" width="4" height="15" fill="#8B4513"/>
    <circle cx="20" cy="10" r="10" fill="#22c55e"/>
    <circle cx="20" cy="8" r="6" fill="#16844a"/>
  </g>
  
  <!-- Clouds -->
  <ellipse cx="80" cy="40" rx="20" ry="8" fill="#FFFFFF" opacity="0.8"/>
  <ellipse cx="95" cy="35" rx="15" ry="6" fill="#FFFFFF" opacity="0.8"/>
  <ellipse cx="320" cy="30" rx="25" ry="10" fill="#FFFFFF" opacity="0.8"/>
  <ellipse cx="340" cy="25" rx="18" ry="7" fill="#FFFFFF" opacity="0.8"/>
  
  <!-- Sun -->
  <circle cx="350" cy="50" r="15" fill="#FFD700" opacity="0.9"/>
  <!-- Sun rays -->
  <g transform="translate(350, 50)">
    <line x1="0" y1="-25" x2="0" y2="-20" stroke="#FFD700" stroke-width="2" opacity="0.7"/>
    <line x1="18" y1="-18" x2="14" y2="-14" stroke="#FFD700" stroke-width="2" opacity="0.7"/>
    <line x1="25" y1="0" x2="20" y2="0" stroke="#FFD700" stroke-width="2" opacity="0.7"/>
    <line x1="18" y1="18" x2="14" y2="14" stroke="#FFD700" stroke-width="2" opacity="0.7"/>
    <line x1="0" y1="25" x2="0" y2="20" stroke="#FFD700" stroke-width="2" opacity="0.7"/>
    <line x1="-18" y1="18" x2="-14" y2="14" stroke="#FFD700" stroke-width="2" opacity="0.7"/>
    <line x1="-25" y1="0" x2="-20" y2="0" stroke="#FFD700" stroke-width="2" opacity="0.7"/>
    <line x1="-18" y1="-18" x2="-14" y2="-14" stroke="#FFD700" stroke-width="2" opacity="0.7"/>
  </g>
  
  <!-- Ground/Grass -->
  <rect x="0" y="150" width="400" height="100" fill="#22c55e"/>
  
  <!-- Eco symbols -->
  <g transform="translate(50, 180)">
    <circle cx="0" cy="0" r="15" fill="#16844a" opacity="0.8"/>
    <text x="0" y="5" text-anchor="middle" fill="#ffffff" font-size="16" font-weight="bold">♻</text>
  </g>
  
  <g transform="translate(350, 190)">
    <circle cx="0" cy="0" r="12" fill="#16844a" opacity="0.8"/>
    <text x="0" y="4" text-anchor="middle" fill="#ffffff" font-size="14" font-weight="bold">🌱</text>
  </g>
  
  <!-- Energy flow lines -->
  <path d="M 100 100 Q 150 90 200 110" stroke="#2563eb" stroke-width="2" fill="none" opacity="0.6" stroke-dasharray="5,5"/>
  <path d="M 200 100 Q 250 85 300 95" stroke="#16844a" stroke-width="2" fill="none" opacity="0.6" stroke-dasharray="5,5"/>
</svg>
