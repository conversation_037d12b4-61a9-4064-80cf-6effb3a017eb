<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Проверка файлов</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h2>🔍 Проверка доступности файлов</h2>
    <div id="results"></div>

    <script>
        const results = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        // Проверяем доступность файлов
        const filesToCheck = [
            'assets/js/main.js',
            'assets/js/modern.js',
            'assets/js/bootstrap.bundle.min.js',
            'assets/js/jquery.min.js'
        ];
        
        addResult('🚀 Начинаем проверку файлов...');
        
        filesToCheck.forEach(file => {
            fetch(file)
                .then(response => {
                    if (response.ok) {
                        addResult(`✅ ${file} - доступен (${response.status})`, 'success');
                        return response.text();
                    } else {
                        addResult(`❌ ${file} - ошибка ${response.status}`, 'error');
                        return null;
                    }
                })
                .then(content => {
                    if (content && file === 'assets/js/main.js') {
                        // Проверяем содержимое main.js
                        if (content.includes('handleInvestClick')) {
                            addResult(`✅ main.js содержит handleInvestClick`, 'success');
                        } else {
                            addResult(`❌ main.js НЕ содержит handleInvestClick`, 'error');
                        }
                        
                        if (content.includes('window.handleInvestClick')) {
                            addResult(`✅ main.js экспортирует handleInvestClick`, 'success');
                        } else {
                            addResult(`❌ main.js НЕ экспортирует handleInvestClick`, 'error');
                        }
                        
                        // Проверяем размер файла
                        addResult(`📊 Размер main.js: ${content.length} символов`);
                    }
                })
                .catch(error => {
                    addResult(`❌ ${file} - ошибка загрузки: ${error.message}`, 'error');
                });
        });
        
        // Проверяем загрузку main.js через script tag
        setTimeout(() => {
            addResult('🔄 Проверяем загрузку через script tag...');
            
            const script = document.createElement('script');
            script.src = 'assets/js/main.js';
            script.onload = () => {
                addResult('✅ main.js загружен через script tag', 'success');
                
                // Проверяем доступность функций
                setTimeout(() => {
                    if (typeof window.handleInvestClick === 'function') {
                        addResult('✅ handleInvestClick доступна в window', 'success');
                    } else {
                        addResult('❌ handleInvestClick НЕ доступна в window', 'error');
                    }
                    
                    if (typeof window.showInvestModal === 'function') {
                        addResult('✅ showInvestModal доступна в window', 'success');
                    } else {
                        addResult('❌ showInvestModal НЕ доступна в window', 'error');
                    }
                }, 100);
            };
            script.onerror = () => {
                addResult('❌ Ошибка загрузки main.js через script tag', 'error');
            };
            
            document.head.appendChild(script);
        }, 2000);
    </script>
</body>
</html>
