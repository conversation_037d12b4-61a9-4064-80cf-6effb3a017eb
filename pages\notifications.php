<?php
$page_title = "Уведомления";

// Проверка авторизации
if (!isLoggedIn()) {
    redirect('index.php?page=login&redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

$user_id = $_SESSION['user_id'];

// Обработка действий
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'mark_read':
            $result = markNotificationAsRead($_POST['notification_id'], $user_id);
            break;
        case 'mark_all_read':
            $result = markAllNotificationsAsRead($user_id);
            break;
        case 'delete_notification':
            $result = deleteNotification($_POST['notification_id'], $user_id);
            break;
        case 'update_settings':
            $result = updateNotificationSettings($user_id);
            break;
        default:
            $result = ['success' => false, 'message' => 'Неизвестное действие'];
    }
    
    if ($result['success']) {
        redirect('index.php?page=notifications', $result['message'], 'success');
    } else {
        $error_message = $result['message'];
    }
}

// Получение уведомлений
$notifications = getUserNotifications($user_id);
$notification_stats = getNotificationStats($user_id);
$notification_settings = getNotificationSettings($user_id);
?>

<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="page-header mb-4">
                <h2 class="page-title">
                    <i class="fas fa-bell text-primary"></i> Уведомления
                </h2>
                <p class="page-subtitle">Управление уведомлениями и настройками</p>
            </div>
        </div>
    </div>
    
    <!-- Статистика уведомлений -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-primary">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo $notification_stats['total']; ?></div>
                    <div class="stats-label">Всего уведомлений</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-warning">
                    <i class="fas fa-envelope-open"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo $notification_stats['unread']; ?></div>
                    <div class="stats-label">Непрочитанных</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo $notification_stats['read']; ?></div>
                    <div class="stats-label">Прочитанных</div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-info">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-value"><?php echo $notification_stats['today']; ?></div>
                    <div class="stats-label">Сегодня</div>
                </div>
            </div>
        </div>
    </div>
    
    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>
    
    <div class="row">
        <!-- Список уведомлений -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list"></i> Уведомления
                    </h5>
                    <div class="notification-actions">
                        <?php if ($notification_stats['unread'] > 0): ?>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="mark_all_read">
                                <button type="submit" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-check-double"></i> Отметить все как прочитанные
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($notifications)): ?>
                        <div class="empty-state">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <h5>Уведомлений нет</h5>
                            <p class="text-muted">Здесь будут отображаться ваши уведомления</p>
                        </div>
                    <?php else: ?>
                        <div class="notifications-list">
                            <?php foreach ($notifications as $notification): ?>
                                <div class="notification-item <?php echo !$notification['is_read'] ? 'unread' : ''; ?>">
                                    <div class="notification-icon">
                                        <i class="<?php echo getNotificationIcon($notification['type']); ?>"></i>
                                    </div>
                                    
                                    <div class="notification-content">
                                        <div class="notification-header">
                                            <h6 class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></h6>
                                            <div class="notification-time">
                                                <?php echo timeAgo($notification['created_at']); ?>
                                            </div>
                                        </div>
                                        
                                        <p class="notification-message">
                                            <?php echo htmlspecialchars($notification['message']); ?>
                                        </p>
                                        
                                        <div class="notification-actions">
                                            <?php if (!$notification['is_read']): ?>
                                                <form method="POST" class="d-inline">
                                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                    <input type="hidden" name="action" value="mark_read">
                                                    <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-check"></i> Прочитано
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                            
                                            <form method="POST" class="d-inline">
                                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                <input type="hidden" name="action" value="delete_notification">
                                                <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                        onclick="return confirm('Удалить уведомление?')">
                                                    <i class="fas fa-trash"></i> Удалить
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                    
                                    <div class="notification-type">
                                        <span class="badge bg-<?php echo getNotificationTypeColor($notification['type']); ?>">
                                            <?php echo getNotificationTypeName($notification['type']); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Настройки уведомлений -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog"></i> Настройки уведомлений
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="update_settings">
                        
                        <div class="notification-setting">
                            <div class="setting-info">
                                <h6>Email уведомления</h6>
                                <p class="text-muted">Получать уведомления на email</p>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="email_notifications" 
                                       name="email_notifications" value="1" 
                                       <?php echo $notification_settings['email_notifications'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="email_notifications"></label>
                            </div>
                        </div>
                        
                        <div class="notification-setting">
                            <div class="setting-info">
                                <h6>Push уведомления</h6>
                                <p class="text-muted">Браузерные push-уведомления</p>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="push_notifications" 
                                       name="push_notifications" value="1" 
                                       <?php echo $notification_settings['push_notifications'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="push_notifications"></label>
                            </div>
                        </div>
                        
                        <div class="notification-setting">
                            <div class="setting-info">
                                <h6>Уведомления о прибыли</h6>
                                <p class="text-muted">Ежедневные уведомления о начислении прибыли</p>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="profit_notifications" 
                                       name="profit_notifications" value="1" 
                                       <?php echo $notification_settings['profit_notifications'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="profit_notifications"></label>
                            </div>
                        </div>
                        
                        <div class="notification-setting">
                            <div class="setting-info">
                                <h6>Реферальные уведомления</h6>
                                <p class="text-muted">Уведомления о новых рефералах и бонусах</p>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="referral_notifications" 
                                       name="referral_notifications" value="1" 
                                       <?php echo $notification_settings['referral_notifications'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="referral_notifications"></label>
                            </div>
                        </div>
                        
                        <div class="notification-setting">
                            <div class="setting-info">
                                <h6>Новости и обновления</h6>
                                <p class="text-muted">Уведомления о новостях платформы</p>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="news_notifications" 
                                       name="news_notifications" value="1" 
                                       <?php echo $notification_settings['news_notifications'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="news_notifications"></label>
                            </div>
                        </div>
                        
                        <div class="d-grid mt-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Сохранить настройки
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stats-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.stats-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.notification-item {
    display: flex;
    padding: 1.5rem;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.notification-item:hover {
    background: #f8f9fa;
}

.notification-item.unread {
    background: rgba(0, 123, 255, 0.05);
    border-left: 4px solid var(--primary-color);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.notification-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
    color: var(--dark-color);
}

.notification-time {
    font-size: 0.8rem;
    color: #6c757d;
    white-space: nowrap;
}

.notification-message {
    color: #6c757d;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
}

.notification-type {
    flex-shrink: 0;
    margin-left: 1rem;
}

.notification-setting {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.notification-setting:last-child {
    border-bottom: none;
}

.setting-info h6 {
    margin: 0 0 0.25rem 0;
    font-weight: 600;
}

.setting-info p {
    margin: 0;
    font-size: 0.9rem;
}

.form-switch .form-check-input {
    width: 3rem;
    height: 1.5rem;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

@media (max-width: 768px) {
    .notification-item {
        flex-direction: column;
        text-align: center;
    }
    
    .notification-icon {
        margin: 0 auto 1rem;
    }
    
    .notification-header {
        flex-direction: column;
        text-align: center;
    }
    
    .notification-time {
        margin-top: 0.5rem;
    }
    
    .notification-actions {
        justify-content: center;
        margin-top: 1rem;
    }
    
    .notification-type {
        margin: 1rem 0 0 0;
        text-align: center;
    }
    
    .notification-setting {
        flex-direction: column;
        text-align: center;
    }
    
    .form-switch {
        margin-top: 1rem;
    }
}
</style>

<?php
/**
 * Получение уведомлений пользователя
 */
function getUserNotifications($user_id) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT * FROM notifications 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 50
    ");
    $stmt->execute([$user_id]);
    
    return $stmt->fetchAll();
}

/**
 * Получение статистики уведомлений
 */
function getNotificationStats($user_id) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread,
            COUNT(CASE WHEN is_read = 1 THEN 1 END) as read,
            COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today
        FROM notifications 
        WHERE user_id = ?
    ");
    $stmt->execute([$user_id]);
    
    return $stmt->fetch();
}

/**
 * Получение настроек уведомлений
 */
function getNotificationSettings($user_id) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT * FROM user_notification_settings 
        WHERE user_id = ?
    ");
    $stmt->execute([$user_id]);
    $settings = $stmt->fetch();
    
    // Если настроек нет, создаем с дефолтными значениями
    if (!$settings) {
        $stmt = $conn->prepare("
            INSERT INTO user_notification_settings 
            (user_id, email_notifications, push_notifications, profit_notifications, referral_notifications, news_notifications) 
            VALUES (?, 1, 1, 1, 1, 1)
        ");
        $stmt->execute([$user_id]);
        
        return [
            'email_notifications' => 1,
            'push_notifications' => 1,
            'profit_notifications' => 1,
            'referral_notifications' => 1,
            'news_notifications' => 1
        ];
    }
    
    return $settings;
}

/**
 * Отметить уведомление как прочитанное
 */
function markNotificationAsRead($notification_id, $user_id) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            UPDATE notifications 
            SET is_read = 1, read_at = NOW() 
            WHERE id = ? AND user_id = ?
        ");
        $stmt->execute([$notification_id, $user_id]);
        
        return ['success' => true, 'message' => 'Уведомление отмечено как прочитанное'];
        
    } catch (Exception $e) {
        error_log("Mark notification read error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при обновлении уведомления'];
    }
}

/**
 * Отметить все уведомления как прочитанные
 */
function markAllNotificationsAsRead($user_id) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            UPDATE notifications 
            SET is_read = 1, read_at = NOW() 
            WHERE user_id = ? AND is_read = 0
        ");
        $stmt->execute([$user_id]);
        
        return ['success' => true, 'message' => 'Все уведомления отмечены как прочитанные'];
        
    } catch (Exception $e) {
        error_log("Mark all notifications read error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при обновлении уведомлений'];
    }
}

/**
 * Удаление уведомления
 */
function deleteNotification($notification_id, $user_id) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            DELETE FROM notifications 
            WHERE id = ? AND user_id = ?
        ");
        $stmt->execute([$notification_id, $user_id]);
        
        return ['success' => true, 'message' => 'Уведомление удалено'];
        
    } catch (Exception $e) {
        error_log("Delete notification error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при удалении уведомления'];
    }
}

/**
 * Обновление настроек уведомлений
 */
function updateNotificationSettings($user_id) {
    global $conn;
    
    try {
        $email_notifications = isset($_POST['email_notifications']) ? 1 : 0;
        $push_notifications = isset($_POST['push_notifications']) ? 1 : 0;
        $profit_notifications = isset($_POST['profit_notifications']) ? 1 : 0;
        $referral_notifications = isset($_POST['referral_notifications']) ? 1 : 0;
        $news_notifications = isset($_POST['news_notifications']) ? 1 : 0;
        
        $stmt = $conn->prepare("
            UPDATE user_notification_settings 
            SET email_notifications = ?, push_notifications = ?, profit_notifications = ?, 
                referral_notifications = ?, news_notifications = ?
            WHERE user_id = ?
        ");
        $stmt->execute([
            $email_notifications, $push_notifications, $profit_notifications,
            $referral_notifications, $news_notifications, $user_id
        ]);
        
        return ['success' => true, 'message' => 'Настройки уведомлений обновлены'];
        
    } catch (Exception $e) {
        error_log("Update notification settings error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Ошибка при обновлении настроек'];
    }
}

/**
 * Получение иконки уведомления
 */
function getNotificationIcon($type) {
    $icons = [
        'info' => 'fas fa-info-circle',
        'success' => 'fas fa-check-circle',
        'warning' => 'fas fa-exclamation-triangle',
        'error' => 'fas fa-times-circle',
        'profit' => 'fas fa-coins',
        'investment' => 'fas fa-chart-line',
        'referral' => 'fas fa-users',
        'achievement' => 'fas fa-trophy',
        'news' => 'fas fa-newspaper'
    ];
    
    return $icons[$type] ?? 'fas fa-bell';
}

/**
 * Получение цвета типа уведомления
 */
function getNotificationTypeColor($type) {
    $colors = [
        'info' => 'info',
        'success' => 'success',
        'warning' => 'warning',
        'error' => 'danger',
        'profit' => 'success',
        'investment' => 'primary',
        'referral' => 'info',
        'achievement' => 'warning',
        'news' => 'secondary'
    ];
    
    return $colors[$type] ?? 'secondary';
}

/**
 * Получение названия типа уведомления
 */
function getNotificationTypeName($type) {
    $names = [
        'info' => 'Информация',
        'success' => 'Успех',
        'warning' => 'Предупреждение',
        'error' => 'Ошибка',
        'profit' => 'Прибыль',
        'investment' => 'Инвестиция',
        'referral' => 'Реферал',
        'achievement' => 'Достижение',
        'news' => 'Новости'
    ];
    
    return $names[$type] ?? 'Уведомление';
}
?>
