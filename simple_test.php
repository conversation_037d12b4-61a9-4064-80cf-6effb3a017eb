<?php
// Простой тест функций
require_once 'config/config.php';
require_once 'config/session_config.php';
session_start();
require_once 'includes/functions.php';

echo "<h1>Простой тест</h1>";

// Тест инвестиционных пакетов
echo "<h2>Тест инвестиционных пакетов</h2>";

try {
    $stmt = $conn->query("SELECT * FROM investment_packages WHERE is_active = 1 ORDER BY daily_rate ASC");
    $packages = $stmt->fetchAll();
    
    if (is_array($packages) && count($packages) > 0) {
        echo "✅ Получение инвестиционных пакетов работает (" . count($packages) . " пакетов)<br>";
        foreach ($packages as $package) {
            echo "  - {$package['name']}: {$package['daily_rate']}% в день<br>";
        }
    } else {
        echo "❌ Нет доступных инвестиционных пакетов<br>";
    }
} catch (Exception $e) {
    echo "❌ Ошибка получения пакетов: " . $e->getMessage() . "<br>";
}

// Тест пользователей
echo "<h2>Тест пользователей</h2>";

try {
    $stmt = $conn->query("SELECT id, username, email, balance FROM users LIMIT 5");
    $users = $stmt->fetchAll();
    
    if (is_array($users) && count($users) > 0) {
        echo "✅ Пользователи в базе (" . count($users) . "):<br>";
        foreach ($users as $user) {
            echo "  - {$user['username']} ({$user['email']}) - Баланс: \${$user['balance']}<br>";
        }
    } else {
        echo "❌ Нет пользователей в базе<br>";
    }
} catch (Exception $e) {
    echo "❌ Ошибка получения пользователей: " . $e->getMessage() . "<br>";
}

echo "<p><a href='index.php'>← Вернуться на главную</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #2c3e50; }
h2 { color: #34495e; border-bottom: 2px solid #ecf0f1; padding-bottom: 5px; }
</style>
