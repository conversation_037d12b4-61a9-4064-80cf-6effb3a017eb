// Минимальная тестовая версия main.js для диагностики

console.log('🚀 test-main.js загружается...');

// Глобальные переменные
let isLoggedIn = false;
let currentUser = null;

console.log('📝 Переменные инициализированы');

// Проверка статуса авторизации
function checkAuthStatus() {
    console.log('🔍 checkAuthStatus вызвана');
    const userElement = document.getElementById('user-data') || document.querySelector('[data-user-id]');
    if (userElement) {
        isLoggedIn = true;
        currentUser = {
            id: userElement.dataset.userId,
            name: userElement.dataset.userName,
            role: userElement.dataset.userRole
        };
        console.log('✅ User authenticated:', currentUser);
    } else {
        isLoggedIn = false;
        currentUser = null;
        console.log('❌ User not authenticated');
    }
}

// Обработка кликов по кнопкам инвестирования
function handleInvestClick(e) {
    console.log('🎯 handleInvestClick вызвана');
    e.preventDefault();
    const packageId = e.target.dataset.packageId;
    const packageType = e.target.dataset.packageType;
    
    console.log('📦 Данные пакета:', {
        packageId: packageId,
        packageType: packageType,
        isLoggedIn: isLoggedIn,
        currentUser: currentUser
    });
    
    if (!isLoggedIn) {
        console.log('🚫 Пользователь не авторизован');
        showLoginModal();
        return;
    }
    
    console.log('✅ Пользователь авторизован, вызываем showInvestModal');
    showInvestModal(packageId, packageType);
}

// Показ модального окна входа
function showLoginModal() {
    console.log('🔐 showLoginModal вызвана');
    alert('Для выполнения этого действия необходимо войти в систему.');
}

// Показ модального окна инвестирования
function showInvestModal(packageId, packageType) {
    console.log('💰 showInvestModal вызвана с параметрами:', {
        packageId: packageId,
        packageType: packageType
    });
    
    if (packageId) {
        console.log('📊 Есть packageId, можно загрузить данные пакета');
        alert(`Инвестирование в пакет ID: ${packageId}, тип: ${packageType}`);
    } else {
        console.log('🔄 Нет packageId, перенаправляем на страницу инвестиций');
        let investUrl = 'index.php?page=invest';
        if (packageType) {
            investUrl += `&package_type=${packageType}`;
        }
        console.log('🔗 Переход на:', investUrl);
        alert(`Переход на страницу инвестиций: ${investUrl}`);
    }
}

// Инициализация при загрузке DOM
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎬 DOM загружен, начинаем инициализацию');
    
    // Проверяем статус авторизации
    checkAuthStatus();
    
    // Инициализация обработчиков событий
    console.log('🎯 Добавляем обработчики событий');
    document.addEventListener('click', function(e) {
        if (e.target.matches('.invest-btn')) {
            console.log('🔥 Клик по .invest-btn перехвачен!');
            handleInvestClick(e);
        }
    });
    
    console.log('✅ Инициализация завершена');
});

// Экспорт функций в глобальную область
console.log('🌐 Экспортируем функции в window...');
window.handleInvestClick = handleInvestClick;
window.showInvestModal = showInvestModal;
window.checkAuthStatus = checkAuthStatus;
window.isLoggedIn = isLoggedIn;
window.currentUser = currentUser;

console.log('✅ test-main.js полностью загружен');
