/* ===== ЧЕТКОЕ РАЗДЕЛЕНИЕ И ВЫДЕЛЕНИЕ СЕКЦИЙ ===== */

/* ===== БАЗОВЫЕ СТИЛИ ДЛЯ ВСЕХ СЕКЦИЙ ===== */

section {
    position: relative !important;
    margin: 0 !important;
    padding: 5rem 0 !important;
    border-radius: 0 !important;
    overflow: hidden !important;
    /* Четкие границы между секциями */
    border-bottom: 2px solid rgba(255, 255, 255, 0.1) !important;
    /* Дополнительные отступы */
    margin-bottom: 0 !important;
}

/* Убираем границу у последней секции */
section:last-child {
    border-bottom: none !important;
}

/* ===== ВИЗУАЛЬНЫЕ РАЗДЕЛИТЕЛИ ===== */

/* Декоративная линия между секциями */
section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 1px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.3) 20%, 
        rgba(22, 132, 74, 0.5) 50%, 
        rgba(255, 255, 255, 0.3) 80%, 
        transparent 100%);
    z-index: 1;
}

/* Убираем разделитель для первой секции */
section:first-child::before,
.modern-hero::before {
    display: none !important;
}

/* ===== АЛЬТЕРНАТИВНЫЕ ФОНЫ ДЛЯ СЕКЦИЙ ===== */

/* Hero секция - особый стиль */
.modern-hero {
    background: transparent !important;
    border-bottom: 3px solid rgba(22, 132, 74, 0.3) !important;
    padding: 6rem 0 4rem !important;
    position: relative;
}

.modern-hero::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(180deg, 
        transparent 0%, 
        rgba(22, 132, 74, 0.05) 100%);
    pointer-events: none;
}

/* Четные секции - светлый фон */
section:nth-child(even) {
    background: rgba(255, 255, 255, 0.03) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.05) !important;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

/* Нечетные секции - темный фон */
section:nth-child(odd) {
    background: rgba(0, 0, 0, 0.03) !important;
    border-top: 1px solid rgba(0, 0, 0, 0.05) !important;
    box-shadow: inset 0 1px 0 rgba(0, 0, 0, 0.1) !important;
}

/* ===== СПЕЦИАЛЬНЫЕ ТИПЫ СЕКЦИЙ ===== */

/* Зеленые секции */
.section-green-bg,
.content-section-modern.section-green-bg {
    background: linear-gradient(135deg, 
        rgba(22, 132, 74, 0.08) 0%, 
        rgba(16, 185, 129, 0.05) 50%, 
        rgba(34, 197, 94, 0.03) 100%) !important;
    border-top: 2px solid rgba(22, 132, 74, 0.2) !important;
    border-bottom: 2px solid rgba(22, 132, 74, 0.2) !important;
    box-shadow: 
        inset 0 1px 0 rgba(22, 132, 74, 0.1),
        0 0 50px rgba(22, 132, 74, 0.1) !important;
}

/* Темные секции */
.section-dark-bg {
    background: linear-gradient(135deg, 
        rgba(0, 0, 0, 0.1) 0%, 
        rgba(15, 36, 25, 0.08) 50%, 
        rgba(26, 61, 46, 0.05) 100%) !important;
    border-top: 2px solid rgba(255, 255, 255, 0.1) !important;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.05),
        0 0 50px rgba(0, 0, 0, 0.1) !important;
}

/* Акцентные секции */
.section-accent-bg {
    background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.05) 0%, 
        rgba(99, 102, 241, 0.03) 50%, 
        rgba(139, 92, 246, 0.02) 100%) !important;
    border-top: 2px solid rgba(59, 130, 246, 0.2) !important;
    border-bottom: 2px solid rgba(59, 130, 246, 0.2) !important;
}

/* ===== ВИЗУАЛЬНАЯ ИЕРАРХИЯ ===== */

/* Важные секции - увеличенные отступы и тени */
.section-important {
    padding: 6rem 0 !important;
    box-shadow: 
        0 0 100px rgba(22, 132, 74, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    border-top: 3px solid rgba(22, 132, 74, 0.3) !important;
    border-bottom: 3px solid rgba(22, 132, 74, 0.3) !important;
}

/* Вторичные секции - стандартные отступы */
.section-secondary {
    padding: 4rem 0 !important;
    opacity: 0.95;
}

/* Менее важные секции - уменьшенные отступы */
.section-minor {
    padding: 3rem 0 !important;
    opacity: 0.9;
}

/* ===== КОНТЕЙНЕРЫ С ЧЕТКИМИ ГРАНИЦАМИ ===== */

.container {
    position: relative !important;
    z-index: 2 !important;
}

/* Дополнительные контейнеры для выделения */
.section-container-highlighted {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 8px !important;
    padding: 3rem !important;
    margin: 2rem 0 !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
}

/* ===== ДЕКОРАТИВНЫЕ ЭЛЕМЕНТЫ ===== */

/* Боковые декоративные линии */
section::after {
    content: '';
    position: absolute;
    top: 20%;
    bottom: 20%;
    left: 0;
    width: 4px;
    background: linear-gradient(180deg, 
        transparent 0%, 
        rgba(22, 132, 74, 0.3) 20%, 
        rgba(22, 132, 74, 0.5) 50%, 
        rgba(22, 132, 74, 0.3) 80%, 
        transparent 100%);
    z-index: 1;
}

/* Правая декоративная линия */
section.section-with-right-line::after {
    left: auto;
    right: 0;
}

/* Убираем декоративные линии для hero */
.modern-hero::after {
    display: none !important;
}

/* ===== АНИМАЦИИ ПОЯВЛЕНИЯ ===== */

section {
    animation: sectionFadeIn 0.8s ease-out !important;
}

@keyframes sectionFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Задержка анимации для разных секций */
section:nth-child(1) { animation-delay: 0s; }
section:nth-child(2) { animation-delay: 0.1s; }
section:nth-child(3) { animation-delay: 0.2s; }
section:nth-child(4) { animation-delay: 0.3s; }
section:nth-child(5) { animation-delay: 0.4s; }

/* ===== АДАПТИВНОСТЬ ===== */

@media (max-width: 768px) {
    section {
        padding: 3rem 0 !important;
    }
    
    .section-important {
        padding: 4rem 0 !important;
    }
    
    .section-minor {
        padding: 2rem 0 !important;
    }
    
    .section-container-highlighted {
        padding: 2rem !important;
        margin: 1rem 0 !important;
    }
    
    /* Упрощенные разделители на мобильных */
    section::before {
        width: 90%;
    }
    
    section::after {
        display: none;
    }
}

@media (max-width: 576px) {
    section {
        padding: 2rem 0 !important;
    }
    
    .modern-hero {
        padding: 5rem 0 3rem !important;
    }
    
    .section-important {
        padding: 3rem 0 !important;
    }
}

/* ===== СПЕЦИАЛЬНЫЕ ЭФФЕКТЫ ===== */

/* Эффект параллакса для фоновых элементов */
.section-parallax {
    background-attachment: fixed !important;
}

/* Эффект размытия для фона */
.section-blur-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    backdrop-filter: blur(2px) !important;
    z-index: 0;
}

/* Градиентные переходы между секциями */
.section-gradient-transition::before {
    height: 3px;
    background: linear-gradient(90deg, 
        #16844a 0%, 
        #22c55e 25%, 
        #10b981 50%, 
        #22c55e 75%, 
        #16844a 100%);
    animation: gradientFlow 3s ease-in-out infinite;
}

@keyframes gradientFlow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}
