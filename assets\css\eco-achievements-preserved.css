/* ===== СОХРАНЕННЫЕ СТИЛИ СЕКЦИИ "OUR ECO ACHIEVEMENTS" ===== */

/* Основная секция */
.content-section-modern.section-green-bg {
    background: transparent !important;
    position: relative;
    padding: 5rem 0 !important;
    margin: 2rem 0 !important;
    border-radius: 1rem !important;
}

.content-section-modern .container {
    position: relative;
    z-index: 2;
}

/* Заголовки секции */
.section-title-modern {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
    color: #1f2937;
}

.section-title-eco {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle-modern {
    font-size: 1.1rem;
    color: #374151;
    text-align: center;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Сет<PERSON>а карточек */
.features-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

/* Карточки функций */
.feature-card-modern {
    background: #ffffff;
    border: 1px solid #ecfccb;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.feature-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card-modern:hover::before {
    transform: scaleX(1);
}

.feature-card-modern:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(16, 185, 129, 0.15);
    border-color: #10b981;
}

/* Иконки карточек */
.feature-icon-modern {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    transition: all 0.3s ease;
}

.feature-card-modern:hover .feature-icon-modern {
    transform: scale(1.1) rotate(360deg);
}

/* Заголовки карточек */
.feature-title-modern {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

/* Описания карточек */
.feature-description-modern {
    color: #374151;
    line-height: 1.6;
}

/* Статистические числа */
.stat-number-modern {
    font-size: 2rem;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Цветовые классы для текста */
.text-eco-forest {
    color: #059669 !important;
}

.text-eco-gold {
    color: #f59e0b !important;
}

.text-eco-emerald {
    color: #34d399 !important;
}

.text-eco-lime {
    color: #84cc16 !important;
}

.text-eco-bright {
    color: #10b981 !important;
}

/* Адаптивность для секции */
@media (max-width: 767.98px) {
    .content-section-modern {
        padding: 3rem 0;
    }
    
    .section-title-modern {
        font-size: 2rem;
    }
    
    .features-grid-modern {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .feature-card-modern {
        padding: 1.5rem;
    }
    
    .feature-icon-modern {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

@media (max-width: 575.98px) {
    .feature-card-modern {
        padding: 1.5rem;
    }
    
    .feature-icon-modern {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}
