<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест центрирования - <PERSON><PERSON><PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/desktop-centering.css">
    <style>
        /* Визуальные индикаторы для тестирования */
        .test-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            z-index: 9999;
        }
        
        .container-outline {
            outline: 2px dashed rgba(255, 0, 0, 0.5) !important;
            position: relative;
        }
        
        .container-outline::before {
            content: 'Container';
            position: absolute;
            top: -20px;
            left: 0;
            background: red;
            color: white;
            padding: 2px 6px;
            font-size: 12px;
            border-radius: 3px;
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 2rem;
            background: linear-gradient(135deg, #1a3d2e, #2d5a3d);
            border-radius: 15px;
            border: 1px solid #d4af37;
        }
        
        .test-card {
            background: rgba(26, 61, 46, 0.8);
            border: 1px solid #d4af37;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Индикатор разрешения экрана -->
    <div class="test-indicator">
        <div>Ширина: <span id="screenWidth"></span>px</div>
        <div>Высота: <span id="screenHeight"></span>px</div>
        <div>Статус: <span id="centeringStatus"></span></div>
    </div>

    <!-- Тест главной страницы -->
    <section class="modern-hero">
        <div class="container container-outline">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="test-card">
                        <h1 style="color: #d4af37;">Тест центрирования</h1>
                        <p style="color: #e5e5e5;">Этот контейнер должен быть центрирован на экранах 1200px и выше</p>
                        <div class="hero-stats-modern">
                            <div class="stat-card-modern">
                                <div class="stat-number-modern">1200px+</div>
                                <div class="stat-label-modern">Центрирование</div>
                            </div>
                            <div class="stat-card-modern">
                                <div class="stat-number-modern">100%</div>
                                <div class="stat-label-modern">Совместимость</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="test-card">
                        <h3 style="color: #d4af37;">Проверка адаптивности</h3>
                        <ul style="color: #e5e5e5;">
                            <li>< 1200px: Полная ширина</li>
                            <li>1200-1399px: Центрирование 1200px</li>
                            <li>1400-1599px: Центрирование 1400px</li>
                            <li>1600px+: Центрирование 1600px</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Тест дашборда -->
    <div class="modern-dashboard">
        <div class="container-fluid container-outline">
            <div class="test-section">
                <h2 style="color: #d4af37; text-align: center;">Тест дашборда</h2>
                <div class="stats-grid-modern">
                    <div class="test-card">
                        <h4 style="color: #d4af37;">Статистика 1</h4>
                        <p>Контент дашборда должен быть центрирован</p>
                    </div>
                    <div class="test-card">
                        <h4 style="color: #d4af37;">Статистика 2</h4>
                        <p>Сохраняя все существующие стили</p>
                    </div>
                    <div class="test-card">
                        <h4 style="color: #d4af37;">Статистика 3</h4>
                        <p>И адаптивность для мобильных</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Тест обычной страницы -->
    <div class="container container-outline">
        <div class="test-section">
            <h2 style="color: #d4af37; text-align: center;">Тест обычной страницы</h2>
            <div class="row">
                <div class="col-lg-4">
                    <div class="test-card">
                        <h4 style="color: #d4af37;">Колонка 1</h4>
                        <p>Контент в колонках должен правильно размещаться в центрированном контейнере</p>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="test-card">
                        <h4 style="color: #d4af37;">Колонка 2</h4>
                        <p>Все цвета, шрифты и анимации должны остаться неизменными</p>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="test-card">
                        <h4 style="color: #d4af37;">Колонка 3</h4>
                        <p>Адаптивность для мобильных устройств сохраняется</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Тест форм -->
    <div class="container container-outline">
        <div class="test-section">
            <h2 style="color: #d4af37; text-align: center;">Тест форм</h2>
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="test-card">
                        <h4 style="color: #d4af37;">Тестовая форма</h4>
                        <form>
                            <div class="mb-3">
                                <label class="form-label" style="color: white;">Тестовое поле</label>
                                <input type="text" class="form-control" placeholder="Введите текст">
                            </div>
                            <div class="mb-3">
                                <label class="form-label" style="color: white;">Выбор опции</label>
                                <select class="form-select">
                                    <option>Опция 1</option>
                                    <option>Опция 2</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">Отправить</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Тест таблиц -->
    <div class="container container-outline">
        <div class="test-section">
            <h2 style="color: #d4af37; text-align: center;">Тест таблиц</h2>
            <div class="table-responsive">
                <table class="table table-dark table-striped">
                    <thead>
                        <tr>
                            <th>Разрешение</th>
                            <th>Максимальная ширина</th>
                            <th>Отступы</th>
                            <th>Статус</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>< 1200px</td>
                            <td>100%</td>
                            <td>Стандартные</td>
                            <td><span class="badge bg-success">Полная ширина</span></td>
                        </tr>
                        <tr>
                            <td>1200-1399px</td>
                            <td>1200px</td>
                            <td>2rem</td>
                            <td><span class="badge bg-primary">Центрировано</span></td>
                        </tr>
                        <tr>
                            <td>1400-1599px</td>
                            <td>1400px</td>
                            <td>2.5rem</td>
                            <td><span class="badge bg-primary">Центрировано</span></td>
                        </tr>
                        <tr>
                            <td>1600px+</td>
                            <td>1600px</td>
                            <td>3rem</td>
                            <td><span class="badge bg-primary">Центрировано</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Обновление индикатора разрешения
        function updateScreenInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            
            document.getElementById('screenWidth').textContent = width;
            document.getElementById('screenHeight').textContent = height;
            
            let status = 'Полная ширина';
            if (width >= 1600) {
                status = 'Центрировано 1600px';
            } else if (width >= 1400) {
                status = 'Центрировано 1400px';
            } else if (width >= 1200) {
                status = 'Центрировано 1200px';
            }
            
            document.getElementById('centeringStatus').textContent = status;
        }
        
        // Обновляем при загрузке и изменении размера
        window.addEventListener('load', updateScreenInfo);
        window.addEventListener('resize', updateScreenInfo);
        
        // Переключение контуров для отладки
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F2') {
                document.querySelectorAll('.container-outline').forEach(el => {
                    el.style.outline = el.style.outline ? '' : '2px dashed rgba(255, 0, 0, 0.5)';
                });
            }
        });
    </script>
</body>
</html>
