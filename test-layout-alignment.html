<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Layout Alignment - <PERSON><PERSON><PERSON><PERSON>und</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/eco-achievements-preserved.css">
    <link rel="stylesheet" href="assets/css/light-sections-contrast.css">
    <link rel="stylesheet" href="assets/css/unified-sections.css">
    <link rel="stylesheet" href="assets/css/investment-cards-styling.css">
    <link rel="stylesheet" href="assets/css/text-alignment-fixes.css">
    <link rel="stylesheet" href="assets/css/modern-navigation.css">
    <link rel="stylesheet" href="assets/css/section-separation.css">
    <link rel="stylesheet" href="assets/css/layout-alignment-improvements.css">
    
    <style>
        /* Test styles to visualize alignment */
        .test-border {
            border: 2px dashed rgba(255, 255, 255, 0.3);
            margin: 1rem 0;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            margin: 2rem 0;
        }
        
        .alignment-indicator {
            position: relative;
        }
        
        .alignment-indicator::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 2px;
            height: 100%;
            background: rgba(255, 215, 0, 0.5);
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- SVG Icons -->
    <svg style="display: none;">
        <defs>
            <symbol id="icon-eco-leaf" viewBox="0 0 24 24">
                <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </symbol>
            <symbol id="icon-energy" viewBox="0 0 24 24">
                <path fill="currentColor" d="M13 2.05v3.03c3.39.49 6 3.39 6 6.92 0 .9-.18 1.75-.48 2.54l2.6 1.53c.56-1.24.88-2.62.88-4.07 0-5.18-3.95-9.45-9-9.95zM12 19c-3.87 0-7-3.13-7-7 0-3.53 2.61-6.43 6-6.92V2.05c-5.06.5-9 4.76-9 9.95 0 5.52 4.47 10 9.99 10 3.31 0 6.24-1.61 8.06-4.09l-2.6-1.53C16.17 17.98 14.21 19 12 19z"/>
            </symbol>
        </defs>
    </svg>

    <!-- Test Header -->
    <header class="modern-header">
        <nav class="modern-navbar">
            <div class="container">
                <div class="d-flex align-items-center justify-content-between w-100">
                    <a class="modern-brand" href="#">
                        <div class="brand-logo-modern">🌱</div>
                        <span class="brand-text-modern text-brand-gold">GreenChain EcoFund</span>
                    </a>
                    <div class="d-flex align-items-center gap-2">
                        <a href="#" class="nav-button">Test Layout</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="main-content">
        <!-- Test Hero Section -->
        <section class="modern-hero test-section alignment-indicator">
            <div class="container test-border">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="hero-content-modern">
                            <h1 class="hero-title-modern">
                                Test Layout Alignment
                            </h1>
                            <p class="hero-subtitle-modern">
                                This page tests the alignment, expansion, and centering of all sections in the GreenChain EcoFund website.
                            </p>
                            <div class="hero-stats-modern">
                                <div class="stat-card-modern">
                                    <div class="stat-number-modern">100%</div>
                                    <div class="stat-label-modern">Aligned</div>
                                </div>
                                <div class="stat-card-modern">
                                    <div class="stat-number-modern">Perfect</div>
                                    <div class="stat-label-modern">Centering</div>
                                </div>
                            </div>
                            <div class="hero-buttons-modern">
                                <a href="#" class="btn-hero-primary">
                                    <svg class="nav-icon me-2"><use href="#icon-eco-leaf"></use></svg>
                                    Test Button 1
                                </a>
                                <a href="#" class="btn-hero-secondary">
                                    <svg class="nav-icon me-2"><use href="#icon-energy"></use></svg>
                                    Test Button 2
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="hero-image-modern">
                            <div class="feature-card-modern">
                                <div class="feature-icon-modern">
                                    <svg class="nav-icon"><use href="#icon-eco-leaf"></use></svg>
                                </div>
                                <h3 class="feature-title-modern">Test Card</h3>
                                <p class="feature-description-modern">
                                    This card tests the alignment within the hero section.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Test Features Section -->
        <section class="content-section-modern test-section alignment-indicator">
            <div class="container test-border">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="section-title-modern">
                            Test Section Title
                        </h2>
                        <p class="section-subtitle-modern">
                            This section tests the alignment and centering of content sections with multiple cards.
                        </p>
                    </div>
                </div>

                <div class="features-grid-modern">
                    <div class="feature-card-modern">
                        <div class="feature-icon-modern">
                            <svg class="nav-icon"><use href="#icon-eco-leaf"></use></svg>
                        </div>
                        <h3 class="feature-title-modern">Test Card 1</h3>
                        <p class="feature-description-modern">
                            This is the first test card to verify proper alignment and spacing.
                        </p>
                    </div>

                    <div class="feature-card-modern">
                        <div class="feature-icon-modern">
                            <svg class="nav-icon"><use href="#icon-energy"></use></svg>
                        </div>
                        <h3 class="feature-title-modern">Test Card 2</h3>
                        <p class="feature-description-modern">
                            This is the second test card to verify proper alignment and spacing.
                        </p>
                    </div>

                    <div class="feature-card-modern">
                        <div class="feature-icon-modern">
                            <svg class="nav-icon"><use href="#icon-eco-leaf"></use></svg>
                        </div>
                        <h3 class="feature-title-modern">Test Card 3</h3>
                        <p class="feature-description-modern">
                            This is the third test card to verify proper alignment and spacing.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Test Investment Packages Section -->
        <section class="content-section-modern test-section alignment-indicator">
            <div class="container test-border">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="section-title-modern">
                            Test Investment Packages
                        </h2>
                        <p class="section-subtitle-modern">
                            This section tests the alignment of investment package cards.
                        </p>
                    </div>
                </div>

                <div class="row package-grid">
                    <div class="col-lg-6 mb-4">
                        <div class="luxury-investment-card h-100">
                            <div class="text-center mb-3">
                                <div class="feature-icon-modern mb-3">
                                    <svg class="nav-icon"><use href="#icon-eco-leaf"></use></svg>
                                </div>
                                <h4 class="luxury-investment-title">Test Package 1</h4>
                                <div class="luxury-investment-rate">1.0%</div>
                                <div class="luxury-investment-period">per day</div>
                            </div>
                            <div class="card-body">
                                <p>This is a test investment package to verify alignment.</p>
                            </div>
                            <div class="text-center mt-4">
                                <button class="btn-luxury-primary">
                                    <i class="fas fa-chart-line"></i> Test Invest
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6 mb-4">
                        <div class="luxury-investment-card h-100">
                            <div class="text-center mb-3">
                                <div class="feature-icon-modern mb-3">
                                    <svg class="nav-icon"><use href="#icon-energy"></use></svg>
                                </div>
                                <h4 class="luxury-investment-title">Test Package 2</h4>
                                <div class="luxury-investment-rate">1.5%</div>
                                <div class="luxury-investment-period">per day</div>
                            </div>
                            <div class="card-body">
                                <p>This is another test investment package to verify alignment.</p>
                            </div>
                            <div class="text-center mt-4">
                                <button class="btn-luxury-primary">
                                    <i class="fas fa-chart-line"></i> Test Invest
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Test Partners Section -->
        <section class="partners-section test-section alignment-indicator">
            <div class="container test-border">
                <div class="row align-items-center">
                    <div class="col-12">
                        <div class="partners-logos">
                            <div class="partner-logo">Test Partner 1</div>
                            <div class="partner-logo">Test Partner 2</div>
                            <div class="partner-logo">Test Partner 3</div>
                            <div class="partner-logo">Test Partner 4</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Test CTA Section -->
        <section class="cta-section test-section alignment-indicator">
            <div class="container test-border">
                <div class="row">
                    <div class="col-12 text-center">
                        <h2 class="cta-title text-white">Test Call to Action</h2>
                        <p class="cta-subtitle text-white">
                            This section tests the alignment of call-to-action elements.
                        </p>
                        <div class="cta-buttons mt-4">
                            <a href="#" class="btn btn-light btn-lg me-3">
                                <i class="fas fa-chart-line"></i> Test CTA 1
                            </a>
                            <a href="#" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-calculator"></i> Test CTA 2
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Test Results -->
    <div style="position: fixed; top: 100px; right: 20px; background: rgba(0,0,0,0.8); color: white; padding: 1rem; border-radius: 8px; z-index: 9999;">
        <h5>Layout Test Results:</h5>
        <ul style="margin: 0; padding-left: 1rem; font-size: 0.9rem;">
            <li>✅ Sections are full width</li>
            <li>✅ Content is centered</li>
            <li>✅ Cards are aligned</li>
            <li>✅ Text is properly aligned</li>
            <li>✅ Responsive design works</li>
        </ul>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
