/* ===== ПОЛНОЕ ЦЕНТРИРОВАНИЕ САЙТА GREENCHAIN ECOFUND ===== */

/* ===== ГЛОБАЛЬНЫЕ ПРАВИЛА ЦЕНТРИРОВАНИЯ ===== */

/* Основные контейнеры - полное центрирование */
.container,
.container-fluid,
.container-xxl,
.container-xl,
.container-lg,
.container-md,
.container-sm {
    margin-left: auto !important;
    margin-right: auto !important;
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Все секции - центрирование */
section,
.content-section,
.content-section-modern,
.modern-hero,
.features-section,
.how-it-works-section,
.partners-section,
.cta-section {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
}

/* Все строки Bootstrap - центрирование */
.row {
    margin-left: auto !important;
    margin-right: auto !important;
    text-align: center !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    flex-wrap: wrap !important;
}

/* Все колонки Bootstrap - центрирование */
[class*="col-"] {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Заголовки секций - всегда по центру */
h1, h2,
.hero-title,
.hero-title-modern,
.section-title,
.section-title-modern,
.section-title-eco {
    text-align: center !important;
    width: 100% !important;
    display: block !important;
    margin-left: auto !important;
    margin-right: auto !important;
    /* Правильные межстрочные интервалы */
    line-height: 1.2 !important;
    margin-bottom: 1.5rem !important;
}

/* Подзаголовки секций - по центру */
.hero-subtitle,
.hero-subtitle-modern,
.section-subtitle,
.section-subtitle-modern {
    text-align: center !important;
    width: 100% !important;
    display: block !important;
    margin-left: auto !important;
    margin-right: auto !important;
    max-width: 700px !important;
    /* Правильные межстрочные интервалы */
    line-height: 1.6 !important;
    margin-bottom: 2rem !important;
}

/* Все карточки - центрирование */
.feature-card-modern,
.investment-card,
.luxury-investment-card,
.feature-card,
.step-card,
.card {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

/* Заголовки в карточках - по центру */
h3, h4, h5, h6,
.feature-title,
.feature-title-modern,
.investment-title,
.luxury-investment-title,
.step-title,
.card-title {
    text-align: center !important;
    width: 100% !important;
    /* Правильные межстрочные интервалы */
    line-height: 1.3 !important;
    margin-bottom: 1rem !important;
}

/* Обычный текст в карточках - по центру */
p,
.feature-description,
.feature-description-modern,
.investment-description,
.step-description,
.card-text {
    text-align: center !important;
    width: 100% !important;
    /* Правильные межстрочные интервалы */
    line-height: 1.6 !important;
    margin-bottom: 1rem !important;
}

/* Списки - по центру */
ul, ol, li {
    text-align: center !important;
    line-height: 1.6 !important;
    list-style-position: inside !important;
}

/* Все сетки и грид-контейнеры - центрирование */
.features-grid-modern,
.investment-grid,
.steps-grid,
.grid-container {
    display: grid !important;
    justify-content: center !important;
    align-items: center !important;
    justify-items: center !important;
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

/* ===== СПЕЦИАЛЬНЫЕ СЛУЧАИ - ПОЛНОЕ ЦЕНТРИРОВАНИЕ ===== */

/* Центрированный текст в статистике */
.stat-number,
.stat-number-modern,
.stat-label,
.stat-label-modern,
.hero-stats-modern .stat-card-modern {
    text-align: center !important;
}

/* Все кнопки - центрирование */
.hero-buttons,
.hero-buttons-modern,
.text-center .btn,
.btn-container,
.btn,
.btn-primary,
.btn-secondary,
.btn-hero-primary,
.btn-hero-secondary {
    text-align: center !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

/* Контейнеры кнопок */
.hero-buttons,
.hero-buttons-modern,
.btn-container {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 1rem !important;
    flex-wrap: wrap !important;
    width: 100% !important;
}

/* ===== HERO СЕКЦИЯ - ПОЛНОЕ ЦЕНТРИРОВАНИЕ ===== */

/* Hero секция */
.modern-hero .hero-content-modern {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

.modern-hero .hero-title-modern {
    text-align: center !important;
    margin-bottom: 1.5rem !important;
    width: 100% !important;
}

.modern-hero .hero-subtitle-modern {
    text-align: center !important;
    margin-bottom: 2rem !important;
    width: 100% !important;
    max-width: 600px !important;
}

/* Статистика в hero - центрирование */
.hero-stats-modern {
    display: flex !important;
    justify-content: center !important;
    gap: 2rem !important;
    margin: 2rem 0 !important;
    flex-wrap: wrap !important;
    width: 100% !important;
}

.hero-stats-modern .stat-card-modern {
    text-align: center !important;
    min-width: 120px !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Кнопки в hero - центрирование */
.hero-buttons-modern {
    display: flex !important;
    justify-content: center !important;
    gap: 1rem !important;
    margin-top: 2rem !important;
    flex-wrap: wrap !important;
    width: 100% !important;
}

/* ===== КАРТОЧКИ И СЕТКИ - ПОЛНОЕ ЦЕНТРИРОВАНИЕ ===== */

/* Контейнеры карточек */
.features-grid-modern,
.row {
    display: grid !important;
    align-items: center !important;
    justify-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

/* Внутреннее содержимое карточек - центрирование */
.feature-card-modern,
.investment-card,
.luxury-investment-card,
.feature-card,
.step-card {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

/* Иконки в карточках - по центру */
.feature-icon-modern,
.feature-icon,
.step-icon,
.icon {
    text-align: center !important;
    margin: 0 auto 1.5rem !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

/* ===== НАВИГАЦИЯ - ЦЕНТРИРОВАНИЕ ===== */

/* Header и навигация */
.modern-header,
.modern-navbar {
    text-align: center !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

/* Контейнер навигации */
.modern-navbar .container {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    width: 100% !important;
}

/* Элементы навигации */
.nav-button,
.navbar-nav .nav-link,
.modern-brand {
    text-align: center !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

/* Информация о пользователе - центрирование */
.user-info {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

.user-name,
.user-balance {
    text-align: center !important;
    line-height: 1.2 !important;
}

/* ===== ФУТЕР - ЦЕНТРИРОВАНИЕ ===== */

/* Весь футер */
footer {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Контейнер футера */
footer .container {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Колонки футера */
.footer-column {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

.footer-column h5 {
    text-align: center !important;
    margin-bottom: 1rem !important;
}

.footer-column ul,
.footer-column p {
    text-align: center !important;
}

.footer-column ul {
    list-style: none !important;
    padding: 0 !important;
}

.footer-column li {
    text-align: center !important;
}

/* ===== АДАПТИВНЫЕ ИСПРАВЛЕНИЯ - ПОЛНОЕ ЦЕНТРИРОВАНИЕ ===== */

@media (max-width: 768px) {
    /* На мобильных все элементы по центру */
    .modern-hero .hero-title-modern,
    .modern-hero .hero-subtitle-modern,
    .modern-hero .hero-content-modern {
        text-align: center !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* Статистика и кнопки по центру на мобильных */
    .hero-stats-modern,
    .hero-buttons-modern {
        justify-content: center !important;
        align-items: center !important;
        text-align: center !important;
    }

    /* Все контейнеры по центру */
    .container,
    .container-fluid {
        text-align: center !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* Все колонки по центру */
    [class*="col-"] {
        text-align: center !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* Уменьшенные отступы */
    h1, h2 {
        margin-bottom: 1rem !important;
    }

    .section-subtitle,
    .section-subtitle-modern {
        margin-bottom: 1.5rem !important;
    }
}

@media (max-width: 576px) {
    /* Еще более компактные отступы */
    h1, h2 {
        margin-bottom: 0.75rem !important;
    }

    .hero-stats-modern {
        gap: 1rem !important;
        justify-content: center !important;
    }

    .hero-buttons-modern {
        gap: 0.75rem !important;
        justify-content: center !important;
    }

    /* Все элементы строго по центру на маленьких экранах */
    * {
        text-align: center !important;
    }

    /* Исключения для элементов, которые должны быть слева */
    input, textarea, select {
        text-align: left !important;
    }
}

/* ===== ПРИНУДИТЕЛЬНЫЕ ИСПРАВЛЕНИЯ - ПОЛНОЕ ЦЕНТРИРОВАНИЕ ===== */

/* Восстанавливаем правильное выравнивание */
.text-center,
.text-center * {
    text-align: center !important;
}

.text-left,
.text-left * {
    text-align: left !important;
}

.text-right,
.text-right * {
    text-align: right !important;
}

/* ===== ГЛОБАЛЬНОЕ ЦЕНТРИРОВАНИЕ ВСЕХ ЭЛЕМЕНТОВ ===== */

/* Все основные элементы по центру */
main,
.main-content,
article,
aside,
div {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Обеспечиваем правильное выравнивание для всех Bootstrap колонок */
[class*="col-"] {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
}

/* Все центрированные колонки */
.col-12,
.col-lg-12,
.col-md-12,
.col-sm-12,
.col-lg-6,
.col-md-6,
.col-sm-6,
.col-lg-4,
.col-md-4,
.col-sm-4,
.col-lg-3,
.col-md-3,
.col-sm-3 {
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
}

/* Улучшенное выравнивание для flex контейнеров */
.d-flex {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.justify-content-center {
    justify-content: center !important;
}

.align-items-center {
    align-items: center !important;
}

.flex-wrap {
    flex-wrap: wrap !important;
}

/* Все формы и элементы ввода */
.form-group,
.form-control,
.input-group {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

/* Обеспечиваем правильные отступы */
.mb-4 {
    margin-bottom: 2rem !important;
}

.mb-5 {
    margin-bottom: 3rem !important;
}

.mt-4 {
    margin-top: 2rem !important;
}

.gap-2 {
    gap: 0.5rem !important;
}

.gap-3 {
    gap: 1rem !important;
}

.gap-4 {
    gap: 1.5rem !important;
}

/* ===== ФИНАЛЬНЫЕ ПРАВИЛА ЦЕНТРИРОВАНИЯ ===== */

/* Все таблицы */
table,
.table {
    margin-left: auto !important;
    margin-right: auto !important;
    text-align: center !important;
}

/* Все изображения */
img,
.img-fluid {
    display: block !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

/* Все модальные окна */
.modal-content,
.modal-body,
.modal-header,
.modal-footer {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Все алерты и уведомления */
.alert {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

/* Все бейджи и лейблы */
.badge,
.label {
    display: inline-flex !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
}
