<?php
// Включаем буферизацию вывода для предотвращения ошибок headers already sent
ob_start();

// Подключаем настройки сессии ПЕРЕД session_start()
require_once 'config/config.php';
require_once 'config/session_config.php';

// Запускаем сессию после настройки
session_start();

require_once 'includes/functions.php';

// Проверка подключения к базе данных
if (!$conn) {
    die("Ошибка подключения к базе данных");
}

// Определение текущей страницы
$page = isset($_GET['page']) ? $_GET['page'] : 'home';

// Проверка авторизации для защищенных страниц
$protected_pages = ['dashboard', 'invest', 'withdraw', 'profile', 'admin'];
if (in_array($page, $protected_pages) && !isLoggedIn()) {
    $page = 'login';
}

// Проверка прав администратора
if ($page === 'admin' && !isAdmin()) {
    $page = 'dashboard';
}

include 'includes/header.php';

// Маршрутизация страниц
switch ($page) {
    case 'home':
        include 'pages/home.php';
        break;
    case 'login':
        include 'pages/auth/login.php';
        break;
    case 'register':
        include 'pages/auth/register.php';
        break;
    case 'dashboard':
        include 'pages/dashboard.php';
        break;
    case 'invest':
        include 'pages/invest.php';
        break;
    case 'withdraw':
        include 'pages/withdraw.php';
        break;
    case 'profile':
        include 'pages/profile.php';
        break;
    case 'map':
        include 'pages/map.php';
        break;
    case 'calculator':
        include 'pages/calculator.php';
        break;
    case 'education':
        include 'pages/education.php';
        break;
    case 'leaderboard':
        include 'pages/leaderboard.php';
        break;
    case 'referrals':
        include 'pages/referrals.php';
        break;
    case 'tasks':
        include 'pages/tasks.php';
        break;
    case 'admin':
        include 'pages/admin/admin.php';
        break;
    case 'logout':
        include 'pages/auth/logout.php';
        break;
    default:
        include 'pages/404.php';
        break;
}

include 'includes/footer.php';

// Завершаем буферизацию и выводим содержимое
ob_end_flush();
?>
